// tslint:disable:indent array-type
// tslint:disable:no-use-before-declare

  export interface ActionResultApiResponseModel<T> {
    error?: string;
    result: T;
  }
  export interface AddressModel {
    address1?: string;
    address2?: string;
    addressId?: number;
    city?: string;
    countryCode?: string;
    firstName?: string;
    hasRegions?: boolean;
    lastName?: string;
    localAddressId?: number;
    phoneNumber?: string;
    state?: string;
    zipPostalCode?: string;
  }
  export interface ShippingMethodModel {
    amount: number;
    available: boolean;
    baseAmount: number;
    carrierCode: string;
    carrierTitle: string;
    errorMessage: string;
    methodCode: string;
    methodTitle: string;
    priceWithTaxExcluded: number;
    priceWithTaxIncluded: number;
  }
  export interface Amount {
    currencyCode: string;
    value: number;
  }
  export interface PaymentCardModel {
    encryptedPaymentCard?: string;
    paymentCardId?: number;
  }
  export interface PaymentModel {
    address?: AddressModel;
    customerEmail?: string;
    transaction: TransactionModel;
    verifiedCountryCode?: string;
    verifiedRegionCode?: string;
  }
  export interface PaymentResultModel {
    orderNumber: string;
    transactionResult: TransactionResultModel;
  }
  export interface ValidatePONumberInitModel {
    poNumber: string;
  }
  export interface TransactionModel {
    paymentMethodCode: string;
    paymentProperties: PaymentPropertyModel[];
  }
  export interface TransactionResultModel {
    paymentMethodCode: string;
    publicPaymentProperties: PaymentPropertyModel[];
    status: OperationStatuses;
  }
  export interface PaymentPropertyModel {
    key: string;
    value: string;
  }
  export interface CustomDataModel {
    key: string;
    value: string;
  }
  export interface OrderModel {
    adjustments: string[];
    billingAddress: AddressModel;
    cardLast4Digits: string;
    cardType: PaymentCardTypes;
    cartLines: CartLineModel[];
    couponCode: string;
    currencyCode: string;
    currencySymbol: string;
    customData: CustomDataModel[];
    donationCartLine: CartLineModel;
    email: string;
    giftCard: CartGiftCardTotalModel;
    giftRecipientAddress?: AddressModel;
    giftRecipientEmail?: string;
    giftRecipientNote?: string;
    giftRecipientPersonId?: string;
    id: string;
    isCouponApplied: boolean;
    isVirtual: boolean;
    itemsCount: number;
    localCurrencyCode: string;
    localCurrencySymbol: string;
    membershipCartLine: CartLineModel;
    methodSubType: string;
    methodType: string;
    paidWithLocalCurrency?: boolean;
    paymentMethodCode: string;
    paypalUserName: string;
    pdfLink: string;
    personId: number;
    poNumber: string;
    price: CartPriceModel;
    shippingAddress: AddressModel;
    shippingDescription: string;
    status: string;
    storeCurrencyCode: string;
    storeCurrencySymbol: string;
    voucherCode: string;
  }
  export interface BundleOptionModel {
    optionId: number;
    selectedProductType: string;
    selectionId: number;
    selectionName: string;
    selectionSku: string;
  }
  export interface BundleProductOptionSearchResult {
    chapterCity?: string;
    chapterState?: string;
    id: string;
    optionId: number;
    productAdditionalDetails?: string;
    productName: string;
    sku: string;
  }
  export interface CartGiftCardItemModel {
    giftCardCode: string;
    giftCardId: number;
    giftCardValue: number;
  }
  export interface CartGiftCardTotalModel {
    giftCardItems: CartGiftCardItemModel[];
    title: string;
    totalValue: number;
  }
  export interface CartItemDto {
    cartId?: string;
    customOptions?: CartItemOption[];
    giftRecipient?: CartItemGiftRecipient;
    itemId?: string;
    productId?: string;
    quantity?: number;
  }
  export interface CartItemGiftRecipient {
    city: string;
    countryCode: string;
    emailAddress: string;
    firstName: string;
    lastName: string;
    note?: string;
    phoneNumber: string;
    postalCode: string;
    regionCode: string;
    street1?: string;
    street2?: string;
  }
  export interface CartItemOption {
    title: string;
    value: string;
  }
  export interface CartLineModel {
    addedProductLineOptions?: BundleOptionModel[];
    disabledProductURL: boolean;
    displayName: string;
    fulfillmentProvider: string;
    giftRecipient: CartItemGiftRecipient;
    id: string;
    price: CartLinePriceModel;
    quantity: number;
    quantityOrdered: number;
    selectedBundleOptions: BundleOptionModel[];
    sku: string;
  }
  export interface CartLinePriceModel {
    baseDiscountAmount: number;
    basePrice: number;
    basePriceInclTax: number;
    baseRowTotal: number;
    baseRowTotalInclTax: number;
    baseTaxAmount: number;
    discountAmount: number;
    discountPercent: number;
    discountPercentage: number;
    orderRowTotal: number;
    price: number;
    priceInclTax: number;
    productBasePrice: number;
    rowTotal: number;
    rowTotalInclTax: number;
    rowTotalWithDiscount: number;
    taxAmount: number;
    taxPercent: number;
    totalProductDiscountAmount: number;
    totalRowDiscountAmount: number;
    weeeTaxApplied: string;
    weeeTaxAppliedAmount: number;
  }
  export interface CartModel {
    adjustments: string[];
    billingAddress: AddressModel;
    cacheHit?: boolean;
    cartLines: CartLineModel[];
    couponCode: string;
    customData?: ProductOptionModel[];
    donationCartLine: CartLineModel;
    email: string;
    giftCard: CartGiftCardTotalModel;
    giftRecipientAddress?: AddressModel;
    giftRecipientEmail?: string;
    giftRecipientNote?: string;
    id: string;
    isCouponApplied: boolean;
    isGiftCardApplied: boolean;
    isVirtual: boolean;
    itemsCount: number;
    itemsQuantity: number;
    localCurrencyCode: string;
    localCurrencySymbol: string;
    lockStatus: number;
    membershipCartLine: CartLineModel;
    orderData?: CartOrderDataModel;
    poNumber: string;
    price: CartPriceModel;
    reservedOrderId?: string;
    shippingAddress?: AddressModel;
    storeCurrencyCode: string;
    storeCurrencySymbol: string;
    storeExternalId: string;
    storeId: number;
    storeName: string;
    voucherCode: string;
  }
  export interface CartOrderDataModel {
    paymentStatus?: string;
    worldpayOrderCode?: string;
    worldpayPaymentCode?: string;
  }
  export interface CartPriceModel {
    baseDiscountAmount: number;
    baseGrandTotal: number;
    baseShippingAmount: number;
    baseShippingDiscountAmount: number;
    baseShippingInclTax: number;
    baseShippingTaxAmount: number;
    baseSubtotal: number;
    baseSubtotalWithDiscount: number;
    baseTaxAmount: number;
    discountAmount: number;
    grandTotal: number;
    grandTotalMinorAmount: number;
    invalidTax: string;
    shippingAmount: number;
    shippingDiscountAmount: number;
    shippingInclTax: number;
    shippingTaxAmount: number;
    subTotal: number;
    subtotalInclTax: number;
    subtotalWithDiscount: number;
    taxAmount: number;
    totalCartAmount: number;
    totalCartDiscountAmount: number;
    totalGrossLocalCurrencyAmount: number;
    totalGrossLocalCurrencyMinorAmount: number;
    voucherDiscountAmount: number;
    weeeTaxAppliedAmount: number;
  }
  export interface ProductOptionModel {
    title: string;
    value: string;
  }
  export interface UpdateCurrencyPreferencesDto {
    currencyCode: string;
    isLocal: boolean;
  }
  export interface NavigationStepModel {
    stepFullUrl: string;
    stepId: number;
    stepTitle: string;
    stepUrl: string;
  }
  export interface NavigationStepsModel {
    mixedProducts: NavigationStepModel[];
    virtualProducts: NavigationStepModel[];
  }
  export interface EnrollAbandonedCartModel {
    quantity: number;
    sku: string;
  }
  export interface ChapterModel {
    chapterCity: string;
    chapterCode: string;
    chapterName: string;
    chapterRegion: string;
    chapterState: string;
    chapterUrl: string;
    globalPrice: number;
    memberPrice: number;
    optionId: string;
    productSku: string;
    selectionId: string;
  }
  export interface UpdateCartLineBundleOptionsResultModel {
    isCartLineBundleOptionsUpdated: boolean;
    updatedCartLine: CartLineModel;
  }
  export interface ExperienceTestConstants {
  }
  export interface FulfillmentProviderTypes {
  }
  export interface InformationCodes {
  }
  export interface InitStoreReduxActions {
  }
  export interface WarningCodes {
  }
  export const enum OperationStatuses {
    OK = 1,
    Error = 2,
    DataRequest = 3
  }
  export const enum PaymentCardTypes {
    Unknown = 0,
    Diners = 1,
    Discover = 2,
    MasterCard = 3,
    Amex = 4,
    Visa = 5,
    Jcb = 6,
    Paypal = 7,
    Alipay = 8,
    Cup = 9,
    Rupay = 10
  }
