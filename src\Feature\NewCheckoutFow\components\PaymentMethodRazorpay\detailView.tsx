import { selectedActivePaymentMethodAtom } from 'Feature/NewCheckoutFow/atoms';
import { UPI_LIMIT_AMOUNT } from 'Feature/NewCheckoutFow/constants';
import { useGetShoppingCart, usePaymentAutoSaveData, useRazorPay } from 'Feature/NewCheckoutFow/hooks';
import { PaymentMethodCodes } from 'Foundation/Payment/client/BackendConstants.Generated';
import { useSetAtom } from 'jotai';
import React, { useEffect } from 'react';
import { CardOption, RazorPayOption, RazorpayAutoSavePaymentAlert } from './components';
import { RazorpayDetailProps } from './models';

export const RazorpayContainer: React.FC<RazorpayDetailProps> = ({
  addingPaymentMethodCode,
  selectedPaymentMethodCode,
  fields,
  paymentMethodChanged,
}) => {
  const { isFetched, isLoading, isActive, availablePaymentMethods } = useRazorPay();
  const { shouldAutoSaveUserPaymentWhenRazorpay } = usePaymentAutoSaveData();
  const setActivePayment = useSetAtom(selectedActivePaymentMethodAtom);
  const { cart, cartHasMembership, isFetching: isCartLoading } = useGetShoppingCart();

  // TODO: TESTING - Force UPI to be available for testing disabled state
  const testAvailablePaymentMethods = React.useMemo(() => {
    const methods = [...(availablePaymentMethods || [])];
    if (!methods.includes(PaymentMethodCodes.RazorpayUpi)) {
      methods.push(PaymentMethodCodes.RazorpayUpi);
      console.log('TESTING: Added UPI to available payment methods');
    }
    return methods;
  }, [availablePaymentMethods]);

  // Debug logging for available payment methods
  React.useEffect(() => {
    console.log('Razorpay Debug:', {
      isActive,
      isLoading,
      isFetched,
      originalMethods: availablePaymentMethods,
      testMethods: testAvailablePaymentMethods,
      hasUPI: testAvailablePaymentMethods?.includes(PaymentMethodCodes.RazorpayUpi),
    });
  }, [isActive, isLoading, isFetched, availablePaymentMethods, testAvailablePaymentMethods]);

  useEffect(() => {
    if (isFetched && availablePaymentMethods.length > 0) {
      setActivePayment(availablePaymentMethods[0]);
    }
  }, [isFetched, availablePaymentMethods]);

  const isChecked = React.useCallback(
    (code: string) =>
      addingPaymentMethodCode === code || (!addingPaymentMethodCode && selectedPaymentMethodCode === code),
    [addingPaymentMethodCode, selectedPaymentMethodCode],
  );

  const isUpiEligible = React.useMemo(() => {
    try {
      console.log('UPI Eligibility Check - Starting:', {
        isCartLoading,
        hasCart: !!cart,
        cartHasMembership,
      });

      if (isCartLoading || !cart) {
        console.log('UPI Eligibility: Cart loading or no cart, returning true');
        return true;
      }

      // TODO: TESTING - Remove membership requirement temporarily to test disabled state
      // if (!cartHasMembership) return true;

      // TODO: Remove this fake price override after testing
      const fakePrice = 20000; // Set to 30000 to test disabled state, or 20000 to test enabled state
      const cartTotal = fakePrice; // cart?.price?.grandTotal || 0;
      const isEligible = cartTotal <= UPI_LIMIT_AMOUNT;

      console.log('UPI Eligibility Check - Final:', {
        cartTotal,
        UPI_LIMIT_AMOUNT,
        cartHasMembership,
        isEligible,
      });

      return isEligible;
    } catch (error) {
      console.error('Error calculating UPI eligibility:', error);
      return true;
    }
  }, [isCartLoading, cart, cartHasMembership]);

  if (!isLoading && !isActive) return null;

  const renderComponent = (code: string) => {
    console.log('Rendering payment method:', code);
    switch (code) {
      case PaymentMethodCodes.RazorpayCreditCard:
        return (
          <CardOption
            key={PaymentMethodCodes.RazorpayCreditCard}
            isChecked={isChecked(PaymentMethodCodes.RazorpayCreditCard)}
            onChange={paymentMethodChanged}
            paymentMethodCode={PaymentMethodCodes.RazorpayCreditCard}
            label={fields?.creditCardLabel?.value}
            description={fields?.creditCardSubtext?.value}
          />
        );
      case PaymentMethodCodes.RazorpayNetbanking:
        return (
          <RazorPayOption
            key={PaymentMethodCodes.RazorpayNetbanking}
            isChecked={isChecked(PaymentMethodCodes.RazorpayNetbanking)}
            onChange={paymentMethodChanged}
            paymentMethodCode={PaymentMethodCodes.RazorpayNetbanking}
            label={fields?.netbankingLabel?.value}
            description={fields?.netbankingSubtext?.value}
          />
        );
      case PaymentMethodCodes.RazorpayUpi:
        return (
          <RazorPayOption
            key={PaymentMethodCodes.RazorpayUpi}
            isChecked={isChecked(PaymentMethodCodes.RazorpayUpi)}
            onChange={paymentMethodChanged}
            paymentMethodCode={PaymentMethodCodes.RazorpayUpi}
            label={fields?.upiLabel?.value}
            description={
              fields?.upiSubtext?.value ||
              'UPI is only available for transactions totaling ₹24,999 or less when Membership is part of the order.'
            }
            disabled={!isUpiEligible}
          />
        );
      default:
        return null;
    }
  };

  return (
    <>
      {shouldAutoSaveUserPaymentWhenRazorpay && (
        <RazorpayAutoSavePaymentAlert message={fields?.membershipAndSubscriptionAutoRenewMessage?.value} />
      )}
      {testAvailablePaymentMethods?.map(renderComponent)}
    </>
  );
};
