import { selectedActivePaymentMethodAtom } from 'Feature/NewCheckoutFow/atoms';
import { UPI_LIMIT_AMOUNT } from 'Feature/NewCheckoutFow/constants';
import { useGetShoppingCart, usePaymentAutoSaveData, useRazorPay } from 'Feature/NewCheckoutFow/hooks';
import { PaymentMethodCodes } from 'Foundation/Payment/client/BackendConstants.Generated';
import { useSetAtom } from 'jotai';
import React, { useEffect } from 'react';
import { CardOption, RazorPayOption, RazorpayAutoSavePaymentAlert } from './components';
import { RazorpayDetailProps } from './models';

export const RazorpayContainer: React.FC<RazorpayDetailProps> = ({
  addingPaymentMethodCode,
  selectedPaymentMethodCode,
  fields,
  paymentMethodChanged,
}) => {
  const { isFetched, isLoading, isActive, availablePaymentMethods } = useRazorPay();
  const { shouldAutoSaveUserPaymentWhenRazorpay } = usePaymentAutoSaveData();
  const setActivePayment = useSetAtom(selectedActivePaymentMethodAtom);
  const { cart, cartHasMembership, isFetching: isCartLoading } = useGetShoppingCart();

  useEffect(() => {
    if (isFetched && availablePaymentMethods.length > 0) {
      setActivePayment(availablePaymentMethods[0]);
    }
  }, [isFetched, availablePaymentMethods]);

  const isChecked = React.useCallback(
    (code: string) =>
      addingPaymentMethodCode === code || (!addingPaymentMethodCode && selectedPaymentMethodCode === code),
    [addingPaymentMethodCode, selectedPaymentMethodCode],
  );

  const isUpiEligible = React.useMemo(() => {
    if (isCartLoading || !cart) return true;

    if (!cartHasMembership) return true;

    const cartTotal = cart.price?.grandTotal || 0;
    return cartTotal <= UPI_LIMIT_AMOUNT;
  }, [isCartLoading, cart, cartHasMembership]);

  if (!isLoading && !isActive) return null;

  const renderComponent = (code: string) => {
    switch (code) {
      case PaymentMethodCodes.RazorpayCreditCard:
        return (
          <CardOption
            key={PaymentMethodCodes.RazorpayCreditCard}
            isChecked={isChecked(PaymentMethodCodes.RazorpayCreditCard)}
            onChange={paymentMethodChanged}
            paymentMethodCode={PaymentMethodCodes.RazorpayCreditCard}
            label={fields?.creditCardLabel?.value}
            description={fields?.creditCardSubtext?.value}
          />
        );
      case PaymentMethodCodes.RazorpayNetbanking:
        return (
          <RazorPayOption
            key={PaymentMethodCodes.RazorpayNetbanking}
            isChecked={isChecked(PaymentMethodCodes.RazorpayNetbanking)}
            onChange={paymentMethodChanged}
            paymentMethodCode={PaymentMethodCodes.RazorpayNetbanking}
            label={fields?.netbankingLabel?.value}
            description={fields?.netbankingSubtext?.value}
          />
        );
      case PaymentMethodCodes.RazorpayUpi:
        return (
          <RazorPayOption
            key={PaymentMethodCodes.RazorpayUpi}
            isChecked={isChecked(PaymentMethodCodes.RazorpayUpi)}
            onChange={paymentMethodChanged}
            paymentMethodCode={PaymentMethodCodes.RazorpayUpi}
            label={fields?.upiLabel?.value}
            description={
              fields?.upiSubtext?.value ||
              'UPI is only available for transactions totaling ₹24,999 or less when Membership is part of the order.'
            }
            disabled={!isUpiEligible}
          />
        );
      default:
        return null;
    }
  };

  return (
    <>
      {shouldAutoSaveUserPaymentWhenRazorpay && (
        <RazorpayAutoSavePaymentAlert message={fields?.membershipAndSubscriptionAutoRenewMessage?.value} />
      )}
      {availablePaymentMethods.map(renderComponent)}
    </>
  );
};
