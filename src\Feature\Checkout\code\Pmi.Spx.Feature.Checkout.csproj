﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\..\..\Build\sitecore-versions.props.user" Condition="Exists('..\..\..\Build\sitecore-versions.props.user')" />
  <Import Project="..\..\..\Build\sitecore-versions.props" Condition="Exists('..\..\..\Build\sitecore-versions.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{EC1DB1BE-B3A9-4AC2-8700-34186E20444A}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Pmi.Spx.Feature.Checkout</RootNamespace>
    <AssemblyName>Pmi.Spx.Feature.Checkout</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <UseIISExpress>true</UseIISExpress>
    <Use64BitIISExpress />
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <RestoreProjectStyle>PackageReference</RestoreProjectStyle>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <MvcBuildViews>false</MvcBuildViews>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <MvcBuildViews Condition="!Exists('.\razorgenerator.directives')">false</MvcBuildViews>
    <MvcBuildViews Condition="Exists('.\razorgenerator.directives')">true</MvcBuildViews>
    <DebugSymbols>false</DebugSymbols>
    <DebugType>None</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Abstractions" />
    <Reference Include="System.Web.Routing" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.AspNet.Mvc">
      <Version>$(MvcVersion)</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.AspNet.Web.Optimization">
      <Version>1.1.3</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.AspNet.WebApi">
      <Version>5.2.6</Version>
    </PackageReference>
    <PackageReference Include="RazorGenerator.MsBuild">
      <Version>2.4.7</Version>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>
      </PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.AspNet.Razor">
      <Version>$(WebPageVersion)</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.AspNet.WebPages">
      <Version>$(WebPageVersion)</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform">
      <Version>3.6.0</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.Owin.Security.OpenIdConnect">
      <Version>4.0.0</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.Web.Infrastructure">
      <Version>1.0.0</Version>
    </PackageReference>
    <PackageReference Include="Sitecore.Kernel">
      <Version>$(SitecoreVersion)</Version>
    </PackageReference>
    <PackageReference Include="Sitecore.Mvc">
      <Version>$(SitecoreVersion)</Version>
    </PackageReference>
    <PackageReference Include="SPXBuildActions">
      <Version>$(SPXBuildActionsVersion)</Version>
    </PackageReference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ApiProviders\FreeProductActivationApiProvider.cs" />
    <Compile Include="ApiProviders\IFreeProductActivationApiProvider.cs" />
    <Compile Include="Configuration\FreeProductActivationApiSettings.cs" />
    <Compile Include="Configuration\FreeProductActivationConfiguration.cs" />
    <Compile Include="Configuration\IFreeProductActivationApiSettings.cs" />
    <Compile Include="Configuration\IFreeProductActivationConfiguration.cs" />
    <Compile Include="Constants\InitStoreReduxActions.cs" />
    <Compile Include="Controllers\ChapterProductsSearchController.cs" />
    <Compile Include="Configuration\IOrderTypeConfiguration.cs" />
    <Compile Include="Configuration\IOrderTypeSetting.cs" />
    <Compile Include="Configuration\IStoreTypeSetting.cs" />
    <Compile Include="Configuration\OrderTypeConfiguration.cs" />
    <Compile Include="Configuration\OrderTypeSetting.cs" />
    <Compile Include="Configuration\StoreTypeSetting.cs" />
    <Compile Include="Constants\ExperienceTestConstants.cs" />
    <Compile Include="Constants\FulfillmentProviderTypes.cs" />
    <Compile Include="Constants\InformationCodes.cs" />
    <Compile Include="Constants\CartOrderData.cs" />
    <Compile Include="Constants\WarningCodes.cs" />
    <Compile Include="Controllers\FreeProductActivationController.cs" />
    <Compile Include="Controllers\MarketingAutomationController.cs" />
    <Compile Include="Graph\GraphInputTypes\AddressInputGraphType.cs" />
    <Compile Include="Graph\GraphInputTypes\CartItemInputGraphType.cs" />
    <Compile Include="Graph\GraphInputTypes\CartItemOptionInputGraphType.cs" />
    <Compile Include="Graph\GraphInputTypes\GiftRecipientInputGraphType.cs" />
    <Compile Include="Graph\GraphInputTypes\PaymentCardInputGraphType.cs" />
    <Compile Include="Graph\GraphInputTypes\PaymentInputGraphType.cs" />
    <Compile Include="Graph\GraphInputTypes\ShippingMethodInputGraphType.cs" />
    <Compile Include="Graph\GraphInputTypes\UpdateCurrencyPreferencesInputGraphType.cs" />
    <Compile Include="Graph\GraphInputTypes\ValidatePONumberInputGraphType.cs" />
    <Compile Include="Graph\GraphResolver\MembershipResolver.cs" />
    <Compile Include="Graph\GraphTypes\AddressGraphType.cs" />
    <Compile Include="Graph\GraphTypes\AmountGraphType.cs" />
    <Compile Include="Graph\GraphTypes\CartOrderDataGraphType.cs" />
    <Compile Include="Graph\GraphTypes\CartGiftCardGraphType.cs" />
    <Compile Include="Graph\GraphTypes\CartGiftCardItemGraphType.cs" />
    <Compile Include="Graph\GraphTypes\CartGraphType.cs" />
    <Compile Include="Graph\GraphTypes\CartLineGraphType.cs" />
    <Compile Include="Graph\GraphTypes\CartLinePriceGraphType.cs" />
    <Compile Include="Graph\GraphTypes\CartPriceGraphType.cs" />
    <Compile Include="Graph\GraphTypes\ChapterGraphType.cs" />
    <Compile Include="Graph\GraphTypes\CustomDataGraphType.cs" />
    <Compile Include="Graph\GraphTypes\GiftRecipientGraphType.cs" />
    <Compile Include="Graph\GraphTypes\NavigationStepGraphType.cs" />
    <Compile Include="Graph\GraphTypes\NavigationStepsGraphType.cs" />
    <Compile Include="Graph\GraphTypes\OrderGraphType.cs" />
    <Compile Include="Graph\GraphTypes\PaymentResultGraphType.cs" />
    <Compile Include="Graph\GraphTypes\ProductGraphType.cs" />
    <Compile Include="Graph\GraphTypes\ProductOptionDataGraphType.cs" />
    <Compile Include="Graph\GraphTypes\SelectedBundleOptionsGraphType.cs" />
    <Compile Include="Graph\GraphTypes\ShippingMethodGraphType.cs" />
    <Compile Include="Graph\GraphTypes\UpdateCartLineBundleOptionsResultGraphType.cs" />
    <Compile Include="Graph\GraphTypes\ValidatePONumberResultGraphType.cs" />
    <Compile Include="Graph\Mutations\ReplaceCartLineMutation.cs" />
    <Compile Include="Graph\Mutations\AddCartLineMutation.cs" />
    <Compile Include="Graph\Mutations\ApplyCouponMutation.cs" />
    <Compile Include="Graph\Mutations\ApplyDonationMutation.cs" />
    <Compile Include="Graph\Mutations\ApplyGiftCardMutation.cs" />
    <Compile Include="Graph\Mutations\ApplyVoucherMutation.cs" />
    <Compile Include="Graph\Mutations\ConvertQuoteToOrderMutation.cs" />
    <Compile Include="Graph\Mutations\RemoveCartLineMutation.cs" />
    <Compile Include="Graph\Mutations\RemoveCouponMutation.cs" />
    <Compile Include="Graph\Mutations\RemoveVoucherMutation.cs" />
    <Compile Include="Graph\Mutations\SetShippingAddressMutation.cs" />
    <Compile Include="Graph\Mutations\SubmitPaymentDataMutation.cs" />
    <Compile Include="Graph\Mutations\UpdateCartLineBundleOptionsMutation.cs" />
    <Compile Include="Graph\Mutations\UpdateCartLineMutation.cs" />
    <Compile Include="Graph\Mutations\UpdatePaymentCurrencyPreferences.cs" />
    <Compile Include="Graph\Mutations\ValidatePOMutation.cs" />
    <Compile Include="Graph\Queries\AvailablePaymentMethodCodesQuery.cs" />
    <Compile Include="Graph\Queries\CartQuery.cs" />
    <Compile Include="Graph\Queries\ChaptersQuery.cs" />
    <Compile Include="Graph\Queries\NavigationQuery.cs" />
    <Compile Include="Graph\Queries\OrderQuery.cs" />
    <Compile Include="Graph\Queries\ShippingMethodsQuery.cs" />
    <Compile Include="JsModels\ActionResultApiResponseModel.cs" />
    <Compile Include="JsModels\AddressModel.cs" />
    <Compile Include="JsModels\Cart\BundleOptionModel.cs" />
    <Compile Include="JsModels\Cart\BundleProductOptionSearchResult.cs" />
    <Compile Include="JsModels\Cart\CartItemGiftRecipient.cs" />
    <Compile Include="JsModels\Cart\CartOrderDataModel.cs" />
    <Compile Include="JsModels\Cart\CartGiftCardItemModel.cs" />
    <Compile Include="JsModels\Cart\CartGiftCardTotalModel.cs" />
    <Compile Include="JsModels\Cart\CartItemDto.cs" />
    <Compile Include="JsModels\Cart\CartItemOption.cs" />
    <Compile Include="JsModels\Cart\CartLineModel.cs" />
    <Compile Include="JsModels\Cart\CartLinePriceModel.cs" />
    <Compile Include="JsModels\Cart\CartModel.cs" />
    <Compile Include="JsModels\Cart\CartPriceModel.cs" />
    <Compile Include="JsModels\Cart\ProductOptionModel.cs" />
    <Compile Include="JsModels\Cart\UpdateCurrencyPreferencesDto.cs" />
    <Compile Include="JsModels\Chapter\ChapterModel.cs" />
    <Compile Include="JsModels\Contracts\ICartLines.cs" />
    <Compile Include="JsModels\MarketingAutomation\EnrollAbandonedCartModel.cs" />
    <Compile Include="JsModels\Navigation\NavigationStepModel.cs" />
    <Compile Include="JsModels\Navigation\NavigationStepsModel.cs" />
    <Compile Include="JsModels\Order\CustomDataModel.cs" />
    <Compile Include="JsModels\Order\OrderModel.cs" />
    <Compile Include="JsModels\Payment\Amount.cs" />
    <Compile Include="JsModels\Payment\PaymentCardModel.cs" />
    <Compile Include="JsModels\Payment\PaymentModel.cs" />
    <Compile Include="JsModels\Payment\PaymentResultModel.cs" />
    <Compile Include="JsModels\Payment\ValidatePONumberInitModel.cs" />
    <Compile Include="JsModels\Shipping\ShippingMethodModel.cs" />
    <Compile Include="Mappers\IMapper.cs" />
    <Compile Include="Mappers\Mapper.cs" />
    <Compile Include="Models\FreeProductActivationResponse.cs" />
    <Compile Include="Models\SignalRNegotiateResponse.cs" />
    <Compile Include="Pipelines\GlassMapper\GetGlassLoaders\PageContentGlassLoader.cs" />
    <Compile Include="Pipelines\RegisterRoutes.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="..\..\..\Build\Version.cs">
      <Link>Properties\Version.cs</Link>
    </Compile>
    <Compile Include="Providers\CheckoutSettingsProvider.cs" />
    <Compile Include="Providers\ICheckoutSettingsProvider.cs" />
    <Compile Include="RegisterDependencies.cs" />
    <Compile Include="Rules\PmiUsersCurrentSetPreferedCurrencyCondition.cs" />
    <Compile Include="Services\CartLineService.cs" />
    <Compile Include="Services\ChapterService.cs" />
    <Compile Include="Services\CheckoutNavigationService.cs" />
    <Compile Include="Services\FreeProductActivationService.cs" />
    <Compile Include="Services\ICartLineService.cs" />
    <Compile Include="Services\IChapterService.cs" />
    <Compile Include="Services\ICheckoutNavigationService.cs" />
    <Compile Include="Services\IFreeProductActivationService.cs" />
    <Compile Include="Services\IMarketingAutomationService.cs" />
    <Compile Include="Services\IPaymentService.cs" />
    <Compile Include="Services\IShippingService.cs" />
    <Compile Include="Services\MarketingAutomationService.cs" />
    <Compile Include="Services\PaymentService.cs" />
    <Compile Include="Services\ShippingService.cs" />
    <Compile Include="SitecoreModels.Extended.cs" />
    <Compile Include="SitecoreModels.Generated.cs" />
    <Compile Include="StoreInitializers\AvailablePaymentMethodCodesInitializer.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="App_Config\Include\Feature\SPX\Feature.Checkout.config" />
    <Compile Include="Rules\PmiUserHasSetPreferedCurrencyCondition.cs" />
    <Content Include="App_Config\Include\Z.Feature\SPX\Feature.Checkout.ENV.config" />
    <None Include="Properties\PublishProfiles\LocalDev.pubxml" />
    <None Include="Web.config" />
    <None Include="razorgenerator.directives.disabled" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\Foundation\Commerce\code\Pmi.Spx.Foundation.Commerce.csproj">
      <Project>{4cd01a37-76d8-4a6a-8822-014369c4243d}</Project>
      <Name>Pmi.Spx.Foundation.Commerce</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Foundation\Connect\code\Pmi.Spx.Foundation.Connect.csproj">
      <Project>{60cd1765-857b-4bc8-831e-7526264f1ec5}</Project>
      <Name>Pmi.Spx.Foundation.Connect</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Foundation\CRM\code\Pmi.Spx.Foundation.CRM.csproj">
      <Project>{88184e35-5dbe-4246-a741-54399e87405e}</Project>
      <Name>Pmi.Spx.Foundation.CRM</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Foundation\Extensions\code\Pmi.Spx.Foundation.Extensions.csproj">
      <Project>{B4CD14E8-725D-401C-8D99-F20EF83E3FF5}</Project>
      <Name>Pmi.Spx.Foundation.Extensions</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Foundation\Framework\code\Pmi.Spx.Foundation.Framework.csproj">
      <Project>{32c6aa3c-2163-4eaa-965e-b5df329bd05b}</Project>
      <Name>Pmi.Spx.Foundation.Framework</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Foundation\GeoIP\code\Pmi.Spx.Foundation.GeoIP.csproj">
      <Project>{40084298-92c3-424d-aa45-f27aa858ab75}</Project>
      <Name>Pmi.Spx.Foundation.GeoIP</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Foundation\GlassMapper\code\Pmi.Spx.Foundation.GlassMapper.csproj">
      <Project>{0044b0ca-abe2-4365-b1b2-1e347993b4aa}</Project>
      <Name>Pmi.Spx.Foundation.GlassMapper</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Foundation\GraphQL\code\Pmi.Spx.Foundation.GraphQl.csproj">
      <Project>{bba3c893-edd6-4666-8f00-4392091f98bb}</Project>
      <Name>Pmi.Spx.Foundation.GraphQl</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Foundation\MagentoConnector\code\Pmi.Spx.Foundation.MagentoConnector.csproj">
      <Project>{caab64b3-3adb-4c71-8516-025d877a86cb}</Project>
      <Name>Pmi.Spx.Foundation.MagentoConnector</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Foundation\Payment\code\Pmi.Spx.Foundation.Payment.csproj">
      <Project>{e1a74f35-b944-44e7-942d-47f654f4b9fd}</Project>
      <Name>Pmi.Spx.Foundation.Payment</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Foundation\Profile\code\Pmi.Spx.Foundation.Profile.csproj">
      <Project>{0611ec87-85c8-41ac-8606-d1ee82a059ba}</Project>
      <Name>Pmi.Spx.Foundation.Profile</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Foundation\ReactJss\code\Pmi.Spx.Foundation.ReactJss.csproj">
      <Project>{9F27A62C-2A80-42F8-8BF0-3877B65C0427}</Project>
      <Name>Pmi.Spx.Foundation.ReactJss</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Foundation\Security\code\Pmi.Spx.Foundation.Security.csproj">
      <Project>{f25e8dda-07c1-43c0-aa8c-fd3b6008ac89}</Project>
      <Name>Pmi.Spx.Foundation.Security</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup />
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <Target Name="MvcBuildViews" AfterTargets="AfterBuild" Condition="'$(MvcBuildViews)'=='true'">
    <AspNetCompiler VirtualPath="temp" PhysicalPath="$(WebProjectOutputDir)" />
  </Target>
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>False</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>20924</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:20924/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>True</UseCustomServer>
          <CustomServerUrl>https://sc911.local</CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <!--System.Web.Mvc this line only exists so that razorgenerator picks this up as an mvc project -->
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target> -->
</Project>