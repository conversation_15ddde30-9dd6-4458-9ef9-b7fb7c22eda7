// Test to check if Tailwind CSS bundle is being generated
const fs = require('fs');
const path = require('path');

function checkBuildOutput() {
  console.log('🔍 Checking for Tailwind CSS build output...\n');
  
  // Check if the output directory exists
  const outputDir = path.resolve('../clientCode/spx');
  console.log('Looking for build output in:', outputDir);
  
  if (!fs.existsSync(outputDir)) {
    console.log('❌ Build output directory does not exist:', outputDir);
    console.log('💡 You need to run the webpack build first');
    return;
  }
  
  // List all files in the output directory
  const files = fs.readdirSync(outputDir);
  console.log('📁 Files in build output:');
  files.forEach(file => {
    console.log('  -', file);
  });
  
  // Look for Tailwind CSS files
  const tailwindFiles = files.filter(file => file.includes('tailwind-styles'));
  
  if (tailwindFiles.length === 0) {
    console.log('\n❌ No tailwind-styles files found in build output');
    console.log('💡 This means the Tailwind CSS bundle is not being generated');
    console.log('💡 Try running the webpack build to generate the CSS bundle');
  } else {
    console.log('\n✅ Found Tailwind CSS files:');
    tailwindFiles.forEach(file => {
      console.log('  -', file);
      
      // Check file size
      const filePath = path.join(outputDir, file);
      const stats = fs.statSync(filePath);
      console.log(`    Size: ${(stats.size / 1024).toFixed(2)} KB`);
      
      // If it's a CSS file, check if it contains Tailwind classes
      if (file.endsWith('.css')) {
        const content = fs.readFileSync(filePath, 'utf8');
        const hasUtilities = content.includes('bg-blue-500') || content.includes('text-white') || content.includes('p-4');
        console.log(`    Contains Tailwind utilities: ${hasUtilities ? '✅' : '❌'}`);
        
        if (content.length > 0) {
          console.log(`    Content preview: ${content.substring(0, 200)}...`);
        }
      }
    });
  }
  
  console.log('\n📋 Next steps:');
  console.log('1. If no tailwind-styles files exist, run webpack build');
  console.log('2. If files exist but are empty, check Tailwind configuration');
  console.log('3. If files exist with content, check HTML includes the CSS file');
}

checkBuildOutput();
