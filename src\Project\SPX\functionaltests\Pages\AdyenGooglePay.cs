﻿namespace Pmi.Spx.Project.Functional.Tests.Pages
{
    using OpenQA.Selenium.Support.UI;
    using Pmi.Web.Ui.Framework.Page;
    using System.Linq;
    using FluentAssertions;
    using OpenQA.Selenium;
    using Pmi.Spx.Project.Functional.Tests.Models;
    using Pmi.Web.Ui.Framework.Extensions;
    using System.Threading;
    using Pmi.Spx.Project.SPX.Functional.Tests.Hooks;

    public class AdyenGooglePay : BasePage<AdyenGooglePay>
    {
        //Adyen GooglePay
        private IWebElement AdyenGpayUsername => WebDriver.FindElement(By.XPath("//input[@id='identifierId']"));

        private IWebElement AdyenGpayPassword => WebDriver.FindElement(By.XPath("//input[@id='password']"));

        private IWebElement VerifyTitle => WebDriver.FindElement(By.XPath("//*[@id=\"id-1\"]"));

        private IWebElement CompletePurchaseTitle => WebDriver.FindElement(By.XPath("//iframe[@title='Complete your purchase']"));

        private IWebElement GPayframe => WebDriver.FindElement(By.XPath("//div[@id='sM432d']//iframe"));

        private IWebElement AdyenGpayNext => WebDriver.FindElement(By.XPath("//button[@id='identifierNext']"));

        private IWebElement AdyenGpayContinue => WebDriver.FindElement(By.XPath("//span[contains(text(), 'Continue')] | //span[contains(text(), 'Pay')]"));

        private IWebElement AdyenGpayPasswordNext => WebDriver.FindElement(By.XPath("//button[@id='passwordNext']"));

        private bool AdyenGpayUserNameDisplayed() => Extensions.CatchUnavailableElement(() => AdyenGpayUsername.Displayed, false);

        private bool AdyenGpayPasswordDisplayed() => Extensions.CatchUnavailableElement(() => AdyenGpayPassword.Displayed, false);

        private bool AdyenGpayContinueToPlaceOrderDisplayed() => Extensions.CatchUnavailableElement(() => AdyenGpayContinue.Displayed, false);

        private bool GpayPlaceOrderTitle() => Extensions.CatchUnavailableElement(() => VerifyTitle.Displayed, false);

        private readonly WebDriverWait _wait;

        private readonly UserSettings _userSettings;

        public AdyenGooglePay(IWebDriver driver) : base(driver)
        {
            _userSettings = new UserSettings();
            _wait = new WebDriverWait(WebDriver, _userSettings.DefaultExplicitWaitTimeout);
        }
        public override string BaseUrl => _userSettings.BaseUrl;
        public override string RelativePath => "";

        public override AdyenGooglePay VerifyPageLoaded()
        {
            WebDriver.WaitForAllLoadersInvisible();
            WebDriver.LogCurrentUrl();
            return base.VerifyPageLoaded();
        }
        public AdyenGooglePay PlaceAdyenGooglePayOrder(TestData testData)
        {
            WebDriver.Navigate().Refresh();

            LoginToGooglePay(testData.GetAdyenCredential());
            ContinuePayment();

            return this;
        }

        private void LoginToGooglePay(AdyenCredential adyenCredential)
        {
            EnterUsername(adyenCredential.UserName);
            EnterPassword(adyenCredential.Password);
        }

        private void EnterUsername(string username)
        {
            _wait.Until(_ => AdyenGpayUserNameDisplayed());
            AdyenGpayUsername.Displayed.Should().BeTrue("Username Field is not displayed");
            AdyenGpayUsername.ScrollAndClickAndWait(WebDriver);
            AdyenGpayUsername.SendKeys(username);
            AdyenGpayNext.Displayed.Should().BeTrue("Click Next Button is not displayed");
            AdyenGpayNext.ScrollAndClickAndWait(WebDriver);
        }

        private void EnterPassword(string password)
        {
            _wait.Until(_ => AdyenGpayPasswordDisplayed());
            AdyenGpayPassword.Displayed.Should().BeTrue("Password field is not displayed");
            AdyenGpayPassword.ScrollAndClickAndWait(WebDriver);
            AdyenGpayPassword.SendKeys(password);
            AdyenGpayPasswordNext.ScrollAndClickAndWait(WebDriver);
            Thread.Sleep(10000);
            WebDriver.SwitchTo().Frame(GPayframe);
            WebDriver.WaitForAllLoadersInvisible();
        }

        private void ContinuePayment()
        {
            _wait.Until(_ => GpayPlaceOrderTitle());
            _wait.Until(_ => AdyenGpayContinueToPlaceOrderDisplayed());
            AdyenGpayContinue.Displayed.Should().BeTrue("Continue button is not displayed");
            AdyenGpayContinue.ActionClick(WebDriver);
            WebDriver.SwitchTo().Window(WebDriver.WindowHandles.First());
        }
    }
}
