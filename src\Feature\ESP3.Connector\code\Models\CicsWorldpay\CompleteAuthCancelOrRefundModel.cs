﻿namespace Pmi.Spx.Feature.ESP3.Connector.Models.CicsWorldpay
{
    using Newtonsoft.Json;

    public class CompleteAuthCancelOrRefundModel
    {
        [JsonProperty("reference")]
        public string Reference { get; set; }

        [JsonProperty("paymentPspReference")]
        public string PaymentPspReference { get; set; }

        [JsonProperty("merchantCode")]
        public string MerchantCode { get; set; }

        [JsonProperty("paymentMethod")]
        public string PaymentMethod { get; set; }

        [JsonProperty("paymentVendor")]
        public string PaymentVendor { get; set; }
    }
}