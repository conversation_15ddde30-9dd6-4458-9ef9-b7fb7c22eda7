import * as React from 'react';

import AnalyticsInitializer from 'Foundation/Analytics/client/Initializer';
import FastRedirect from 'Foundation/Security/client/FastRedirect';

import { SSRState } from '../models';

interface HTML {
  content: string;
  ssrState: SSRState;
  distPath: string;
  assetMap: { [key: string]: string };
  version: string;
}

const Html: React.FC<HTML> = ({ content, ssrState, distPath, assetMap, version }) => {
  const {
    REDUX_STATE: {
      viewBag: {
        signInUrl,
        httpContext: {
          request: { path },
        },
      },
      sitecore: {
        context: {
          globalSettings: {
            styles: { stylesUrls },
          },
          site: { name: siteName },
        },
      },
    },
  } = ssrState;
  const translateAssetName = (filename: string) => {
    return assetMap[filename] || filename.replace('.js', '.bundle.js');
  };

  return (
    <html>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />
        <meta httpEquiv="X-UA-Compatible" content="IE=edge" />
        <meta name="robots" content="noindex, nofollow" />
        {stylesUrls?.map((_) => <link key={_} href={_} rel="stylesheet" />)}
        <link href={`${distPath}/${translateAssetName('base-client.css')}`} rel="stylesheet" />
        <link href={`${distPath}/${translateAssetName('tailwind-styles.css')}`} rel="stylesheet" />
        <link rel="stylesheet" href="//maxcdn.bootstrapcdn.com/font-awesome/4.5.0/css/font-awesome.min.css" />
        <link rel="icon" href={`/favicon.ico`} type="image/x-icon" />
        {siteName === 'B2B2C' && (
          <>
            <link href={`/css/icons-shared-nav.css?v=${version}`} rel="stylesheet" />
            <link href={`/css/shared.navigation.min.css?v=${version}`} rel="stylesheet" />
          </>
        )}

        <link rel="apple-touch-icon" sizes="180x180" href="~/media/pmi/icons/favicon/apple-touch-icon.png" />
      </head>
      <body>
        {!ssrState.REDUX_STATE.security.currentUser && <FastRedirect signInUrl={signInUrl} path={path} />}
        <div id="app" dangerouslySetInnerHTML={{ __html: content }} />
        <div id="app-state" style={{ display: 'none' }}>
          {JSON.stringify(ssrState)}
        </div>
        <script type="text/javascript" dangerouslySetInnerHTML={{ __html: "window.datalayer_pageload = 'off';" }} />
        <script src={`${distPath}${translateAssetName('runtime.js')}`} />
        <script src={`${distPath}${translateAssetName('vendors.js')}`} />
        <script src={`${distPath}${translateAssetName('dsm-react.js')}`} />
        <script src={`${distPath}${translateAssetName('base-client.js')}`} />
        <script src={`${distPath}${translateAssetName('spa-client.js')}`} />
        {/* // feature switch to determine which javascript file to load */}
        <AnalyticsInitializer />
      </body>
    </html>
  );
};

export default Html;
