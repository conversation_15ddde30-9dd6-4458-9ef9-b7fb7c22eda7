﻿// ReSharper disable CollectionNeverUpdated.Local

#pragma warning disable 649

namespace Pmi.Spx.Project.Functional.Tests.Pages
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text.RegularExpressions;
    using OpenQA.Selenium;
    using OpenQA.Selenium.Support.UI;
    using Pmi.Spx.Project.Functional.Tests.Components;
    using Pmi.Spx.Project.Functional.Tests.Models;
    using Pmi.Spx.Project.SPX.Functional.Tests.Hooks;
    using Pmi.Web.Ui.Framework.Extensions;
    using Pmi.Web.Ui.Framework.Page;
    using Amount = Currency.Amount;
    using User = Models.User;
    using TechTalk.SpecFlow;
    using System.IO;
    using System.Drawing;
    using ZXing;
    using NUnit.Framework;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Assert = NUnit.Framework.Assert;

    public class CheckoutPage : BasePage<CheckoutPage>
    {
        private const int ZeroDollarSubscription = 0;

        string projectPath = Directory.GetParent(Directory.GetCurrentDirectory()).Parent.FullName;

        private const string OrderSummaryAccordian = ".//section[@id='accordion-ordersummary']//li";

        private IList<IWebElement> OrderSummaryProductList => WebDriver.FindElements(By.XPath(OrderSummaryAccordian));

        private IWebElement CheckoutPageHeader => WebDriver.FindElement(By.XPath("//div[contains(@class,'checkout-container__heading')]/*[contains(text(),'Checkout')]"));

        private IWebElement QuotePageHeader => WebDriver.FindElement(By.XPath("//div[contains(@class,'checkout-container__heading')]/h1[contains(text(),'Quote')]"));

        private IWebElement ViewCartItems => WebDriver.FindElement(By.XPath(".//button[@id='button-accordion-ordersummary']/span[text()='View Items']"));

        private IWebElement HideCartItems => WebDriver.FindElement(By.XPath(".//button[@id='button-accordion-ordersummary']/span[text()='Hide Items']"));

        private IWebElement BackButton => WebDriver.FindElement(By.XPath("//span[@class='back-to-cart-text']"));

        private IWebElement EditContactButton => WebDriver.FindElement(By.Id("button-contact-detail-view"));

        private IWebElement EditPayment => WebDriver.FindElement(By.Id("button-payment-detail-view"));

        private IWebElement EditBilling => WebDriver.FindElement(By.Id("button-billing-detail-view"));

        private IWebElement AddressEdit => WebDriver.FindElement(By.XPath("//div[@class='payment-billing__item-edit']/button[@title='Edit']"));

        private IWebElement EmailAddressInput => WebDriver.FindElement(By.XPath("//input[@id='email']"));

        private IWebElement UseThisContactInformation => WebDriver.FindElement(By.XPath("//button[@type='submit'][text()='Use This Contact Information']"));

        private IWebElement PaymentMethod => WebDriver.FindElement(By.XPath("//div[@class='payment-heading-worldpay']"));

        private IWebElement PaymentMethods => WebDriver.FindElement(By.XPath("//*[@class='payment-paymentmethods'] | //*[text()='Your Saved Payment Methods']"));

        private IList<IWebElement> SavedPaymentMethods => WebDriver.FindElements(By.XPath("//*[text()='Your Saved Payment Methods']/parent::div//li"));

        private IWebElement CreditCardPayment => WebDriver.FindElement(By.XPath("//div[@class='mui-radiobutton']//input[@id='worldpay_api']/following-sibling::span"));

        private IWebElement TokenizedCardPayment => WebDriver.FindElement(By.XPath("//div[@class='payment-paymentmethod-worldpay__item-display container-fluid p-0']"));

        private IWebElement PayPalPayment => WebDriver.FindElement(By.XPath("//div[@class='mui-radiobutton']//input[@id='worldpay_paypal_api']/following-sibling::span"));

        private IWebElement TokenizedPayPalPayment => WebDriver.FindElement(By.XPath("//div[@class='mui-radiobutton']//div[text()='PayPal']"));

        private IWebElement AliPayPayment => WebDriver.FindElement(By.XPath("//span[text()='AliPay']/ancestor::button"));

        private IWebElement RadioButtonIsChecked => WebDriver.FindElement(By.XPath("//div[@class='mui-radiobutton']//input[@type='radio' and @checked='']"));

        private IWebElement SavedCardPayment => WebDriver.FindElement(By.XPath("//div[@class='payment-heading-worldpay']"));

        private IWebElement AddNewPaymentMethod => _userSettings.Environment.Equals("QA") ?
            WebDriver.FindElement(By.XPath("//button[text()='Add A New Payment Method']")) :
        WebDriver.FindElement(By.XPath("//button[text()='Add New Payment Method']"));

        private IWebElement SavedPayPalPayment => WebDriver.FindElement(By.XPath("//div[@class='payment-heading-paypal']"));

        private IWebElement SavedAliPayPayment => WebDriver.FindElement(By.XPath("//div[@class='payment-heading-alipay']"));

        private IWebElement SavePayPalAsDefaultPayment => WebDriver.FindElement(By.XPath($"//span[@class='paypal-form__check-label'][text()='{SAVE_PAYPAL_AS_DEFAULT_PAYMENT}']"));

        private IWebElement UseThisPaymentMethod => WebDriver.FindElement(By.XPath("//button[text()='Use This Payment Method']"));

        private IWebElement PromoCode => WebDriver.FindElement(By.Id("couponCode"));

        private IWebElement PromoCodeApplyButton => WebDriver.FindElement(By.XPath("//form[@class='new-promocode']//button[text()='Apply']"));

        private IWebElement PromoCodeSuccessfulMessage => WebDriver.FindElement(By.XPath("//form[@class='new-promocode']//span[@class='chip-input__text'] | //form[@class='new-promocode']//following-sibling::button//span[@class='chip-input__text']"));

        private IWebElement PromoCodeToolTipIcon => WebDriver.FindElement(By.XPath("//form[@class='new-promocode']//button[contains(@class,'dsm-tooltip__trigger')]"));

        private IWebElement PromoCodeToolTip => WebDriver.FindElement(By.XPath("//div[@class = 'new-promocode__tooltip-message']"));

        private IWebElement VoucherCode => WebDriver.FindElement(By.Id("voucherCode"));

        private IWebElement VoucherReedeemButton => WebDriver.FindElement(By.XPath("//button[@type='button'][text()='Redeem']"));

        private IWebElement VoucherSuccessfulMessage => WebDriver.FindElement(By.XPath("//div[@class='new-vouchercode-container']//span[@class='chip-input__text']"));

        private IWebElement VoucherCodeToolTipIcon => WebDriver.FindElement(By.XPath("//form[@class='new-vouchercode']//button[contains(@class,'dsm-tooltip__trigger')]"));

        private IWebElement VoucherToolTip => WebDriver.FindElement(By.XPath("//div[@class='new-vouchercode__tooltip-message']"));

        private IWebElement PlaceOrderButton => WebDriver.FindElement(By.XPath("//button[text()='Place Your Order!'] | //button[text()='Place Order']"));

        private IWebElement PayWithPayPalButton => WebDriver.FindElement(By.XPath("//button[text()='Pay With Paypal'] | //button[text()='Place Order']"));

        private IWebElement PayWithAliPayButton => WebDriver.FindElement(By.XPath("//button[text()='Pay With Alipay']"));

        private IWebElement PhoneNumber => WebDriver.FindElement(By.XPath("//input[@name='dialingNumber']"));

        private IList<IWebElement> CountryCode => WebDriver.FindElements(By.XPath("//select[@id='dialingCountryCode']/option"));

        private IWebElement CpfNumber => WebDriver.FindElement(By.XPath(".//input[@name='brazilCpfCode']"));

        private IWebElement AddCpfButton => WebDriver.FindElement(By.XPath("//button[@type='submit'][text()='Add CPF']"));

        private IWebElement ErrorBlock => WebDriver.FindElement(By.XPath("//div[@class='errors block-section']//li/div"));

        private IWebElement EditCart => WebDriver.FindElement(By.XPath("//div[@class='order-summary']//button[contains(text(),'Cart')]"));

        private IWebElement ErrorBlockSection => WebDriver.FindElement(By.XPath("//div[@class='errors block-section']"));

        private IWebElement BlockExclamationMark => WebDriver.FindElement(By.XPath("//div[@class='block-exclamation']/i"));

        private IWebElement ErrorMessage => WebDriver.FindElement(By.XPath("//li[@class='block-item']/div"));

        private IWebElement ErrorMessageClose => WebDriver.FindElement(By.XPath("//*[contains(@class,'block-btn-close')]"));

        private IWebElement ClearErrorMessage => WebDriver.FindElement(By.XPath("//a[@title='Clean messages']/span"));

        private IWebElement SaveForFutureCheckboxForAdyen => WebDriver.FindElement(By.CssSelector("input[name*=storeDetails]"));

        //Adyen Credit Card
        private IWebElement AdyenCreditCardPayment => WebDriver.FindElement(By.XPath("//*[contains(@class,'adyen-checkout__payment-method__name')][text()='Credit or debit card']"));

        private IWebElement AdyenCreditCardSection => WebDriver.FindElement(By.XPath("//*[contains(@class,'adyen-checkout__payment-method--card')]"));

        private IWebElement AdyenCheckoutSpinner => WebDriver.FindElement(By.XPath("//div[contains(@class,'adyen-checkout__spinner')]"));

        private IWebElement AdyenDropInLoading => WebDriver.FindElement(By.XPath("//div[contains(@class,'adyen-checkout__dropin--loading')]"));

        private IWebElement AdyenPayPalPayment => WebDriver.FindElement(By.XPath("//*[contains(@class,'adyen-checkout__payment-method__name')][text()='PayPal']"));

        private IWebElement AdyenGooglePayPayment => WebDriver.FindElement(By.XPath("//*[contains(@class,'adyen-checkout__payment-method__name')][text()='Google Pay']"));

        private IWebElement AdyenSaveForFutureCheckbox => WebDriver.FindElement(By.XPath("//span[@class='adyen-checkout__checkbox__label']"));

        private IWebElement AdyenCreditCardPay => WebDriver.FindElement(By.XPath("//button//span[contains(text(),'Pay ')]"));

        private IWebElement AdyenAutoSaveForFutureMessage => WebDriver.FindElement(By.XPath($"//div[@class='alert-content']/span[text()='{AUTO_SAVE_ADYEN_MEMBERSHIP}'] | //div[@class='alert-content']/span[text()='{AUTO_SAVE_ADYEN_SUBSCRIPTION}'] " +
            $"| //div[@class='alert-content']/span[text()='{AUTO_SAVE_ADYEN_QA}']"));

        private IWebElement AdyenPaymentAccordian => WebDriver.FindElement(By.Id("payment-detail-view"));

        private IWebElement IFrameCreditCardNumber => WebDriver.FindElement(By.XPath("//iframe[@title='Iframe for card number']"));

        private By AdyenCreditCardNumber => By.XPath("//input[contains(@id,'adyen-checkout-encryptedCardNumber')]");

        private By AdyenCreditCardExpiryField => By.XPath("//input[contains(@id,'adyen-checkout-encryptedExpiryDate')]");

        private IWebElement IFrameCreditCardExpiry => WebDriver.FindElement(By.XPath("//iframe[@title='Iframe for expiry date']"));

        private By AdyenCreditCardSecurityCode => By.XPath("//input[contains(@id,'adyen-checkout-encryptedSecurityCode')]");

        private IWebElement IFrameAdyen3DS => WebDriver.FindElement(By.XPath("//iframe[contains(@class,'adyen-checkout__iframe--threeDSIframe')]"));

        private IWebElement SubmitAdyen3DS => WebDriver.FindElement(By.XPath("//button[@Id='buttonSubmit']"));

        private By Adyen3DSpasswordField => By.XPath("//input[@class='input-field']");

        private IWebElement IFrameCreditCardSecurityCode => WebDriver.FindElement(By.XPath("//iframe[@title='Iframe for security code']"));

        private IWebElement AdyenCreditCardNameField => WebDriver.FindElement(By.XPath("//input[contains(@id,'adyen-checkout-holderName')]"));

        private IWebElement AdyenCreditCardPayButton => WebDriver.FindElement(By.XPath("//button[contains(@class,'adyen-checkout__button')]//span[@class='adyen-checkout__button__text']"));

        private IWebElement AdyenCreditCardSaveDetailsButton => WebDriver.FindElement(By.XPath("//span[@class='adyen-checkout__button__text' and text()='Save details']"));

        private IWebElement AdyenPaypalButtonDisplayed => WebDriver.FindElement(By.XPath("//iframe[contains(@name,'paypal_buttons')]"));

        private IWebElement AdyenPaypalButton => WebDriver.FindElement(By.XPath("//div[@class='adyen-checkout__paypal']//div[contains(@class,'button--paypal')]"));

        private IWebElement AdyenPaypalPayLaterButton => WebDriver.FindElement(By.XPath("//div[@class='adyen-checkout__paypal']//div[contains(@class,'button--pay-later')]"));

        private IWebElement SavedAddress => WebDriver.FindElement(By.XPath("//div[@class='address-info']"));

        private IWebElement SavedCard => WebDriver.FindElement(By.XPath("//span[contains(@class,'adyen-checkout__payment-method__name--selected')]"));

        private IWebElement EditCard => WebDriver.FindElement(By.XPath("//div[@class='payment-paymentmethod-worldpay__item-edit']/button"));
        //Adyen Gpay
        private IWebElement ContinueToAliPay => WebDriver.FindElement(By.XPath("//button[contains(@class,'adyen-checkout__button')]//span[text()='Continue to AliPay']"));

        private IWebElement LegalStatementBlock => WebDriver.FindElement(By.XPath("//span[@class='alert-text']"));

        private IWebElement PaymentMethodsDropInComponent => WebDriver.FindElement(By.XPath("//div[@id='adyendropin-container']//ul/li"));

        private IWebElement SelectGooglePay => WebDriver.FindElement(By.XPath("//button[@class='adyen-checkout__payment-method__header__title']//span[contains(text(), 'Google Pay')]"));

        private IWebElement GooglepayPaymentImg => WebDriver.FindElement(By.XPath("//span[contains(text(), 'Google Pay')]/ancestor::button//span/img"));

        private IWebElement GooglePayRadioButton => WebDriver.FindElement(By.XPath("//span[contains(text(), 'Google Pay')]/ancestor::button/span"));

        private IWebElement BuyWithGooglePayButton => WebDriver.FindElement(By.XPath("//button[@id='gpay-button-online-api-id']"));

        private IWebElement AdyenGPay => WebDriver.FindElement(By.XPath("//iframe[@class='gpay-card-info-iframe gpay-card-info-iframe-fade-in']"));

        private IList<IWebElement> PaymentMethodsList => WebDriver.FindElements(By.XPath("//div[@class='adyen-checkout__dropin adyen-checkout__dropin--ready']/ul/li"));

        private bool AdyenPaymentDropInComponentsDisplayed() => Extensions.CatchUnavailableElement(() => PaymentMethodsDropInComponent.Displayed, false);

        private IWebElement AdyenPaymentDropInSpinner => WebDriver.FindElement(By.XPath("//div[contains(@class,'adyen-checkout__spinner__wrapper')]"));

        private bool AdyenPaymentDropInSpinnerDisplayed() => Extensions.CatchUnavailableElement(() => AdyenPaymentDropInSpinner.Displayed, false);

        //Adyen Alipay

        private IWebElement AdyenAliPayPayment => WebDriver.FindElement(By.XPath("//*[contains(@class,'adyen-checkout__payment-method__name')][text()='AliPay']"));

        private bool AdyenAliPayPaymentDisplayed() => Extensions.CatchUnavailableElement(() => AdyenAliPayPayment.Displayed, false);

        public By GetBillingAddressModal => By.Id("billing-detail-view");

        public By GetCreditCardModal => By.XPath("//div[contains(@class,'payment-paymentmethod-worldpay')]/form");

        public By GetPayPalModal => By.XPath("//div[(@class='paypal-form')]");

        private IWebElement ContactSection => WebDriver.FindElement(By.XPath("//div[@class='payment-contact']"));

        private IWebElement BillingAddressSection => WebDriver.FindElement(By.XPath("//div[@class='payment-billing'] | //*[@id='billing-detail-view']"));

        private const string SAVE_PAYPAL_AS_DEFAULT_PAYMENT = "Save as my default payment of choice";

        private const string ADYEN_COMMON_ERROR_MESSAGE = "Please try a different payment method, and submit your order again.";

        private const string INDIA_COUNTRY_CODE = "91";

        //Klarna
        private IWebElement KlarnaPayment => WebDriver.FindElement(By.XPath("//*[contains(@class,'adyen-checkout__payment-method__name')][text()='Pay over time with Klarna.']"));

        private IWebElement ContinueToKlarnaPayment => WebDriver.FindElement(By.XPath("//span[@class='adyen-checkout__button__text'][text()='Continue to Pay over time with Klarna.']"));

        private IWebElement KlarnaConfirmAndPayPage => WebDriver.FindElement(By.XPath("//*[text()='Confirm and pay']"));

        private IWebElement PayWithKlarna => WebDriver.FindElement(By.XPath("//*[text()='Pay with']"));

        private IWebElement ContinueKlarna => WebDriver.FindElement(By.XPath("//button[@data-testid='pick-plan']//span[text()='Continue']"));

        private IWebElement ChooseKlarna => WebDriver.FindElement(By.XPath("//*[contains(text(),'Choose how to pay')]"));

        private IWebElement VerifyPhoneNumberPage => WebDriver.FindElement(By.XPath("//*[@id = 'title'][contains(text(),'Welcome to')]"));

        private IWebElement KlarnaPhoneNumberField => WebDriver.FindElement(By.XPath("//input[contains(@id,'phonePasskey')]"));

        private IWebElement KlarnaOTP => WebDriver.FindElement(By.XPath("//input[@id='otp_field']"));

        private IWebElement KlarnaContinueButton => WebDriver.FindElement(By.XPath("//div/span[text()='Continue']"));

        private IWebElement KlarnaChangePayment => WebDriver.FindElement(By.XPath("//span[@id='message-close-button-0__text'][text()='Change payment option']"));

        private IWebElement KlarnaOkayPayment => WebDriver.FindElement(By.XPath("//span[text()='Okay']"));

        private IWebElement KlarnaSavedEmail => WebDriver.FindElement(By.XPath("//span[contains(text(),'customer+')]"));

        private IWebElement KlarnaLogout => WebDriver.FindElement(By.XPath("//span[contains(@id,'logout')]"));

        //AfterPay

        private IWebElement AfterPayLoginPage => WebDriver.FindElement(By.XPath("//*[text()='Welcome back!']"));

        private IWebElement AfterPayPasswordInput => WebDriver.FindElement(By.XPath("//input[@data-testid = 'login-password-input']"));

        private IWebElement AfterPayPayment => WebDriver.FindElement(By.XPath("//*[contains(@class,'adyen-checkout__payment-method__name')][text()='Afterpay']"));

        private IWebElement ContinueToAfterPay => WebDriver.FindElement(By.XPath("//*[@class='adyen-checkout__button__text'][text()='Continue to Afterpay']"));

        private IWebElement AfterPayGetStartedPage => WebDriver.FindElement(By.XPath("//h2[contains(text(),'get started!')]"));

        private IWebElement AfterPayContinueButton => WebDriver.FindElement(By.XPath("//button//span[text()='Continue']"));

        private IWebElement AfterPayMobileInput => WebDriver.FindElement(By.XPath("//input[@data-testid = 'sign-up-mobile-number-input']"));

        private IWebElement AfterCreateAccountPage => WebDriver.FindElement(By.XPath("//*[text()='Create an account']"));

        private IWebElement AfterPayBirthInput => WebDriver.FindElement(By.XPath("//input[@data-testid = 'sign-up-dateOfBirth-input']"));

        private IWebElement AfterPayContinueOnAccountPage => WebDriver.FindElement(By.XPath("//button[@data-testid = 'sign-up-submit-button']"));

        private IWebElement AfterPayVerificationPage => WebDriver.FindElement(By.XPath("//*[text()='Enter verification code']"));

        private IWebElement AfterPayLoader => WebDriver.FindElement(By.XPath("//div[@data-testid='loader-pulse-inner']"));

        private IWebElement AfterPayVerificationCode => WebDriver.FindElement(By.XPath("//*[@data-testid='verify-code-entry']/input"));

        private IWebElement AfterPayConfirmButton => WebDriver.FindElement(By.XPath("//button[@data-dd-action-name='Confirm Checkout Button']//span[text()='Confirm']"));

        private IWebElement AfterPayAddPaymentPage => WebDriver.FindElement(By.XPath("//*[text()='Add a payment method']"));

        private IWebElement AfterPayCreditCardNumberField => WebDriver.FindElement(By.XPath("//input[@data-testid = 'payment-method-cardNumber-input']"));

        private IWebElement AfterPayCreditCardExpiry => WebDriver.FindElement(By.XPath("//input[@data-testid = 'payment-method-cardExpiry-input']"));

        private IWebElement AfterPayCreditCardCVV => WebDriver.FindElement(By.XPath("//input[@data-testid = 'payment-method-cardCvv-input']"));

        private IWebElement AfterPaySchedulePayment => WebDriver.FindElement(By.XPath("//*[text()='Payment schedule']"));

        private IWebElement AfterPayConfirmPayment => WebDriver.FindElement(By.XPath("//button//span[text()='Confirm']"));

        //CashApp Pay

        private IWebElement CashAppPayPayment => WebDriver.FindElement(By.XPath("//*[contains(@class,'adyen-checkout__payment-method__name')][text()='Cash App Pay']"));

        private IWebElement ContinueCashAppPay => WebDriver.FindElement(By.XPath("//div[@class='adyen-checkout__cashapp-button']/div"));

        private IWebElement CashAppQRcodeDialog => WebDriver.FindElement(By.XPath("//div[@class='adyen-checkout__cashapp-button']/div"));

        private IWebElement CashAppApprove => WebDriver.FindElement(By.XPath("//div[@class='PendingSheet_actions__Azo0Z']/button[text()='Approve']"));

        private IWebElement CashAppDecline => WebDriver.FindElement(By.XPath("//div[@class='PendingSheet_actions__Azo0Z']/button[text()='Decline']"));

        private IWebElement CashAppCloseButton => WebDriver.FindElement(By.XPath("//*[@class='CloseBar_bar__ICFu4']/button"));

        private IWebElement CashAppTokenizedPay => WebDriver.FindElement(By.XPath("//span[text()='Cash App Pay']/ancestor::button/parent::div/following-sibling::div//span[contains(text(),'Pay')]"));

        string CashAppQR = "div[data-testid=\"pending-request-qr-code-container\"] > div > img";

        //WeChat Pay
        private IWebElement WeChatPayPayment => WebDriver.FindElement(By.XPath("//*[contains(@class,'adyen-checkout__payment-method__name')][text()='WeChat Pay']"));
        private IWebElement ContinueWeChatPay => WebDriver.FindElement(By.XPath("//li[contains(@class, 'adyen-checkout__payment-method--wechatpayQR')]//button[contains(@class,'adyen-checkout__button--pay')]"));
        private IWebElement WeChatPayQR => WebDriver.FindElement(By.XPath("//div[@id='adyendropin-container']//img[@alt='Scan QR code']"));
        //RazorPay 
        private IWebElement RazorCreditCardPayment => WebDriver.FindElement(By.XPath("//input[@id='razorpay_card']/following-sibling::span"));
        private IWebElement RazorUPIpayment => WebDriver.FindElement(By.XPath("//input[@id='razorpay_upi']/following-sibling::span"));
        private IWebElement RazorNetBankingPayment => WebDriver.FindElement(By.XPath("//input[@id='razorpay_netbanking']/following-sibling::span"));
        private By RazorPayCreditCardComponent => By.XPath("//div[@id='main-container']");
        private IWebElement RazorPayContainer => WebDriver.FindElement(RazorPayCreditCardComponent);
        private IWebElement ContinueToPayment => WebDriver.FindElement(By.XPath("//button[text()='Continue To Payment']"));
        private IWebElement RazorPayMobileInput => WebDriver.FindElement(By.XPath("//div[@id='payment-details-block']"));
        private IList<IWebElement> RazorPayMobileIFrame => WebDriver.FindElements(By.XPath("//iframe[@class='razorpay-checkout-frame']"));
        private IWebElement RazorPaySubscriptionFrame => WebDriver.FindElement(By.XPath("//iframe[@class='razorpay-checkout-frame']"));
        private IWebElement ClickRazorPayButton => WebDriver.FindElement(By.XPath("//button[contains(text(),'Pay using')]"));
        private By RazorCreditCardContainer => By.XPath("//div[@id='add-card-container']");
        private IWebElement SubscriptionRazorCreditCardContainer => WebDriver.FindElement(RazorCreditCardContainer);
        private IWebElement RazorPaySuccessButton => WebDriver.FindElement(By.XPath("//form/button[text()='Success']"));
        private IWebElement RazorPayFailureButton => WebDriver.FindElement(By.XPath("//form/button[text()='Failure']"));
        private IWebElement SubscriptionRazorPayUPIContainer => WebDriver.FindElement(By.XPath("//div[text()='Show QR']"));

        private bool RazorCreditCardPaymentDisplayed() => Extensions.CatchUnavailableElement(() => RazorCreditCardPayment.Displayed, false);
        private bool RazorNetBankingPaymentDisplayed() => Extensions.CatchUnavailableElement(() => RazorNetBankingPayment.Displayed, false);
        private bool RazorUPIPaymentDisplayed() => Extensions.CatchUnavailableElement(() => RazorUPIpayment.Displayed, false);
        private bool RazorPayContainerDisplayed() => Extensions.CatchUnavailableElement(() => RazorPayContainer.Displayed, false);
        private bool RazorPayMobileContainerDisplayed() => Extensions.CatchUnavailableElement(() => ClickRazorPayButton.Displayed, false);
        private bool RazorPaySuccessDisplayed() => Extensions.CatchUnavailableElement(() => RazorPaySuccessButton.Displayed, false);
        private bool SubscriptionCreditCardContainerDisplayed() => Extensions.CatchUnavailableElement(() => SubscriptionRazorCreditCardContainer.Displayed, false);
        private bool SubscriptionRazorPayUPIContainerDisplayed() => Extensions.CatchUnavailableElement(() => SubscriptionRazorPayUPIContainer.Displayed, false);

        private bool CashAppCloseButtonDisplayed() => Extensions.CatchUnavailableElement(() => CashAppCloseButton.Displayed, false);

        private bool KlarnaPaymentDisplayed() => Extensions.CatchUnavailableElement(() => KlarnaPayment.Displayed, false);

        private bool CashAppApproveDisplayed() => Extensions.CatchUnavailableElement(() => CashAppApprove.Displayed, false);

        private bool ContinueCashAppPayDisplayed() => Extensions.CatchUnavailableElement(() => ContinueCashAppPay.Displayed, false);

        private bool ContinueWeChatPayDisplayed() => Extensions.CatchUnavailableElement(() => ContinueWeChatPay.Displayed, false);
        private bool WeChatPayQRDisplayed() => Extensions.CatchUnavailableElement(() => WeChatPayQR.Displayed, false);
        private bool ErrorBlockDisplayed() => Extensions.CatchUnavailableElement(() => ErrorBlock.Displayed, false);

        private bool KlarnaLogoutDisplayed() => Extensions.CatchUnavailableElement(() => KlarnaLogout.Displayed, false);

        private bool AfterPayLoginPageDisplayed() => Extensions.CatchUnavailableElement(() => AfterPayLoginPage.Displayed, false);

        private bool AfterPayContinueOnAccountPageDisplayed() => Extensions.CatchUnavailableElement(() => AfterPayContinueOnAccountPage.Displayed, false);

        private bool AfterPayLoaderDisplayed() => Extensions.CatchUnavailableElement(() => AfterPayLoader.Displayed, false);

        private bool AfterPaySchedulePaymentDisplayed() => Extensions.CatchUnavailableElement(() => AfterPaySchedulePayment.Displayed, false);

        private bool AfterPayAddPaymentPageDisplayed() => Extensions.CatchUnavailableElement(() => AfterPayAddPaymentPage.Displayed, false);

        private bool AfterPayConfirmButtonDisplayed() => Extensions.CatchUnavailableElement(() => AfterPayConfirmButton.Displayed, false);

        private bool AfterPayVerificationPageDisplayed() => Extensions.CatchUnavailableElement(() => AfterPayVerificationPage.Displayed, false);

        private bool AfterCreateAccountPageDisplayed() => Extensions.CatchUnavailableElement(() => AfterCreateAccountPage.Displayed, false);

        private bool AfterPayGetStartedPageDisplayed() => Extensions.CatchUnavailableElement(() => AfterPayGetStartedPage.Displayed, false);

        private bool KlarnaConfirmAndPayPageDisplayed() => Extensions.CatchUnavailableElement(() => PayWithKlarna.Displayed, false);

        private bool ChooseKlarnaKlarnaDisplayed() => Extensions.CatchUnavailableElement(() => ChooseKlarna.Displayed, false);

        private bool KlarnaChangePaymentDisplayed() => Extensions.CatchUnavailableElement(() => KlarnaChangePayment.Displayed, false);

        private bool KlarnaSavedEmailDisplayed() => Extensions.CatchUnavailableElement(() => KlarnaSavedEmail.Displayed, false);

        private bool KlarnaOkayPaymentDisplayed() => Extensions.CatchUnavailableElement(() => KlarnaOkayPayment.Displayed, false);

        private bool KlarnaOTPDisplayed() => Extensions.CatchUnavailableElement(() => KlarnaOTP.Displayed, false);

        private bool VerifyPhoneNumberPageDisplayed() => Extensions.CatchUnavailableElement(() => VerifyPhoneNumberPage.Displayed, false);

        private bool CheckoutPageHeaderDisplayed() => Extensions.CatchUnavailableElement(() => CheckoutPageHeader.Displayed, false);

        private bool QuotePageHeaderDisplayed() => Extensions.CatchUnavailableElement(() => QuotePageHeader.Displayed, false);

        private bool BackButtonDisplayed() => Extensions.CatchUnavailableElement(() => BackButton.Displayed, false);

        private bool ContactSectionDisplayed() => Extensions.CatchUnavailableElement(() => ContactSection.Displayed, false);

        private bool BillingAddressSectionDisplayed() => Extensions.CatchUnavailableElement(() => BillingAddressSection.Displayed, false);

        private bool ErrorBlockSectionDisplayed() => Extensions.CatchUnavailableElement(() => ErrorBlockSection.Displayed, false);

        private bool PaymentMethodDisplayed() => Extensions.CatchUnavailableElement(() => PaymentMethod.Displayed, false);

        private bool PaymentMethodsDisplayed() => Extensions.CatchUnavailableElement(() => PaymentMethods.Displayed, false);

        private bool SavedCardPaymentDisplayed() => Extensions.CatchUnavailableElement(() => SavedCardPayment.Displayed, false);

        private bool TokenizedCardPaymentDisplayed() => Extensions.CatchUnavailableElement(() => TokenizedCardPayment.Displayed, false);

        private bool TokenizedPayPalPaymentDisplayed() => Extensions.CatchUnavailableElement(() => TokenizedPayPalPayment.Displayed, false);

        private bool EditPaymentDisplayed() => Extensions.CatchUnavailableElement(() => EditPayment.Displayed, false);

        private bool SavedPayPalPaymentDisplayed() => Extensions.CatchUnavailableElement(() => SavedPayPalPayment.Displayed, false);

        private bool SavedAliPayPaymentDisplayed() => Extensions.CatchUnavailableElement(() => SavedAliPayPayment.Displayed, false);

        private bool PromoCodeSuccessfulMessageDisplayed() => Extensions.CatchUnavailableElement(() => PromoCodeSuccessfulMessage.Displayed, false);

        private bool VoucherCodeDisplayed() => Extensions.CatchUnavailableElement(() => VoucherCode.Displayed, false);

        private bool VoucherSuccessfulMessageDisplayed() => Extensions.CatchUnavailableElement(() => VoucherSuccessfulMessage.Displayed, false);

        private bool PlaceOrderButtonDisplayed() => Extensions.CatchUnavailableElement(() => PlaceOrderButton.Displayed, false);

        private bool AdyenCreditCardSectionDisplayed() => Extensions.CatchUnavailableElement(() => AdyenCreditCardSection.Displayed, false);

        private bool AdyenCreditCardSaveDetailsButtonDisplayed() => Extensions.CatchUnavailableElement(() => AdyenCreditCardSaveDetailsButton.Displayed, false);

        private bool AdyenCheckoutSpinnerDisplayed() => Extensions.CatchUnavailableElement(() => AdyenCheckoutSpinner.Displayed, false);

        private bool AdyenDropInLoadingDisplayed() => Extensions.CatchUnavailableElement(() => AdyenDropInLoading.Displayed, false);

        private bool AdyenPayPalButtonVisible() => Extensions.CatchUnavailableElement(() => AdyenPaypalButtonDisplayed.Displayed, false);

        private bool AdyenSaveForFutureCheckboxDisplayed() => Extensions.CatchUnavailableElement(() => AdyenSaveForFutureCheckbox.Displayed, false);

        private bool ErrorMessageCloseDisplayed() => Extensions.CatchUnavailableElement(() => ErrorMessageClose.Displayed, false);
        private bool AdyenCreditCardPayButtonDisplayed() => Extensions.CatchUnavailableElement(() => AdyenCreditCardPay.Displayed, false);

        private const string AUTO_SAVE_ADYEN_QA = "By completing your payment, you consent to automatic renewal of your membership and subscriptions. Membership fees, including chapters, and applicable taxes, are billed annually, " +
            "while subscription frequency varies based on your chosen plan. You may turn off this feature or edit your account through your myPMI profile or by contacting Customer Care.";

        private const string AUTO_SAVE_ADYEN_MEMBERSHIP = "By completing your payment, you consent to automatic renewal of your membership, with annual charges for PMI Membership fees, including chapters, and applicable taxes." +
            " You may turn off this feature or edit your account through your myPMI profile or by contacting Customer Care.";

        private const string AUTO_SAVE_ADYEN_SUBSCRIPTION = "By completing your payment, you consent to automatic renewal of your subscriptions, with billing frequency based on your chosen plan. " +
            "You may turn off this feature or edit your account through your myPMI profile or by contacting Customer Care.";

        private readonly WebDriverWait _wait, elementWait;

        private readonly UserSettings _userSettings;

        private readonly CartPage _cartPageNew;

        private readonly Random random;

        private readonly AdyenPayPalPage _adyenPayPalPage;

        public CheckoutPage(IWebDriver driver) : base(driver)
        {
            _adyenPayPalPage = new AdyenPayPalPage(driver);
            _cartPageNew = new CartPage(driver);
            _userSettings = new UserSettings();
            _wait = new WebDriverWait(WebDriver, _userSettings.DefaultExplicitWaitTimeout);
            elementWait = new WebDriverWait(WebDriver, _userSettings.ExplicitElementTimeout);
            random = new Random();
        }

        public override string BaseUrl => _userSettings.BaseUrl;

        public override string RelativePath => $"{_userSettings.CheckoutUrl}/payment";

        public override CheckoutPage VerifyPageLoaded()
        {
            WebDriver.WaitForAllLoadersInvisible();
            WebDriver.LogCurrentUrl();
            _wait.Until(_ => CheckoutPageHeaderDisplayed());
            return base.VerifyPageLoaded();
        }

        public CheckoutPage VerifyQuotePageLoaded()
        {
            WebDriver.WaitForAllLoadersInvisible();
            WebDriver.LogCurrentUrl();
            _wait.Until(_ => QuotePageHeaderDisplayed());
            return this;
        }

        public CheckoutPage ContinueToCart()
        {
            _wait.Until(_ => BackButtonDisplayed());
            BackButton.ScrollAndClickAndWait(WebDriver);
            return this;
        }

        private bool EmailAddressInputDisplayed() => Extensions.CatchUnavailableElement(() => EmailAddressInput.Displayed, false);

        private bool EmailAddressInputClickable() => Extensions.CatchUnavailableElement(() => EmailAddressInput.Displayed && EmailAddressInput.Enabled, false);

        public CheckoutPage EnterContactInformation(string email, StoreContext storeContext, ScenarioContext context, TestData testData)
        {

            OpenContactSection();

            if(context.ScenarioInfo.Tags.Contains("NewEmail"))
            {
                email = testData.User.FirstName + "@pmi.org";
                testData.User.Email = email;
            }

            _wait.Until(_ => EmailAddressInputClickable());

            EmailAddressInput.EmptyTheTextField(WebDriver);
            EmailAddressInput.EnterTextEvent(WebDriver, email);

            if(storeContext.Store == Store.IndiaStore)
                FillOutPhoneInformation();

            UseThisContactInformation.ScrollAndClickAndWait(WebDriver);
            _wait.Until(_ => !EmailAddressInputDisplayed());
            WebDriver.WaitForAllLoadersInvisible();
            if(ErrorMessageCloseDisplayed())
                ErrorMessageClose.ScrollAndClick(WebDriver);

            return this;
        }

        private bool EditContactDisplayed() => Extensions.CatchUnavailableElement(() => EditContactButton.Displayed, false);

        private bool EditBillingDisplayed() => Extensions.CatchUnavailableElement(() => EditBilling.Displayed, false);

        public void OpenContactSection()
        {
            WebDriver.VerifyPage<CheckoutPage>().VerifyPageLoaded();
            _wait.Until(_ => ContactSectionDisplayed());
            if(EditContactDisplayed())
            {
                EditContactButton.ScrollAndClickAndWait(WebDriver);
                WebDriver.WaitForAllLoadersInvisible();
            }
        }

        public void OpenBillingAddressSection()
        {
            _wait.Until(_ => BillingAddressSectionDisplayed() || !EditBillingDisplayed());
            if(EditBillingDisplayed())
                EditBilling.ScrollAndClickAndWait(WebDriver);
        }

        private void FillOutPhoneInformation()
        {
            var random = new Random();
            var phoneNumber = random.Next(1, 1000000).ToString("D8");

            CountryCode.SelectOptionByValue(INDIA_COUNTRY_CODE);
            PhoneNumber.EnterText(WebDriver, "99" + phoneNumber);

        }

        public CheckoutPage EnterBillingAddress(Address address, bool savePaymentForFutureUse, CartContext cartContext, AddressType addressType)
        {
            OpenBillingAddressSection();

            var addressForm = WebDriver.VerifyPage<CheckoutPage>().GetComponent<BillingAddressModal>(this.GetBillingAddressModal);

            if(addressType.Equals(AddressType.Add) || addressType.Equals(AddressType.First))
            {
                addressForm.AddNewBillingAddress(address, savePaymentForFutureUse, cartContext);
            }
            else if(addressType.Equals(AddressType.Edit))
            {
                addressForm.EditBillingAddress(address, savePaymentForFutureUse, cartContext);
            }
            return this;
        }

        public CheckoutPage SelectExistingBillingAddress()
        {
            WebDriver.VerifyPage<CheckoutPage>().GetComponent<BillingAddressModal>(this.GetBillingAddressModal)
                .SelectExistingBillingAddress();
            return this;
        }

        public CheckoutPage ValidateErrorMessageOnBillingAddress()
        {
            Assert.That(ErrorBlockSection.Displayed, Is.True, "Error Block Section is not displayed");
            Assert.That(BlockExclamationMark.Displayed, Is.True, "Error Block Exclamation Mark is not displayed");
            Assert.That(ErrorMessage.Displayed, Is.True, "Error Message is not displayed");
            Assert.That(ErrorMessageClose.Displayed, Is.True, "Clear Error Message Icon (X) is missing");
            return this;
        }

        //Invalid Tax Error
        private string ExpectedInvalidTaxErrorMessage => "Unable to calculate taxes due to an invalid address provided at checkout. Please update your address and submit again.";

        public CheckoutPage ValidateInvalidTaxErrorMessageOnBillingAddress()
        {
            _wait.Until(_ => ErrorBlockSectionDisplayed());
            ValidateErrorMessageOnBillingAddress();
            StringAssert.Contains(ExpectedInvalidTaxErrorMessage, ErrorMessage.Text);
            return this;
        }

        public CheckoutPage ValidateLegalStatementBlock()
        {
            Assert.That(PaymentMethods.Displayed, Is.True, "Payment Methods Section is not displayed");
            Assert.That(LegalStatementBlock.Displayed, Is.True, "Legal Statement Block is not displayed");

            return this;
        }

        public CheckoutPage VerifyPaymentMethods(CartContext cartContext)
        {
            _wait.Until(_ => AdyenPaymentDropInComponentsDisplayed());
            _wait.Until(_ => !AdyenPaymentDropInSpinnerDisplayed());
            if(CartContainsMembershipOrSubscriptionProduct(cartContext))
            {
                Assert.That(AdyenAliPayPaymentDisplayed(), Is.False);
                VerifyAdyenPaymentList();
                if(!cartContext.ProductList.Any(d => d.Title.Contains(MembershipType.Student.ToString())))
                    ValidateLegalStatementBlock();
            }
            else
            {
                Assert.That(AdyenAliPayPaymentDisplayed(), Is.True);
                VerifyAdyenPaymentList();
            }
            return this;
        }

        public void VerifyAdyenPaymentList()
        {
            Assert.That(AdyenCreditCardPayment.Displayed, Is.True);
            //Assert.That(AdyenGooglePayPayment.Displayed, Is.True);
            Assert.That(AdyenPayPalPayment.Displayed, Is.True);
        }

        //This needs a better solution
        private bool CartContainsMembershipOrSubscriptionProduct(CartContext cartContext)
        {
            return cartContext.ProductList.Any(d => d.Title.Contains(MembershipType.Membership.ToString()))
                && !cartContext.ProductList.Any(d => d.Title.Contains(MembershipType.Student.ToString()))
                || cartContext.ProductList.Any(d => d.Title.Contains(MembershipType.Individual.ToString()))
                || cartContext.ProductList.Any(d => d.Title.Contains(MembershipType.IM65.ToString()))
                || cartContext.ProductList.Any(d => d.Title.Contains(MembershipType.Chapter.ToString()))
                || cartContext.ProductList.Any(d => d.Sku?.Contains("DP") ?? false);
        }

        public CheckoutPage AdyenGooglePay()
        {
            Assert.That(SelectGooglePay.Displayed, Is.True, "Google Pay Selection is not displayed");
            Assert.That(GooglepayPaymentImg.Displayed, Is.True, "Google pay payment method img is not displayed");
            Assert.That(GooglePayRadioButton.Displayed, Is.True, "Google pay radio button is missing");
            GooglePayRadioButton.ScrollAndClickAndWait(WebDriver);
            Assert.That(BuyWithGooglePayButton.Displayed, Is.True, "Buy with Google Pay button is not displayed");
            WebDriver.SwitchToNewWindow(BuyWithGooglePayButton, _wait);
            return this;
        }

        private bool SavedPaymentsDisplayed() => SavedPaymentMethods.Count > 0;

        public CheckoutPage EnterCreditCardPaymentDetails(PaymentInfo paymentInfo, bool saveForFutureUse, CartContext cartContext)
        {
            _wait.Until(_ => PaymentMethodsDisplayed());
            if(SavedPaymentsDisplayed())
                AddNewPaymentMethod.ScrollAndClickAndWait(WebDriver);
            CreditCardPayment.ScrollAndClickAndWait(WebDriver);
            WebDriver.VerifyPage<CheckoutPage>().GetComponent<CreditCardModal>(this.GetCreditCardModal)
                .AddCardInfo(paymentInfo, saveForFutureUse, cartContext);
            return this;
        }

        public CheckoutPage EditCreditCardPaymentDetails(PaymentInfo paymentInfo, bool saveForFutureUse, CartContext cartContext)
        {
            _wait.Until(_ => PaymentMethodsDisplayed());
            EditCard.ScrollAndClickAndWait(WebDriver);

            WebDriver.VerifyPage<CheckoutPage>().GetComponent<CreditCardModal>(this.GetCreditCardModal)
                .AddCardInfo(paymentInfo, saveForFutureUse, cartContext);
            return this;
        }

        public CheckoutPage EditCreditCardPaymentDetailsWithNewCreditCard(PaymentInfo paymentInfo, bool saveForFutureUse, CartContext cartContext)
        {
            _wait.Until(_ => PaymentMethodDisplayed());
            EditPayment.ScrollAndClickAndWait(WebDriver);
            EditCard.ScrollAndClickAndWait(WebDriver);

            WebDriver.VerifyPage<CheckoutPage>().GetComponent<CreditCardModal>(this.GetCreditCardModal)
            .AddCardInfo(paymentInfo, saveForFutureUse, cartContext);
            return this;
        }

        public CheckoutPage EnterPayPalPaymentDetails(bool saveForFutureUse, CartContext cartContext)
        {
            _wait.Until(_ => PaymentMethodsDisplayed());
            if(SavedPayPalPaymentDisplayed())
            {
                SelectSavedPayPalPayment();
                return this;
            }

            if(SavedPaymentsDisplayed())
                AddNewPaymentMethod.ScrollAndClickAndWait(WebDriver);

            PayPalPayment.ScrollAndClickAndWait(WebDriver);
            if(saveForFutureUse)
            {
                WebDriver.VerifyPage<CheckoutPage>().GetComponent<CreditCardModal>(this.GetPayPalModal)
                    .HandlePaymentCheckboxes(saveForFutureUse, cartContext);
            }
            UseThisPaymentMethod.ScrollAndClick(WebDriver);
            _wait.Until(_ => SavedPayPalPaymentDisplayed());
            return this;
        }

        public CheckoutPage SavedWorldpayPayment()
        {
            _wait.Until(_ => PaymentMethodsDisplayed());
            if(TokenizedCardPaymentDisplayed())
                SelectSavedCardPayment();

            if(TokenizedPayPalPaymentDisplayed())
                SelectSavedPayPalPayment();

            return this;
        }

        public CheckoutPage SelectSavedCardPayment()
        {
            TokenizedCardPayment.ScrollAndClickAndWait(WebDriver);
            Assert.That(RadioButtonIsChecked.Selected, Is.True);
            UseThisPaymentMethod.ScrollAndClick(WebDriver);
            _wait.Until(_ => SavedCardPaymentDisplayed());
            return this;
        }

        public CheckoutPage SelectSavedPayPalPayment()
        {
            TokenizedPayPalPayment.ScrollAndClickAndWait(WebDriver);
            UseThisPaymentMethod.ScrollAndClick(WebDriver);
            _wait.Until(_ => SavedPayPalPaymentDisplayed());
            return this;
        }

        public CheckoutPage EnterAliPayPaymentDetails()
        {
            _wait.Until(_ => PaymentMethodsDisplayed());
            if(SavedPaymentsDisplayed())
                AddNewPaymentMethod.ScrollAndClickAndWait(WebDriver);
            AliPayPayment.ScrollAndClickAndWait(WebDriver);
            UseThisPaymentMethod.ScrollAndClick(WebDriver);
            _wait.Until(_ => SavedAliPayPaymentDisplayed());
            return this;
        }

        public CheckoutPage InputAliPayPaymentDetails()
        {
            _wait.Until(_ => PaymentMethodsDisplayed());
            AliPayPayment.ScrollAndClickAndWait(WebDriver);
            UseThisPaymentMethod.ScrollAndClick(WebDriver);
            _wait.Until(_ => SavedAliPayPaymentDisplayed());
            return this;
        }

        public CheckoutPage PlaceOrder()
        {
            VerifyAllSectionsAreInCollapsedState();
            PlaceOrderButton.ScrollAndClick(WebDriver);
            return this;
        }

        public CheckoutPage PlaceOrderIsDisabled()
        {
            VerifyAllSectionsAreInCollapsedState();
            Assert.That(PlaceOrderButton.Enabled, Is.False);
            ErrorMessageClose.ScrollAndClick(WebDriver);
            return this;
        }

        public CheckoutPage PlaceOrderForZeroDollar()
        {
            VerifyAllSectionsAreInCollapsedStateWithoutPayment();
            PlaceOrderButton.ScrollAndClick(WebDriver);
            return this;
        }

        public CheckoutPage VerifyAllSectionsAreInCollapsedStateWithoutPayment()
        {
            _wait.Until(_ => EditBilling.Displayed);
            Assert.That(EditContactButton.Displayed, Is.True);
            Assert.That(EditBilling.Displayed, Is.True);
            return this;
        }

        public CheckoutPage VerifyAllSectionsAreInCollapsedState()
        {
            _wait.Until(_ => EditPaymentDisplayed());
            Assert.That(EditContactButton.Displayed, Is.True);
            Assert.That(EditPayment.Displayed, Is.True);
            Assert.That(EditBilling.Displayed, Is.True);
            return this;
        }

        public CheckoutPage PlacePayPalOrder()
        {
            VerifyAllSectionsAreInCollapsedState();
            if(PlaceOrderButtonDisplayed())
            {
                PlaceOrder();
            }
            else
            {
                var worldPaySimulator = OpenPayPalSimulator();
                worldPaySimulator.PaymentAuthorise();
            }
            return this;
        }

        public CheckoutPage CancelPayPalOrder()
        {
            VerifyAllSectionsAreInCollapsedState();
            var worldPaySimulator = OpenPayPalSimulator();
            worldPaySimulator.PaymentCancel();
            WebDriver.VerifyPage<CheckoutPage>();
            Assert.That(ErrorBlock.Displayed, Is.True);
            Console.WriteLine(ErrorBlock.Text.ToString());
            if(EditPaymentDisplayed())
                EditPayment.ScrollAndClickAndWait(WebDriver);
            return this;
        }
        public CheckoutPage PlaceAliPayOrder()
        {
            VerifyAllSectionsAreInCollapsedState();
            var worldPaySimulator = OpenAliPaySimulator();
            worldPaySimulator.AlipayPaymentAuthorise();
            return this;
        }

        public CheckoutPage CancelAliPayOrder()
        {
            VerifyAllSectionsAreInCollapsedState();
            var worldPaySimulator = OpenAliPaySimulator();
            worldPaySimulator.AlipayPaymentCancel();
            WebDriver.VerifyPage<CheckoutPage>();
            Assert.That(ErrorBlock.Displayed, Is.True);
            Console.WriteLine(ErrorBlock.Text.ToString());
            if(EditPaymentDisplayed())
                EditPayment.ScrollAndClickAndWait(WebDriver);
            return this;
        }

        public CheckoutPage PlacePendingOpenAliPayOrder()
        {
            VerifyAllSectionsAreInCollapsedState();
            var worldPaySimulator = OpenAliPaySimulator();
            worldPaySimulator.AlipayPaymentPendingOpen();
            return this;
        }

        private WorldPayPage OpenPayPalSimulator()
        {
            PayWithPayPalButton.ScrollAndClickAndWait(WebDriver);
            WebDriver.LogCurrentUrl();
            return WebDriver.VerifyPage<WorldPayPage>();
        }

        private WorldPayPage OpenAliPaySimulator()
        {
            PayWithAliPayButton.ScrollAndClickAndWait(WebDriver);
            WebDriver.LogCurrentUrl();
            return WebDriver.VerifyPage<WorldPayPage>();
        }

        public CheckoutPage AddPromoCode(string promoCode)
        {
            _cartPageNew.AddPromoCode(promoCode);
            return this;
        }

        public CheckoutPage AddVoucher(string voucherCode)
        {
            _wait.Until(_ => VoucherCodeDisplayed());
            VoucherCode.EnterText(WebDriver, voucherCode);
            _wait.Until(_ => VoucherReedeemButton.Enabled);
            VoucherReedeemButton.ScrollAndClick(WebDriver);
            _wait.Until(_ => VoucherSuccessfulMessageDisplayed());
            VoucherSuccessfulMessage.Text.Contains(voucherCode);
            return this;
        }

        private bool HideCartItemsDisplayed() => Extensions.CatchUnavailableElement(() => HideCartItems.Displayed, false);

        public void ValidateProductsInOrderSummary(CartContext cartContext)
        {
            var productCount = OrderSummaryProductList.Count;
            var expectedCartItems = cartContext.ProductList;

            ViewCartItems.ScrollAndClick(WebDriver);
            _wait.Until(_ => HideCartItemsDisplayed());

            for(var count = 1; count <= productCount; count++)
            {
                var productSummary = WebDriver.InitPage<CheckoutPage>()
                    .GetComponent<OrderSummary>(OrderSummary.RootElement)
                    .GetProductsOnOrderSummary(count);

                var expectedCartItem = expectedCartItems.First(c => c.Title.Equals(productSummary.Title));
                Assert.That(expectedCartItem, Is.Not.Null);

                Assert.That(productSummary.Title, Is.EqualTo(expectedCartItem.Title));

                Currency.CompareAmounts(productSummary.CartPrice, expectedCartItem.CartPrice);
            }
        }

        private bool IsAnyDiscountApplicable(CartContext cartContext) => IsPromoDiscountApplicable(cartContext) || IsVoucherDiscountApplicable(cartContext);

        private bool IsPromoDiscountApplicable(CartContext cartContext) => !string.IsNullOrEmpty(cartContext.PromoCode) && PromoCodeSuccessfulMessageDisplayed();

        private bool IsVoucherDiscountApplicable(CartContext cartContext) => !string.IsNullOrEmpty(cartContext.VoucherCode) && VoucherSuccessfulMessageDisplayed();

        private void ValidateDiscount(CartContext cartContext, OtherCost otherCostSummary)
        {
            if(IsPromoDiscountApplicable(cartContext))
                Currency.CompareAmounts(otherCostSummary.PromoDiscount, cartContext.OtherCost.PromoDiscount);

            if(IsVoucherDiscountApplicable(cartContext))
                Currency.CompareAmounts(otherCostSummary.VoucherDiscount, cartContext.OtherCost.VoucherDiscount);

            Currency.CompareAmounts(otherCostSummary.TotalDiscount, cartContext.OtherCost.TotalDiscount);
        }

        private void ValidateSecondaryCurrency(StoreContext storeContext, User user, Amount totalCost, ref CartContext cartContext)
        {
            try
            {
                var ChinaTotal = UserCartManagement.GetLocalCurrencyTotalPrice(user, storeContext.Store);
                var localCurrencyCost = new Amount(storeContext.SecondaryCurrency, Convert.ToDecimal(ChinaTotal));

                Currency.CompareAmounts(totalCost, localCurrencyCost);
                cartContext.OtherCost.LocalCurrencyTotalCost = localCurrencyCost;
            }
            catch
            {
                Currency.CompareAmounts(totalCost, cartContext.OtherCost.TotalCost);
            }
        }

        private OtherCost GetOtherCostSummary(ref CartContext cartContext)
        {
            var otherCostSummary = WebDriver.InitPage<CartPage>()
                .GetComponent<OrderSummary>(OrderSummary.RootElement)
            .GetOtherCost();

            cartContext.OtherCost.TaxCost = otherCostSummary.TaxCost;

            return otherCostSummary;
        }

        public void ValidateOrderSummary(ref CartContext cartContext, StoreContext storeContext, User user, decimal? promoDiscountPercent)
        {

            var otherCostSummary = GetOtherCostSummary(ref cartContext);

            if(IsAnyDiscountApplicable(cartContext))
            {
                cartContext.CalculateDiscount(storeContext.Currency, promoDiscountPercent);

                ValidateDiscount(cartContext, otherCostSummary);
            }

            cartContext.CalculateTotal(storeContext.Currency);

            if(storeContext.SecondaryCurrency == Currency.ChinaStore.Value)
            {
                ValidateSecondaryCurrency(storeContext, user, otherCostSummary.TotalCost, ref cartContext);
            }
            else
            {
                Currency.CompareAmounts(otherCostSummary.TotalCost, cartContext.OtherCost.TotalCost);
            }
        }

        private bool CpfNumberDisplayed() => Extensions.CatchUnavailableElement(() => CpfNumber.Displayed, false);

        private bool CpfNumberClickable() => Extensions.CatchUnavailableElement(() => CpfNumber.Displayed && CpfNumber.Enabled, false);

        private bool AddCpfButtonClickable() => Extensions.CatchUnavailableElement(() => AddCpfButton.Displayed && AddCpfButton.Enabled, false);

        public CheckoutPage AddCpf(string cpfNumber)
        {
            Assert.That(CpfNumberDisplayed(), Is.True);
            _wait.Until(_ => CpfNumberClickable());

            CpfNumber.ScrollToElement(WebDriver).EnterText(WebDriver, cpfNumber);

            _wait.Until(_ => AddCpfButtonClickable());
            AddCpfButton.Click();
            return this;
        }
        public CheckoutPage ClickEditCartButton()
        {
            WebDriver.VerifyPage<CheckoutPage>();
            EditCart.ScrollAndClickAndWait(WebDriver);
            WebDriver.VerifyPage<CartPage>();
            return this;
        }

        public void VerifyPaymentMethodsNotAvailable()
        {
            _wait.Until(_ => !PaymentMethodsDisplayed());
        }

        private bool Is100PercentDiscount(decimal? PromoDiscountPercent) => PromoDiscountPercent.Equals(decimal.Parse("100") / 100);

        public void VerifySummary(ref CartContext cartContext, StoreContext storeContext, User user, decimal? PromoDiscountPercent)
        {
            if(Is100PercentDiscount(PromoDiscountPercent))
                VerifyPaymentMethodsNotAvailable();

            ValidateProductsInOrderSummary(cartContext);
            //VerifyDonation(cartContext);

            ValidateOrderSummary(ref cartContext, storeContext, user, PromoDiscountPercent);
        }

        public CheckoutPage VerifyAdyenPriceAfterVoucherDiscount(Amount totalCost)
        {
            var payButtonPrice = Currency.ParseAmount(AdyenCreditCardPayButton.Text.Split(' ')[1]);

            Currency.CompareAmounts(payButtonPrice, totalCost);
            return this;
        }

        public CheckoutPage SelectAdyenCreditCardPayment()
        {
            _wait.Until(_ => AdyenCreditCardSectionDisplayed());
            _wait.Until(_ => !AdyenCheckoutSpinnerDisplayed());
            AdyenCreditCardPayment.ScrollAndClickAndWait(WebDriver);
            _wait.Until(_ => !AdyenCheckoutSpinnerDisplayed());

            return this;
        }

        public CheckoutPage EnterAdyenCreditCardDetails(PaymentInfo paymentInfo, CartContext cartContext, StoreContext storeContext, bool saveForFutureUse)
        {
            SelectAdyenCreditCardPayment();

            WebDriver.SwitchToiFrameAndSendText(IFrameCreditCardNumber, AdyenCreditCardNumber, paymentInfo.CreditCard.CardNumber);
            WebDriver.SwitchToiFrameAndSendText(IFrameCreditCardExpiry, AdyenCreditCardExpiryField, paymentInfo.CreditCard.ExpiryDate);
            WebDriver.SwitchToiFrameAndSendText(IFrameCreditCardSecurityCode, AdyenCreditCardSecurityCode, paymentInfo.CreditCard.SecurityCode);
            AdyenCreditCardNameField.EmptyTheFieldAndSendText(WebDriver, paymentInfo.FullName);

            HandleAdyenCreditCardCheckbox(saveForFutureUse, cartContext, storeContext.Store);

            return this;
        }

        public CheckoutPage PlaceAdyenCreditCardOrder(CartContext cartContext)
        {
            if(cartContext.OtherCost.TotalCost.Value != ZeroDollarSubscription) //for zero dollar subscription product
            {
                var TotalAmount = Currency.ParseAmount(AdyenCreditCardPayButton.Text.Split(' ')[1]);
                cartContext.OtherCost.TotalCost.Equals(TotalAmount);
                AdyenCreditCardPayButton.ScrollAndClickAndWait(WebDriver);
            }
            else
            {
                AdyenCreditCardSaveDetailsButtonDisplayed();
                AdyenCreditCardSaveDetailsButton.ScrollAndClickAndWait(WebDriver);
            }

            _wait.Until(_ => !AdyenDropInLoadingDisplayed());
            return this;
        }

        public CheckoutPage AdyenCreditCardPayButtonIsDisabled()
        {
            Assert.That(AdyenCreditCardPayButtonDisplayed(), Is.False);
            return this;
        }

        public void HandleAdyenCreditCardCheckbox(bool savePaymentForFutureUse, CartContext cartContext, string store)
        {
            bool isMembershipOrSubscription = cartContext.ProductList.Any(d =>
                    d.Title.Contains(MembershipType.Membership.ToString()) ||
                    d.Title.Contains(MembershipType.Student.ToString()) ||
                    (d.Sku?.Contains("DP") ?? false)
                );

            if(isMembershipOrSubscription && store != Store.IndiaStore)
            {
                Assert.That(AdyenAutoSaveForFutureMessage.Displayed, Is.True);
            }
            else if(savePaymentForFutureUse)
            {
                AdyenSaveForFutureCheckbox.ScrollAndClick(WebDriver);
            }
        }

        public CheckoutPage HandleAdyen3DSPopup()
        {
            _wait.Until(_ => !AdyenDropInLoadingDisplayed());
            _wait.Until(_ => !AdyenCheckoutSpinnerDisplayed());
            WebDriver.SwitchToiFrameAndSendText(IFrameAdyen3DS, Adyen3DSpasswordField, _userSettings.Adyen3DSpassword);
            WebDriver.SwitchTo().Frame(IFrameAdyen3DS);
            SubmitAdyen3DS.ScrollAndClick(WebDriver);
            WebDriver.SwitchTo().DefaultContent();
            return this;
        }

        public CheckoutPage OpenAdyenPayPalSimulator()
        {
            AdyenPayPalPayment.ScrollAndClick(WebDriver);
            _wait.Until(_ => !AdyenCheckoutSpinnerDisplayed());
            _wait.Until(_ => AdyenPayPalButtonVisible());
            WebDriver.SwitchToNewWindow(AdyenPaypalButton, _wait);
            return this;
        }

        public CheckoutPage PlaceAdyenPayPalOrder(TestData testData, ScenarioContext context)
        {
            OpenAdyenPayPalSimulator();
            WebDriver.VerifyPage<AdyenPayPalPage>().LoginToAdyenPayPal(testData, context);
            return this;
        }

        public CheckoutPage OpenAdyenAliPaySimulator()
        {
            _wait.Until(_ => !AdyenDropInLoadingDisplayed());
            AdyenAliPayPayment.ScrollAndClickAndWait(WebDriver);
            ContinueToAliPay.ScrollAndClickAndWait(WebDriver);
            return this;
        }

        public CheckoutPage VerifySavedAddressDetails(TestData testData)
        {
            StringAssert.StartsWith(SavedAddress.Text, testData.GetAddressForCountry().AddressLine1);
            StringAssert.Contains(SavedAddress.Text, testData.GetAddressForCountry().City);
            StringAssert.Contains(SavedAddress.Text, testData.GetAddressForCountry().ZipCode);
            return this;
        }

        private string checkoutPageCardLast4Digits;
        public string GetCheckoutPageCardLast4Digits(PaymentInfo paymentInfo)
        {
            var cardnumber = Regex.Replace(SavedCard.Text, @"\s+|\•", "");//Remove white spaces in the string
            checkoutPageCardLast4Digits = cardnumber.Substring(cardnumber.Length - 4);
            return checkoutPageCardLast4Digits;
        }

        public CheckoutPage VerifySavedPaymentType(PaymentInfo paymentInfo)
        {
            var ordeConfirmationPage = new OrderConfirmationPage(WebDriver);
            var confirmationPageCardLast4Digits = ordeConfirmationPage.GetCardLast4Digits(paymentInfo);
            GetCheckoutPageCardLast4Digits(paymentInfo);
            Assert.That(confirmationPageCardLast4Digits, Is.EqualTo(checkoutPageCardLast4Digits));
            return this;
        }

        public CheckoutPage PlaceOrderForTokenizedAdyenCreditCard(PaymentInfo paymentInfo)
        {
            WebDriver.SwitchToiFrameAndSendText(IFrameCreditCardSecurityCode, AdyenCreditCardSecurityCode, paymentInfo.CreditCard.SecurityCode);
            AdyenCreditCardPayButton.ScrollAndClick(WebDriver);
            return this;
        }

        public CheckoutPage PlaceKlarnaOrder(string phoneNumber)
        {
            _wait.Until(_ => KlarnaPaymentDisplayed());
            KlarnaPayment.ScrollAndClick(WebDriver);
            ContinueToKlarnaPayment.ScrollAndClick(WebDriver);
            InputPhoneNumberAndOTPForKlarna(phoneNumber);
            _wait.Until(_ => ChooseKlarnaKlarnaDisplayed());
            ContinueKlarna.ScrollAndClick(WebDriver);
            _wait.Until(_ => KlarnaConfirmAndPayPageDisplayed());
            PayWithKlarna.ScrollAndClick(WebDriver);
            return this;
        }

        public CheckoutPage InputPhoneNumberAndOTPForKlarna(string phoneNumber)
        {
            var OtpNumber = random.Next(1, 100000).ToString("D6");
            try
            {
                elementWait.Until(_ => KlarnaConfirmAndPayPageDisplayed());
                if(KlarnaConfirmAndPayPage.Displayed)
                    EditSavedKlarnaProfile();
            }
            catch { }
            _wait.Until(_ => VerifyPhoneNumberPageDisplayed());
            KlarnaPhoneNumberField.EnterText(WebDriver, phoneNumber);
            KlarnaContinueButton.ScrollAndClickAndWait(WebDriver);
            _wait.Until(_ => KlarnaOTPDisplayed());
            KlarnaOTP.EnterText(WebDriver, OtpNumber);
            return this;
        }

        public CheckoutPage PlaceInvalidKlarnaOrder(string phoneNumber)
        {
            PlaceKlarnaOrder(phoneNumber);
            VerifyKlarnaOrderFailed();
            return this;
        }

        public CheckoutPage VerifyKlarnaOrderFailed()
        {
            _wait.Until(_ => KlarnaChangePaymentDisplayed());
            KlarnaChangePayment.ScrollAndClickAndWait(WebDriver);
            _wait.Until(_ => ChooseKlarnaKlarnaDisplayed());
            ContinueKlarna.ScrollAndClick(WebDriver);
            _wait.Until(_ => KlarnaOkayPaymentDisplayed());
            KlarnaOkayPayment.ScrollAndClickAndWait(WebDriver);
            _wait.Until(_ => ErrorBlockDisplayed());
            ErrorBlock.Text.Equals(ADYEN_COMMON_ERROR_MESSAGE);
            return this;
        }

        public CheckoutPage EditSavedKlarnaProfile()
        {
            _wait.Until(_ => KlarnaSavedEmailDisplayed());
            KlarnaSavedEmail.ScrollAndClick(WebDriver);
            _wait.Until(_ => KlarnaLogoutDisplayed());
            KlarnaLogout.ScrollAndClickAndWait(WebDriver);
            _wait.Until(_ => KlarnaLogoutDisplayed());
            KlarnaLogout.ScrollAndClickAndWait(WebDriver);
            return this;
        }

        public CheckoutPage PlaceAfterPayOrder(User user)
        {
            SelectAfterPayPayment();
            CompleteAfterPayLogin(user);
            CompleteAfterPayPaymentSelection();
            CompleteAfterPaySchedulePaymentSection();
            return this;
        }

        public CheckoutPage PlaceWeChatOrder()
        {
            SelectWeChatPayPayment();
            return this;
        }

        public CheckoutPage PlaceCashAppPayOrder()
        {
            SelectCashAppPayPayment();
            var QRUrl = WaitForQRCodeAndGetTheURL();
            var QRImagePath = ExtractQRCodeImage(QRUrl);
            ScanQRCodeImage(QRImagePath);
            ApproveCashAppPayment();
            DeleteSavedQRImages(QRImagePath);
            return this;
        }

        public CheckoutPage SelectAfterPayPayment()
        {
            AfterPayPayment.ScrollAndClick(WebDriver);
            ContinueToAfterPay.ScrollAndClick(WebDriver);
            return this;
        }

        public CheckoutPage SelectCashAppPayPayment()
        {
            CashAppPayPayment.ScrollAndClick(WebDriver);
            _wait.Until(_ => ContinueCashAppPayDisplayed());
            ContinueCashAppPayPayment();

            return this;
        }

        public CheckoutPage SelectWeChatPayPayment()
        {
            WeChatPayPayment.ScrollAndClick(WebDriver);
            _wait.Until(_ => ContinueWeChatPayDisplayed());
            ContinueWeChatPayPayment();

            return this;
        }

        public CheckoutPage PayTokenizedCashAppPayPayment()
        {
            CashAppTokenizedPay.ScrollAndClick(WebDriver);
            return this;
        }

        public CheckoutPage ContinueWeChatPayPayment()
        {
            ContinueWeChatPay.ScrollAndClick(WebDriver);
            _wait.Until(_ => WeChatPayQRDisplayed());
            return this;
        }

        public CheckoutPage ContinueCashAppPayPayment()
        {
            ISearchContext shadow = ContinueCashAppPay.GetShadowRoot();
            IWebElement CashAppButton = shadow.FindElement(By.CssSelector("button[title='Cash App Pay']"));
            CashAppButton.ScrollAndClick(WebDriver);
            return this;
        }

        public string WaitForQRCodeAndGetTheURL()
        {
            ISearchContext shadow = ContinueCashAppPay.GetShadowRoot();
            _wait.Until(_ => Extensions.CatchUnavailableElement(() => shadow.FindElement(By.CssSelector(CashAppQR)).Displayed, false));
            var QRUrl = shadow.FindElement(By.CssSelector(CashAppQR)).GetAttribute("src");
            return QRUrl;
        }

        public void ScanQRCodeImage(string imagePath)
        {
            var QRImagePath = $"{imagePath}" + @"\" + PaymentTypes.CashAppPay.ToString() + ".png";
            Bitmap bitmap = (Bitmap)Image.FromFile(QRImagePath);
            IBarcodeReader reader = new BarcodeReader();
            var result = reader.Decode(bitmap);
            string url = result.Text;
            WebDriver.GoTo(url, "");
        }

        public void ApproveCashAppPayment()
        {
            _wait.Until(_ => CashAppApproveDisplayed());
            CashAppApprove.ScrollAndClick(WebDriver);
            _wait.Until(_ => CashAppCloseButtonDisplayed());
            CashAppCloseButton.ScrollAndClick(WebDriver);
            WebDriver.VerifyPage<CheckoutPage>();
            CashAppPayPayment.ScrollAndClick(WebDriver);
        }

        public void DeleteSavedQRImages(string imagePath)
        {
            string[] files = Directory.GetFiles(imagePath);
            foreach(string file in files)
            {
                File.Delete(file);
            }
        }

        public string ExtractQRCodeImage(string url)
        {
            var QRImagePath = $"{projectPath}" + @"\" + PaymentTypes.CashAppPay.ToString();
            WebDriver.GoTo(url, "").TakeScreenshot(QRImagePath, PaymentTypes.CashAppPay.ToString());
            return QRImagePath;
        }

        public CheckoutPage CompleteAfterPayCreateAccountSection()
        {
            var MobileNumber = random.Next(1, **********).ToString("D10");
            _wait.Until(_ => AfterCreateAccountPageDisplayed());
            AfterPayMobileInput.EmptyTheFieldAndSendText(WebDriver, GenerateRandomUSAPhoneNumber());
            AfterPayBirthInput.EnterText(WebDriver, GenerateRandomDOB());
            AfterPayContinueOnAccountPage.ScrollAndClick(WebDriver);
            return this;
        }

        public CheckoutPage CompleteAfterPayLogin(User user)
        {
            _wait.Until(_ => AfterPayLoginPageDisplayed());
            AfterPayPasswordInput.EmptyTheFieldAndSendText(WebDriver, user.Password);
            AfterPayContinueButton.ScrollAndClick(WebDriver);
            return this;
        }

        private string GenerateRandomDOB()
        {
            var date = random.Next(1, 28);
            var month = random.Next(1, 12);
            var year = random.Next(1980, 2000);
            DateTime randomDate = new DateTime(year, month, date);
            string dateString = randomDate.ToString("MM/dd/yyyy");
            return dateString;
        }

        private string GenerateRandomUSAPhoneNumber()
        {
            int areaCode = random.Next(200, 1000);// Generate area code (3 digits, not starting with 0 or 1)
            int centralOfficeCode = random.Next(200, 1000);// Generate central office code (3 digits, not starting with 0 or 1)           
            int lineNumber = random.Next(1000, 10000);// Generate line number (4 digits)
            string phoneNumber = $"({areaCode}) {centralOfficeCode}-{lineNumber}";
            return phoneNumber;
        }

        public CheckoutPage CompleteAfterPayOTPVerificationSection()
        {
            _wait.Until(_ => !AfterPayLoaderDisplayed());
            var OtpNumber = random.Next(1, 100000).ToString("D6");
            _wait.Until(_ => AfterPayVerificationPageDisplayed());
            AfterPayVerificationCode.EmptyTheFieldAndSendText(WebDriver, OtpNumber);
            return this;
        }

        public CheckoutPage CompleteAfterPayPaymentSelection()
        {
            _wait.Until(_ => AfterPayConfirmButtonDisplayed());
            AfterPayConfirmButton.ScrollAndClick(WebDriver);
            return this;
        }

        public CheckoutPage CompleteAfterPayCreditCardPaymentSection(PaymentInfo paymentInfo)
        {
            _wait.Until(_ => AfterPayAddPaymentPageDisplayed());
            AfterPayCreditCardNumberField.EnterText(WebDriver, paymentInfo.CreditCard.CardNumber);
            AfterPayCreditCardExpiry.EnterText(WebDriver, paymentInfo.CreditCard.ExpiryDate);
            AfterPayCreditCardCVV.EnterText(WebDriver, paymentInfo.CreditCard.SecurityCode);
            AfterPayContinueButton.ScrollAndClick(WebDriver);
            return this;
        }

        public CheckoutPage CompleteAfterPaySchedulePaymentSection()
        {
            _wait.Until(_ => AfterPaySchedulePaymentDisplayed());
            AfterPayConfirmPayment.ScrollAndClick(WebDriver);
            return this;
        }


        //Adyen ACH Payment

        private IWebElement AdyenACHNameField => WebDriver.FindElement(By.XPath("//div[@class='adyen-checkout__ach']//input[contains(@id,'adyen-checkout-holderName')]"));

        private By AdyenACHBankAccountNumber => By.XPath("//input[contains(@id,'adyen-checkout-bankAccountNumber')]");

        private By AdyenACHBankRoutingNumber => By.XPath("//input[contains(@id,'adyen-checkout-bankLocationId')]");


        private IWebElement IFrameACHBankAccountNumber => WebDriver.FindElement(By.XPath("//iframe[@title='Iframe for bank account number']"));

        private IWebElement IFrameACHBankRoutingNumber => WebDriver.FindElement(By.XPath("//iframe[@title='Iframe for bank routing number']"));


        private IWebElement AdyenACHPayment => WebDriver.FindElement(By.XPath("//*[contains(@class,'adyen-checkout__payment-method__name')][text()='ACH Direct Debit']"));

        private IWebElement AdyenACHConfirmPurchase => WebDriver.FindElement(By.XPath("//div[@class='adyen-checkout__ach']//button//span[text()='Confirm purchase']"));


        public CheckoutPage SelectAdyenACHPayment()
        {
            _wait.Until(_ => !AdyenDropInLoadingDisplayed());
            AdyenACHPayment.ScrollAndClickAndWait(WebDriver);

            return this;
        }

        public CheckoutPage EnterAdyenACHPaymentDetails(AdyenCredential adyenCredential, PaymentInfo paymentInfo, CartContext cartContext, Address address)
        {

            SelectAdyenACHPayment();

            var achAddressForm = WebDriver.VerifyPage<CheckoutPage>().GetComponent<BillingAddressModal>(this.GetBillingAddressModal);
            AdyenACHNameField.EmptyTheFieldAndSendText(WebDriver, paymentInfo.FullName);
            WebDriver.SwitchToiFrameAndSendText(IFrameACHBankAccountNumber, AdyenACHBankAccountNumber, adyenCredential.ACHAccountNumber);
            WebDriver.SwitchToiFrameAndSendText(IFrameACHBankRoutingNumber, AdyenACHBankRoutingNumber, adyenCredential.ACHRoutingNumber);
            achAddressForm.FillInACHPaymentAddressValues(address);
            AdyenACHConfirmPurchase.ScrollAndClick(WebDriver);

            return this;
        }

        private IWebElement SelectAmazonPay => WebDriver.FindElement(By.XPath("//button[@class='adyen-checkout__payment-method__header__title']//span[contains(text(), 'Amazon Pay')]"));

        private IWebElement AmazonPayPaymentImg => WebDriver.FindElement(By.XPath("//span[contains(text(), 'Google Pay')]/ancestor::button//span/img"));

        private IWebElement AmazonPayRadioBtn => WebDriver.FindElement(By.XPath("//span[contains(text(), 'Amazon Pay')]/ancestor::button[@role='radio']/span"));

        private IWebElement AmazonPayDropInComponent => WebDriver.FindElement(By.XPath("//div[@id='adyendropin-container']/div/ul/li[6]/div[2]"));

        private IWebElement AmazonPayBTN => WebDriver.FindElement(By.XPath("//div[@id='amazonPayButton']"));

        private IWebElement SwitchToAmazonPay => WebDriver.FindElement(By.XPath("//h1[contains(text(), 'Sign in with Amazon')]"));

        private bool SwitchToAmazonPayDisplayed() => Extensions.CatchUnavailableElement(() => SwitchToAmazonPay.Displayed, false);

        private IWebElement AmazonPayUsername => WebDriver.FindElement(By.XPath("//input[@id='ap_email']"));

        public CheckoutPage CheckoutWithAdyenAmazonPay()
        {
            Assert.That(SelectAmazonPay.Displayed, Is.True, "Amazon Pay selection is not displayed");
            Assert.That(AmazonPayPaymentImg.Displayed, Is.True, "Amazonpay payment method img is not displayed");
            Assert.That(AmazonPayRadioBtn.Displayed, Is.True, "Amazon pay radio button is missing");
            AmazonPayRadioBtn.ScrollAndClickAndWait(WebDriver);
            OpenAmazonPay();
            AmazonPayUsername.ScrollAndClick(WebDriver);

            return this;
        }

        private void OpenAmazonPay()
        {
            var _shortWait = new WebDriverWait(WebDriver, TimeSpan.FromSeconds(5));
            var windowHandles = new List<string>();
            _wait.Until(_ =>
            {
                AmazonPayDropInComponent.ScrollAndClickAndWait(WebDriver);
                windowHandles = new List<string>(WebDriver.WindowHandles);

                try
                {
                    _shortWait.Until(h =>
                    {
                        return windowHandles.Count > 1;
                    });
                }
                catch(WebDriverTimeoutException)
                {
                    return windowHandles.Count > 1;
                }

                return windowHandles.Count > 1;
            });

            WebDriver.SwitchTo().Window(windowHandles[windowHandles.Count - 1]);
            _wait.Until(_ => SwitchToAmazonPayDisplayed());
        }

        private IWebElement NavigateToAmazonPay => WebDriver.FindElement(By.XPath("//div[@class='adyen-checkout__amazonpay']/div"));

        public CheckoutPage ContinueToAmazonPay()
        {
            ISearchContext shadow = NavigateToAmazonPay.GetShadowRoot();
            IWebElement AmazonPayButton = shadow.FindElement(By.XPath("//picture[@class='amazonpay-button-logo']"));
            AmazonPayButton.ScrollAndClick(WebDriver);
            return this;
        }

        public CheckoutPage PlaceRazorPayCreditCardOrderForNonSubscription(PaymentInfo paymentInfo)
        {
            SelectRazorPayCreditCardOption();
            NavigateToRazorPaySubscriptionForm();
            _wait.Until(_ => RazorPayContainerDisplayed());
            WebDriver.InitPage<CheckoutPage>().GetComponent<RazorPayPaymentContainer>(RazorPayCreditCardComponent)
                .CompleteCreditCardFormForNonSubscription(paymentInfo);
            ApproveRazorPayPayment();
            return this;
        }

        public CheckoutPage PlaceRazorPayCreditCardOrderForSubscription(PaymentInfo paymentInfo)
        {
            SelectRazorPayCreditCardOption();
            NavigateToRazorPaySubscriptionForm();
            _wait.Until(_ => SubscriptionCreditCardContainerDisplayed());
            WebDriver.InitPage<CheckoutPage>().GetComponent<RazorPayPaymentContainer>(RazorCreditCardContainer)
                .CompleteCreditCardFormForSubscription(paymentInfo);
            ApproveRazorPayPayment();
            return this;
        }

        public CheckoutPage PlaceRazorPayUPIOrderForSubscription()
        {
            SelectUPIPaymentOption();
            NavigateToRazorPaySubscriptionForm();
            _wait.Until(_ => SubscriptionRazorPayUPIContainerDisplayed());
            SubscriptionRazorPayUPIContainer.ScrollAndClick(WebDriver);
            WebDriver.SwitchTo().DefaultContent();
            return this;
        }

        public CheckoutPage PlaceRazorPayUPIOrderForNonSubscription()
        {
            SelectUPIPaymentOption();
            NavigateToRazorPaySubscriptionForm();
            _wait.Until(_ => RazorPayContainerDisplayed());
            WebDriver.InitPage<CheckoutPage>().GetComponent<RazorPayPaymentContainer>(RazorPayCreditCardComponent)
                .CompleteUPIPayment();
            WebDriver.SwitchTo().DefaultContent();
            return this;
        }

        public CheckoutPage PlaceRazorPayNetBankingOrderForNonSubscription()
        {
            SelectNetBankingPaymentOption();
            NavigateToRazorPaySubscriptionForm();
            _wait.Until(_ => RazorPayContainerDisplayed());
            WebDriver.InitPage<CheckoutPage>().GetComponent<RazorPayPaymentContainer>(RazorPayCreditCardComponent)
                .CompleteNetBankingPayment();
            ApproveRazorPayPayment();
            return this;
        }

        public CheckoutPage SelectRazorPayCreditCardOption()
        {
            _wait.Until(_ => RazorCreditCardPaymentDisplayed());
            RazorCreditCardPayment.ScrollAndClick(WebDriver);
            ContinueToPayment.ScrollAndClick(WebDriver);
            return this;
        }
        public CheckoutPage SelectUPIPaymentOption()
        {
            _wait.Until(_ => RazorUPIPaymentDisplayed());
            RazorUPIpayment.ScrollAndClick(WebDriver);
            ContinueToPayment.ScrollAndClick(WebDriver);
            return this;
        }

        public CheckoutPage SelectNetBankingPaymentOption()
        {
            _wait.Until(_ => RazorNetBankingPaymentDisplayed());
            RazorNetBankingPayment.ScrollAndClick(WebDriver);
            ContinueToPayment.ScrollAndClick(WebDriver);
            return this;
        }


        public CheckoutPage ApproveRazorPayPayment()
        {
            _wait.Until(_ => WebDriver.WindowHandles.Count > 1);
            WebDriver.SwitchTo().Window(WebDriver.WindowHandles.Last());
            _wait.Until(_ => RazorPaySuccessDisplayed());
            RazorPaySuccessButton.ScrollAndClick(WebDriver);
            WebDriver.SwitchTo().Window(WebDriver.WindowHandles.First());
            return this;
        }

        public CheckoutPage FailRazorPayPayment()
        {
            WebDriver.SwitchTo().Window(WebDriver.WindowHandles.Last());
            _wait.Until(_ => RazorPaySuccessDisplayed());
            RazorPayFailureButton.ScrollAndClick(WebDriver);
            WebDriver.SwitchTo().Window(WebDriver.WindowHandles.First());
            return this;
        }

        public CheckoutPage NavigateToRazorPaySubscriptionForm()
        {
            IWebElement frame;
            try
            {
                WebDriver.SwitchTo().Frame(RazorPaySubscriptionFrame);
                elementWait.Until(_ => RazorPayContainerDisplayed());
                if(RazorPayContainer.Displayed)
                    return this;
                
            }
            catch
            {
                WebDriver.SwitchTo().DefaultContent();
                WebDriver.SwitchTo().Frame(frame= RazorPayMobileIFrame.Count > 1 ? RazorPayMobileIFrame[1] : RazorPayMobileIFrame[0]);
                _wait.Until(_ => RazorPayMobileContainerDisplayed());
                ClickRazorPayButton.ScrollAndClick(WebDriver); 
            }
            return this;
        }

        public CheckoutPage VerifyRazorPayPayments()
        {
            _wait.Until(_ => RazorCreditCardPaymentDisplayed());
            RazorCreditCardPayment.ScrollAndClick(WebDriver);
            return this;
        }
    }
}