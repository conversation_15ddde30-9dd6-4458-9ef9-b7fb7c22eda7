import { useQuery } from '@tanstack/react-query';
import { useMemo } from 'react';

import { getAvailablePaymentMethodCodes } from 'Feature/NewCheckoutFow/api';
import { PaymentMethodCodes, RazorpayCodes } from 'Feature/NewCheckoutFow/constants';
import { getAvailablePaymentMethodCodesKey } from 'Feature/NewCheckoutFow/utils';

const RPOrder = new Map([
  [PaymentMethodCodes.RazorpayCreditCard, 0],
  [PaymentMethodCodes.RazorpayUpi, 1],
  [PaymentMethodCodes.RazorpayNetbanking, 2],
]);

export const useRazorPay = () => {
  const { data, isLoading, isFetched } = useQuery(getAvailablePaymentMethodCodesKey(), getAvailablePaymentMethodCodes);

  const isActive = useMemo(() => data?.some((code) => RazorpayCodes.includes(code.toLowerCase())), [data]);

  const availablePaymentMethods = useMemo(
    () =>
      data
        ?.filter((pm) => RazorpayCodes.includes(pm.toLowerCase()))
        .sort((code1, code2) => RPOrder.get(code1) - RPOrder.get(code2)) ?? [],
    [data],
  );

  return {
    isLoading,
    isFetched,
    isActive,
    availablePaymentMethods,
  };
};
