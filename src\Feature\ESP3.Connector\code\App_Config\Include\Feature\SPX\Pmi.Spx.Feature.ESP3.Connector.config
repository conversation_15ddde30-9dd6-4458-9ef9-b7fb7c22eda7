﻿<?xml version="1.0" encoding="utf-8"?>
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/" xmlns:role="http://www.sitecore.net/xmlconfig/role/" xmlns:espversion="http://www.sitecore.net/xmlconfig/espversion/">
  <sitecore>   
    <services>
      <configurator type="Pmi.Spx.Feature.ESP3.Connector.RegisterDependencies, Pmi.Spx.Feature.ESP3.Connector" />
    </services>

    <spx.feature.esp3.connect.settings role:require="Standalone or ContentDelivery or ContentManagement">
      <api type="Pmi.Spx.Feature.ESP3.Connector.Configuration.Esp3ApiSettings, Pmi.Spx.Feature.ESP3.Connector">
        <apiHost>https://apim.int.pmi.org</apiHost>
        <ocpSubscriptionKey>Ocp-Apim-Subscription-Key</ocpSubscriptionKey>
        <ocpSubscriptionValue>ee15f31544764de1bf9c876433ae7aeb</ocpSubscriptionValue>
      </api>
      <token type="Pmi.Spx.Foundation.Security.Configuration.ClientCredentialsFlowSettings, Pmi.Spx.Foundation.Security">
        <authority>https://idp.int.pmi.org</authority>
        <grantType>client_credentials</grantType>
        <clientId>spx_client_c77b113fbcdc420e97f631c0a32ec56a</clientId>
        <clientSecret>Password1</clientSecret>
        <tokenEndpoint>/connect/token</tokenEndpoint>
        <scope>PRFSVC REFSVC WPSVC CERTAPI PMTSVC</scope>
      </token>
    </spx.feature.esp3.connect.settings>

    <pipelines>
      <!--Payments:-->
      <spx.foundation.profile.paymentmethods.getPaymentMethods>
        <processor type="Pmi.Spx.Feature.ESP3.Connector.Pipelines.PaymentMethods.GetPaymentMethods, Pmi.Spx.Feature.ESP3.Connector" resolve="true" />
      </spx.foundation.profile.paymentmethods.getPaymentMethods>
      <spx.foundation.profile.paymentmethods.createPaymentMethod>
        <processor type="Pmi.Spx.Feature.ESP3.Connector.Pipelines.PaymentMethods.CreatePaymentMethod, Pmi.Spx.Feature.ESP3.Connector" resolve="true" />
      </spx.foundation.profile.paymentmethods.createPaymentMethod>
      <spx.foundation.profile.paymentmethods.updatePaymentMethod>
        <processor type="Pmi.Spx.Feature.ESP3.Connector.Pipelines.PaymentMethods.UpdatePaymentMethod, Pmi.Spx.Feature.ESP3.Connector" resolve="true" />
      </spx.foundation.profile.paymentmethods.updatePaymentMethod>

      <!--Addresses:-->
      <spx.foundation.profile.addresses.getAddresses>
        <processor type="Pmi.Spx.Feature.ESP3.Connector.Pipelines.Addresses.GetAddresses, Pmi.Spx.Feature.ESP3.Connector" resolve="true"
          patch:instead="processor[@type='Pmi.Spx.Foundation.Profile.Pipelines.Addresses.GetAddressesExternal, Pmi.Spx.Foundation.Profile']" />
      </spx.foundation.profile.addresses.getAddresses>
      <spx.foundation.profile.addresses.getAddress>
        <processor type="Pmi.Spx.Feature.ESP3.Connector.Pipelines.Addresses.GetAddress, Pmi.Spx.Feature.ESP3.Connector" resolve="true"
          patch:instead="processor[@type='Pmi.Spx.Foundation.Profile.Pipelines.Addresses.GetAddressExternal, Pmi.Spx.Foundation.Profile']" />
      </spx.foundation.profile.addresses.getAddress>
      <spx.foundation.profile.addresses.createAddress>
        <processor type="Pmi.Spx.Feature.ESP3.Connector.Pipelines.Addresses.CreateAddress, Pmi.Spx.Feature.ESP3.Connector" resolve="true"
          patch:instead="processor[@type='Pmi.Spx.Foundation.Profile.Pipelines.Addresses.CreateAddressExternal, Pmi.Spx.Foundation.Profile']" />
      </spx.foundation.profile.addresses.createAddress>
      <spx.foundation.profile.addresses.updateAddress>
        <processor type="Pmi.Spx.Feature.ESP3.Connector.Pipelines.Addresses.UpdateAddress, Pmi.Spx.Feature.ESP3.Connector" resolve="true"
          patch:instead="processor[@type='Pmi.Spx.Foundation.Profile.Pipelines.Addresses.UpdateAddressExternal, Pmi.Spx.Foundation.Profile']" />
      </spx.foundation.profile.addresses.updateAddress>
      <spx.foundation.profile.addresses.setAddressType>
        <processor type="Pmi.Spx.Feature.ESP3.Connector.Pipelines.Addresses.SetAddressType, Pmi.Spx.Feature.ESP3.Connector" resolve="true"
          patch:instead="processor[@type='Pmi.Spx.Foundation.Profile.Pipelines.Addresses.SetAddressTypeExternal, Pmi.Spx.Foundation.Profile']" />
      </spx.foundation.profile.addresses.setAddressType>
      <spx.foundation.profile.addresses.setPreferredAddress>
        <processor type="Pmi.Spx.Feature.ESP3.Connector.Pipelines.Addresses.SetPreferredAddress, Pmi.Spx.Feature.ESP3.Connector" resolve="true"
          patch:instead="processor[@type='Pmi.Spx.Foundation.Profile.Pipelines.Addresses.SetPreferredAddressExternal, Pmi.Spx.Foundation.Profile']" />
      </spx.foundation.profile.addresses.setPreferredAddress>

      <!-- CicsWorldpay -->
      <spx.foundation.payment.orderPayment.cancelTransaction>
	      <processor type="Pmi.Spx.Feature.ESP3.Connector.Pipelines.CicsWorldpay.WorldpayAuthCancelRefund, Pmi.Spx.Feature.ESP3.Connector" resolve="true" />
      </spx.foundation.payment.orderPayment.cancelTransaction>
    </pipelines>
  </sitecore>
</configuration>