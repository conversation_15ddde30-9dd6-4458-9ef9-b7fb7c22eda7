import { RadioButton } from '@pmi/dsm-react/dist/components/radiobutton';
import classNames from 'classnames';
import React, { FC, useCallback } from 'react';
import { RazorPayOptionProps } from './models';

export const RazorPayOption: FC<RazorPayOptionProps> = (props) => {
  const { paymentMethodCode, isChecked, onChange, label, description, disabled = false } = props;
  const handleChange = useCallback(() => {
    if (!disabled) {
      onChange(paymentMethodCode);
    }
  }, [disabled, onChange, paymentMethodCode]);

  console.log(props, 'RazorPayOption');

  return (
    <li
      className={classNames('payment-paymentmethods__item razorpay__item', {
        'razorpay__item-selected': isChecked,
        'razorpay__item-disabled': disabled,
      })}
    >
      <RadioButton
        id={paymentMethodCode}
        name={paymentMethodCode}
        value={paymentMethodCode}
        checked={props.isChecked}
        disabled={disabled}
        onChange={handleChange}
        label={<div className="razorpay__item-label">{label}</div>}
      />
      {description && (isChecked || disabled) && <div className="razorpay__item-desc">{description}</div>}
    </li>
  );
};
