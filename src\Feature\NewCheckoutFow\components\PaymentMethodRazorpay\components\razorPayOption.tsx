import React, { FC, useCallback } from 'react';
import classNames from 'classnames';
import { RadioButton } from '@pmi/dsm-react/dist/components/radiobutton';
import { RazorPayOptionProps } from './models';

export const RazorPayOption: FC<RazorPayOptionProps> = (props) => {
  const { paymentMethodCode, isChecked, onChange, label, description } = props;
  const handleChange = useCallback(() => onChange(paymentMethodCode), []);
  console.log(props, 'RazorPayOption');
  return (
    <li
      className={classNames('payment-paymentmethods__item razorpay__item', {
        'razorpay__item-selected': isChecked,
      })}
    >
      <RadioButton
        id={paymentMethodCode}
        name={paymentMethodCode}
        value={paymentMethodCode}
        checked={props.isChecked}
        onChange={handleChange}
        label={<div className="razorpay__item-label">{label}</div>}
      />
      {description && isChecked && <div className="razorpay__item-desc">{description}</div>}
    </li>
  );
};
