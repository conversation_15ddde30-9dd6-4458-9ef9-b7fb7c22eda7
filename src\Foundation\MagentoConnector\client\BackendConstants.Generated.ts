// tslint:disable:indent array-type
// tslint:disable:no-use-before-declare
// tslint:disable:no-namespace
  export namespace MagentoPaymentProperties {
    export const AuthorizedAmount: string = 'MagentoPaymentProperties_AuthorizedAmount';
    export const AuthorizedCurrencyCode: string = 'MagentoPaymentProperties_AuthorizedCurrencyCode';
    export const AuthorizationMerchantCode: string = 'MagentoPaymentProperties_AuthorizationMerchantCode';
    export const PaymentProviderStatus: string = 'MagentoPaymentProperties_PaymentProviderStatus';
  }
