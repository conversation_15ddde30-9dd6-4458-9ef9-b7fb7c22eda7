﻿namespace Pmi.Spx.Feature.ESP3.Connector.ApiProviders
{
    using System.Collections.Generic;
    using System.Threading.Tasks;
    using Pmi.Spx.Feature.ESP3.Connector.Configuration;
    using Pmi.Spx.Feature.ESP3.Connector.Models.CicsWorldpay;
    using Pmi.Spx.Foundation.Framework.Enums;
    using Pmi.Spx.Foundation.Framework.Models;
    using Pmi.Spx.Foundation.Framework.Models.HttpHeaders;
    using Pmi.Spx.Foundation.Framework.Services;
    using Pmi.Spx.Foundation.Security.Models.IdentityServer;
    using Pmi.Spx.Foundation.Security.Services;    

    internal class CicsWorldpayProvider : ApiProviderBase, ICicsWorldpayProvider
    {
        private const string AuthCancelRefundEndpoint = "pmtsvc/v1/modification/reversePayment";

        private readonly IHttpService httpService;
        private readonly IEsp3Configuration configuration;
        private readonly IUrlUtility urlUtility;
        private readonly IIdentityServerTokenService tokenService;
        private readonly IMemoryCache memoryCache;

        public CicsWorldpayProvider(IUserService userService, IHttpService httpService, 
                                    IEsp3Configuration configuration, IUrlUtility urlUtility, 
                                    IIdentityServerTokenService tokenService, IMemoryCache memoryCache) : base(userService)
        {
            this.httpService = httpService;
            this.configuration = configuration;
            this.urlUtility = urlUtility;
            this.tokenService = tokenService;
            this.memoryCache = memoryCache;
        }

        public async Task<CompleteAuthCancelOrRefundResponseModel> CompleteAuthCancelOrRefund(CompleteAuthCancelOrRefundModel request)
        {
            var url = this.FormUrl(AuthCancelRefundEndpoint, null);
            var headers = await this.FormHeaders().ConfigureAwait(false);
            var content = new HttpContentData
            {
                Content = request,
                ContentType = HttpContentTypes.Json
            };

            return await this.httpService.PostAsync<CompleteAuthCancelOrRefundResponseModel>(url, content, headers: headers).ConfigureAwait(false);
        }

        private string FormUrl(string endpoint, IDictionary<string, object> parameters)
        {
            var hostUrl = this.configuration.Esp3ApiSettings.ApiHost;
            var url = this.urlUtility.JoinUrlParts(hostUrl, endpoint);
            var query = this.urlUtility.AppendQueryParams(url, parameters);
            return query;
        }
        private async Task<HttpHeaderBase[]> FormHeaders()
        {
            var cacheKey = typeof(PaymentCardProvider).ToString();
            var tokenResponse = this.memoryCache.Get<ClientCredentialsFlowTokenResponse>(cacheKey);
            var apiSettings = this.configuration.Esp3ApiSettings;
            if(tokenResponse == null)
            {
                tokenResponse = await this.tokenService.GetClientCredentialsFlowTokensAsync(this.configuration.Esp3TokenSettings).ConfigureAwait(false);
                this.memoryCache.Set(cacheKey, tokenResponse, tokenResponse.ExpiresIn);
            }
            var authorizationHeader = new BearerTokenAuthHttpHeader { Token = tokenResponse.AccessToken };

            return new HttpHeaderBase[] { authorizationHeader, new HttpHeader(apiSettings.OcpSubscriptionKey, apiSettings.OcpSubscriptionValue) };
        }
    }
}