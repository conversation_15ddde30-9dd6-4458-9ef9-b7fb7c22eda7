import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  RazorpaySubmitOrderRequestVariables,
  RazorpaySubmitOrderResponse,
  RestErrorModel,
} from 'Feature/NewCheckoutFow/models';
import { useAddError } from 'Feature/NewCheckoutFow/hooks';
import { razorpaySubmitOrderKey, getShoppingCartKey, invalidateOrFetchQuery } from 'Feature/NewCheckoutFow/utils';
import { submitRazorpayOrder } from 'Feature/NewCheckoutFow/api';
import { ErrorCodes, RazorpayUrlParameters } from 'Feature/Payment.Razorpay/client/BackendConstants.Generated';
import Url from 'url-parse';
import { useSelector } from 'react-redux';
import { signInGetUrl } from 'Foundation/ReactJss/client';

export const useRazorpaySubmitOrder = () => {
  const queryClient = useQueryClient();
  const signinGetUrl = useSelector(signInGetUrl);
  const { addError } = useAddError();
  const resetCart = ()=>{
    invalidateOrFetchQuery(queryClient, getShoppingCartKey());
  };

  const mutation = useMutation<RazorpaySubmitOrderResponse, RestErrorModel, RazorpaySubmitOrderRequestVariables>({
    mutationKey: razorpaySubmitOrderKey(),
    mutationFn: async (requestData) => submitRazorpayOrder(requestData),
    onError: async (error, variables) => {
      const apiError: RestErrorModel = error?.response?.data;
      
      if(apiError?.exceptionMessage?.toLowerCase().indexOf("session expired") >= 0)
      {
         const parsedUrl = Url(window.location.pathname, true);
         parsedUrl.query[RazorpayUrlParameters.RazorpayPaymentIdParam] = variables.requestData.razorpayPaymentId;
         parsedUrl.query[RazorpayUrlParameters.RazorpayOrderIdParam] = variables.requestData.razorpayOrderId;
         parsedUrl.query[RazorpayUrlParameters.RazorpaySignatureParam] = variables.requestData.razorpaySignature;
         const currentPageUrl = new URL(parsedUrl.toString());
         const pageUrlQueryParams = new URLSearchParams(currentPageUrl.search);
         const parsedSigninGeturl = Url(signinGetUrl, true);
         parsedSigninGeturl.query["returnUrl"] = encodeURIComponent(`${currentPageUrl.pathname}?${pageUrlQueryParams.toString()}`);
         window.location.assign(parsedSigninGeturl.toString());
      }
      else{
        addError({ code: ErrorCodes.RazorpaySubmitOrderFailed, message: apiError?.exceptionMessage });
        resetCart();
      }
    },
  });

  return {
    submitOrder: mutation.mutate,
    submitOrderAsync: mutation.mutateAsync,
    isOrderSubmitting: mutation.isLoading,
    isOrderSubmitted: mutation.isSuccess,
    isOrderSubmitFailed: mutation.isError,
    error: mutation.error,
    response: mutation.data,
  };
};
