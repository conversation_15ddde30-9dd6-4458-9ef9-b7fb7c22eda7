﻿namespace Pmi.Spx.Feature.Payment.Razorpay.Constants
{
    using TypeLite;

    [TsClass]
    public static class ErrorCodes
    {
        public const string RazorpayCreateOrderFailed = "Payment.Razorpay.CreateOrder.Failed";
        public const string InvalidRazorPayOrderSubmitRequest = "Payment.Razorpay.SubmitOrder.InvalidData";
        public const string RazorpaySubmitOrderFailed = "Payment.Razorpay.SubmitOrder.Failed";
        public const string RazorpayHandlerFailed = "Payment.Razorpay.Handler.Failed";
        public const string RazorpayPreAuthFailed = "169";
    }
}