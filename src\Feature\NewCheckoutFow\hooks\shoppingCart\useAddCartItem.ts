import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useSetAtom } from 'jotai';

import { addCartItemAsync, enrollAbandonedCartCampaign, getShoppingCart } from 'Feature/NewCheckoutFow/api';
import { addedProductsAtom } from 'Feature/NewCheckoutFow/atoms';
import { useAddError } from 'Feature/NewCheckoutFow/hooks';
import { CartVariables, ErrorModel, ShoppingCartLine } from 'Feature/NewCheckoutFow/models';
import {
  addCartItemKey,
  getAvailablePaymentMethodCodesKey,
  getRecommendedProductsKey,
  getShoppingCartKey,
} from 'Feature/NewCheckoutFow/utils';

export const useAddCartItem = () => {
  const queryClient = useQueryClient();
  const setAddedProducts = useSetAtom(addedProductsAtom);
  const { addApiError } = useAddError();
  const shoppingCartKey = getShoppingCartKey();

  const mutation = useMutation<ShoppingCartLine[], ErrorModel, CartVariables>(
    addCartItemKey(),
    (cartItem: CartVariables) => addCartItemAsync(cartItem),
    {
      onSuccess: async (data) => {
        setAddedProducts(data);
        enrollAbandonedCartCampaign({ sku: data?.[0]?.product?.productId, quantity: data?.[0]?.quantity });
        await queryClient.fetchQuery({
          queryFn: () => getShoppingCart(true),
          queryKey: shoppingCartKey,
          staleTime: 0,
        });

        await queryClient.invalidateQueries(getRecommendedProductsKey());
        await queryClient.invalidateQueries(getAvailablePaymentMethodCodesKey());
      },
      onError: async (error) => {
        addApiError(error, true);
        await queryClient.fetchQuery({
          queryFn: () => getShoppingCart(true),
          queryKey: shoppingCartKey,
          staleTime: 0,
        });
      },
    },
  );

  return {
    ...mutation,
    addCartItem: mutation.mutate,
    addCartItemAsync: mutation.mutateAsync,
    isAdded: mutation.isSuccess,
    isAdding: mutation.isLoading,
    isAddtoCartError: mutation.isError,
  };
};
