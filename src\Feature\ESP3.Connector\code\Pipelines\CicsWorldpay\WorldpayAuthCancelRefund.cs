﻿namespace Pmi.Spx.Feature.ESP3.Connector.Pipelines.CicsWorldpay
{
    using System;
    using System.Linq;
    using Pmi.Spx.Feature.ESP3.Connector.ApiProviders;
    using Pmi.Spx.Feature.ESP3.Connector.Models.CicsWorldpay;
    using Pmi.Spx.Foundation.Framework.FeatureSettings;
    using Pmi.Spx.Foundation.Framework.Services.Logging;
    using Pmi.Spx.Foundation.Framework.Services.Tasks;
    using Pmi.Spx.Foundation.MagentoConnector.Constants;
    using Pmi.Spx.Foundation.Payment.ServiceProviderModels;
    using Pmi.Spx.Foundation.Payment.ServiceProviderModels.Requests;
    
    public class WorldpayAuthCancelRefund
    {
        private readonly ITaskService taskService;
        private readonly ICicsWorldpayProvider cicsWorldpayProvider;
        private readonly IFeatureSettingsService featureSettingsService;
        private readonly ILogger logger;

        public WorldpayAuthCancelRefund(ITaskService taskService, ICicsWorldpayProvider cicsWorldpayProvider, 
                                        IFeatureSettingsService featureSettingsService, ILoggerFactory loggerFactory)
        {
            this.taskService = taskService;
            this.cicsWorldpayProvider = cicsWorldpayProvider;
            this.featureSettingsService = featureSettingsService;
            this.logger = loggerFactory.GetLogger(this);
        }

        public void Process(ServiceProviderPipelineArgs args)
        {
            if(!this.IsPipelineProcessorEnabled()) return;

            try
            {
                var cancelRequest = args.Request as CancelTransactionRequest;
                var paymentMethodCode = cancelRequest?.TransactionResultModel?.PaymentMethodCode;
                var orderCode = cancelRequest?.TransactionResultModel?.OrderCode;
                var merchantCode = cancelRequest?.TransactionResultModel
                                                ?.PaymentProperties
                                                ?.FirstOrDefault(p => p.Key == MagentoPaymentProperties.AuthorizationMerchantCode)
                                                ?.Value;

                if(string.IsNullOrWhiteSpace(paymentMethodCode) || string.IsNullOrWhiteSpace(orderCode))
                    return;

                var request = new CompleteAuthCancelOrRefundModel 
                {
                    Reference = orderCode,
                    PaymentPspReference = orderCode,
                    MerchantCode = merchantCode,
                    PaymentMethod = paymentMethodCode,
                    PaymentVendor = "Worldpay"
                };
                var cancelRefundResponse = this.taskService.SyncInvoke(() => this.cicsWorldpayProvider.CompleteAuthCancelOrRefund(request));
                if(cancelRefundResponse != null)
                {
                    this.logger.Info($"Order {cancelRefundResponse.OrderCode} sent to CancelRefundQueue - paymentmethod: {cancelRefundResponse.PaymentMethod}," +
                        $" status : {cancelRefundResponse.Status}");
                }
            }
            catch(Exception ex)
            {
                this.logger.Error("Error in Pmi.Spx.Feature.ESP3.Connector.Pipelines.CicsWorldpay.WorldpayAuthCancelRefund.Process().", ex);
            }
        }

        private bool IsPipelineProcessorEnabled()
        {
            var featureSettings = featureSettingsService.GetFeatureSettings();
            return featureSettings?.IsCancelRefundQueueEnabled ?? false;
        }
    }
}