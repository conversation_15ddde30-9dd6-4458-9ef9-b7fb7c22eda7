﻿namespace Pmi.Spx.Project.Functional.Tests.Models
{
    using Pmi.Spx.Project.SPX.Functional.Tests.Hooks;
    using Amount = Currency.Amount;

    public class ProductBuilder
    {

        private readonly TestData _testData;
        private readonly StoreContext _storeContext;

        public ProductBuilder(TestData testData, StoreContext storeContext)
        {
            _testData = testData;
            _storeContext = storeContext;
        }

        public Product GenerateProductFromProductDetails(ProductDetails productDetails)
        {
            var product = new Product()
            {
                Title = productDetails.Name.Trim(),
                Sku = productDetails.Sku,
                RegularPrice = new Amount(_storeContext.Currency, productDetails.Price),
                TierPrices = productDetails.Tier_Prices,
                ZonePrices = productDetails.Extension_Attributes.Zone_Prices,
                CountryPrices = productDetails.Extension_Attributes.Country_Prices
            };

            return product;
        }

        public string GetMembershipProductTitle()
        {
            var productDetails = AdobeCommerce.GetProductDetails(_testData.MembershipSku, _storeContext.Store);
            return productDetails.Name;
        }

        public Product GetChapterProduct(string chapterSku = null)
        {
            var product = new Product();

            if(_testData.ChapterProduct == null && chapterSku == null)
            {
                var productDetails = AdobeCommerce.GetProductDetails(_testData.ChapterSku, _storeContext.Store);
                _testData.ChapterCode = (string)productDetails.GetCustomAttributeValue("chapter_code");

                product = GenerateProductFromProductDetails(productDetails);
            }
            else if(chapterSku != null)
            {
                var productDetails = AdobeCommerce.GetProductDetails(chapterSku, _storeContext.Store);

                product = GenerateProductFromProductDetails(productDetails);
            }
            else
            {
                product.Title = _testData.ChapterProduct;
                product.RegularPrice = new Amount(_storeContext.Currency, _testData.ChapterRegularPrice);
            }

            product.Category = Category.ChapterMembership;

            return product;
        }

        public Product GetDigitalProduct()
        {
            var product = new Product();

            if(_testData.CourseName == null)
            {
                var productDetails = AdobeCommerce.GetProductDetails(_testData.DigitalProductSku, _storeContext.Store);

                product = GenerateProductFromProductDetails(productDetails);
            }
            else
            {
                product.Title = _testData.CourseName;
                product.RegularPrice = new Amount(_storeContext.Currency, _testData.CourseRegularPrice);
                product.MemberPrice = new Amount(_storeContext.Currency, _testData.CourseMemberPrice);
            }

            product.RegularPrice = SetCountryOrZonePriceIfAny(product);

            product.Category = Category.DigitalProduct;

            return product;
        }

        public Product GetCertificationProduct()
        {
            var product = new Product();

            if(_testData.CertName == null)
            {
                var productDetails = AdobeCommerce.GetProductDetails(_testData.CertSku, _storeContext.Store);

                product = GenerateProductFromProductDetails(productDetails);
            }
            else
            {
                product.Title = _testData.CertName;
                product.RegularPrice = new Amount(_storeContext.Currency, _testData.CertRegularPrice);
                product.MemberPrice = new Amount(_storeContext.Currency, _testData.CertMemberPrice);
            }

            product.Category = Category.DigitalProduct;

            return product;
        }

        public Product GetMembershipProduct(MembershipType membershipType)
        {
            var product = new Product();

            if(_testData.MembershipProduct == null)
            {
                var productDetails = AdobeCommerce.GetProductDetails(_testData.MembershipSku, _storeContext.Store);

                product = GenerateProductFromProductDetails(productDetails);
                product.Title = product.Title.TrimEnd();

                if(_testData.MembershipSku.Contains("CHAPTERS") || _testData.MembershipSku.Contains("MEMB"))
                {
                    var cartDetails = AdobeCommerce.GetCartDetails(_testData.User, _storeContext.Store);
                    var cartItem = cartDetails.GetItemInCartWithSku(_testData.MembershipSku);
                    product.RegularPrice = new Amount(_storeContext.Currency, (decimal)cartItem.Price);
                }
                else
                {
                    product.RegularPrice = new Amount(_storeContext.Currency, productDetails.Price);
                }
                product.RegularPrice = SetCountryOrZonePriceIfAny(product, membershipType);
            }
            else
            {
                product.Title = _testData.MembershipProduct;
                product.Sku = _testData.MembershipSku;
                product.RegularPrice = new Amount(_storeContext.Currency, _testData.MembershipRegularPrice);
            }

            product.Category = Category.Membership;

            return product;
        }

        private Amount SetCountryOrZonePriceIfAny(Product product, MembershipType? membershipType = null)
        {
            var countryPrice = product.CountryPrices?.Find(p => p.Extension_Attributes.Country_Code.Equals(_testData.Country));
            var countryCode = _testData.Country.Equals("USA") ? "US" : _testData.Country;//This is a temporary solution to bypass config issues on the sitecore
            var zonePrice = product.ZonePrices?.Find(p => p.Extension_Attributes.Zone_code.Equals(countryCode));
            if(countryPrice == null && zonePrice == null)
                return product.RegularPrice;

            var price = countryPrice ?? zonePrice;
            var renewalPrice = price.Extension_Attributes.Renewal_Price;

            if(membershipType.ToString().Contains("Renewal"))
                return new Amount(_storeContext.Currency, renewalPrice);

            return new Amount(_storeContext.Currency, price.Value);
        }
    }
}
