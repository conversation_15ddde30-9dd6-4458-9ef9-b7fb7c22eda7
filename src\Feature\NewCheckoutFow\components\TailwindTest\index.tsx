import * as React from 'react';

/**
 * Test component to verify Tailwind CSS is working correctly
 * This component uses various Tailwind utility classes to test the integration
 */
export const TailwindTest: React.FC = () => {
  return (
    <div className="bg-blue-200 border border-blue-300 rounded-lg p-6 m-4 max-w-md mx-auto">
      <h2 className="text-2xl font-bold text-blue-800 mb-4">🎨 Tailwind CSS Test</h2>

      <div className="space-y-3">
        <div className="bg-white rounded-md p-3 shadow-sm">
          <p className="text-gray-700 text-sm">If you can see this styled correctly, Tailwind CSS is working! ✅</p>
        </div>

        <div className="flex space-x-2">
          <button className="bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded transition-colors">
            Success
          </button>
          <button className="bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded transition-colors">
            Error
          </button>
        </div>

        <div className="grid grid-cols-2 gap-2">
          <div className="bg-yellow-200 p-2 rounded text-center text-xs">Grid 1</div>
          <div className="bg-purple-200 p-2 rounded text-center text-xs">Grid 2</div>
        </div>
      </div>

      <div className="mt-4 text-xs text-gray-500">
        Classes used: bg-blue-100, border, rounded-lg, p-6, text-2xl, font-bold, etc.
      </div>
    </div>
  );
};

export default TailwindTest;
