﻿namespace Pmi.Spx.Feature.Checkout.Services
{
    using Pmi.Spx.Feature.Checkout.JsModels.MarketingAutomation;
    using Pmi.Spx.Foundation.Commerce.Models.Cart;
    using Pmi.Spx.Foundation.Commerce.ServiceProviders.Contracts;
    using Sitecore.Commerce;

    public class MarketingAutomationService : IMarketingAutomationService
    {
        private readonly IMarketingAutomationServiceProvider _marketingAutomationServiceProvider;
        public MarketingAutomationService(IMarketingAutomationServiceProvider marketingAutomationServiceProvider)
        {
            Assert.ArgumentNotNull(marketingAutomationServiceProvider, nameof(marketingAutomationServiceProvider));

            _marketingAutomationServiceProvider = marketingAutomationServiceProvider;
        }

        public void EnrollAbandonedCartCampaign(EnrollAbandonedCartModel enrollAbandonedCartModel)
        {
            var commerceCartLine = new CartLine
            {
                Sku = enrollAbandonedCartModel.Sku,
                Quantity = enrollAbandonedCartModel.Quantity,
            };
            _marketingAutomationServiceProvider.EnrollUserToAbandonedCartCampaign(commerceCartLine);
        }
    }
}