// tslint:disable:indent array-type
// tslint:disable:no-use-before-declare
// tslint:disable:no-namespace
  export namespace ErrorCodes {
    export const RazorpayCreateOrderFailed: string = 'Payment.Razorpay.CreateOrder.Failed';
    export const InvalidRazorPayOrderSubmitRequest: string = 'Payment.Razorpay.SubmitOrder.InvalidData';
    export const RazorpaySubmitOrderFailed: string = 'Payment.Razorpay.SubmitOrder.Failed';
    export const RazorpayHandlerFailed: string = 'Payment.Razorpay.Handler.Failed';
  }
  export namespace RazorpayPaymentMethods {
    export const CreditCard: string = 'card';
    export const Upi: string = 'upi';
    export const NetBanking: string = 'netbanking';
  }
  export namespace RazorpayPaymentStatuses {
    export const Success: string = 'SUCCESS';
    export const Failure: string = 'FAILURE';
  }
  export namespace RazorpaySitecoreSettings {
    export const RazorpaySettingsItemId: string = '{FEA40E00-BF72-44DA-B924-1DE766947D92}';
  }
  export namespace RazorpayUrlParameters {
    export const RazorpayStatus: string = 'razorpayStatus';
    export const RazorpayErrorCode: string = 'razorpayErrorCode';
    export const RazorpayErrorDescription: string = 'razorpayErrorDesc';
  }
