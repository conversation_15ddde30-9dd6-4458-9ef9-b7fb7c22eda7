﻿namespace Pmi.Spx.Feature.Checkout.Tests.Controllers
{
    using Pmi.Spx.Feature.Checkout.Controllers;
    using Pmi.Spx.Feature.Checkout.Services;
    using Moq;
    using NSubstitute;
    using Pmi.Spx.Foundation.Extensions.Controllers.ActionResult;
    using Pmi.Spx.Foundation.Framework.Services.Logging;
    using System;
    using System.Web.Mvc;
    using Xunit;
    using Pmi.Spx.Feature.Checkout.JsModels.MarketingAutomation;

    public class MarketingAutomationControllerTests
    {
        private readonly Mock<IMarketingAutomationService> _marketingAutomationServiceMock;
        private readonly Mock<ILoggerFactory> _loggerFactoryMock;
        private readonly MarketingAutomationController _controller;

        public MarketingAutomationControllerTests()
        {
            _marketingAutomationServiceMock = new Mock<IMarketingAutomationService>();
            _loggerFactoryMock = new Mock<ILoggerFactory>();

            var loggerMock = new Mock<ILogger>();
            _loggerFactoryMock.Setup(m => m.GetLogger(It.IsAny<object>(), It.IsAny<string>())).Returns(loggerMock.Object);

            _controller = new MarketingAutomationController(_marketingAutomationServiceMock.Object, _loggerFactoryMock.Object);
        }

        [Fact]
        public void EnrollAbandonedCartCampaing_ShouldReturnSuccess()
        {
            // Arrange
            EnrollAbandonedCartModel enrollAbandonedCartModel = Substitute.For<EnrollAbandonedCartModel>();
            
            // Act
            var result = _controller.EnrollAbandonedCartCampaign(enrollAbandonedCartModel);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<CamelCasePropertyJsonResult>(result);

            var  jsonResult = result as CamelCasePropertyJsonResult;
            Assert.Equal(System.Net.HttpStatusCode.OK, jsonResult.StatusCode);
        }

        [Fact]
        public void EnrollAbandonedCartCampaing_ShouldReturnError_WhenEnrollAbandonedCartThrowsException()
        {
            // Arrange
            EnrollAbandonedCartModel enrollAbandonedCartModel = Substitute.For<EnrollAbandonedCartModel>();
            _marketingAutomationServiceMock.Setup(m => m.EnrollAbandonedCartCampaign(enrollAbandonedCartModel)).Throws(new Exception("Test exception"));
            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = Substitute.For<System.Web.HttpContextBase>()
            };

            // Act
            var result = _controller.EnrollAbandonedCartCampaign(enrollAbandonedCartModel);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<CamelCasePropertyJsonResult>(result);

            var jsonResult = result as CamelCasePropertyJsonResult;
            Assert.Equal(System.Net.HttpStatusCode.InternalServerError, jsonResult.StatusCode);
        }
    }
}
