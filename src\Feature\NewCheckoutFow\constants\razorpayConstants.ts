import { PaymentMethodCodes } from 'Foundation/Payment/client/BackendConstants.Generated';

export const RazorpayCodes = [
  PaymentMethodCodes.RazorpayCreditCard,
  PaymentMethodCodes.RazorpayUpi,
  PaymentMethodCodes.RazorpayNetbanking,
];

/**
 * UPI payment method limit in rupees
 * UPI is only available for transactions totaling ₹24,999 or less when Membership is part of the order
 */
export const UPI_LIMIT_AMOUNT = 24999;

// Re-export PaymentMethodCodes to avoid circular dependencies
export { PaymentMethodCodes };
