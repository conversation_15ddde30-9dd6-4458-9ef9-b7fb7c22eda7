﻿namespace Pmi.Spx.Feature.Payment.Razorpay.Pipelines.Order.CreateOrder
{
    using Pmi.Spx.Foundation.Framework.Services.Logging;
    using Pmi.Spx.Feature.Payment.Razorpay.ServiceProviderModels;
    using Pmi.Spx.Feature.Payment.Razorpay.TemplateModels;
    using Pmi.Spx.Foundation.Commerce.Constants;
    using Pmi.Spx.Foundation.Framework.Services.GlassMapper;
    using Pmi.Spx.Foundation.Framework.Services.Tasks;
    using Pmi.Spx.Foundation.GeoIP.Services;
    using Pmi.Spx.Foundation.MagentoConnector.MagentoApi.Models.PreAuth;
    using Pmi.Spx.Foundation.MagentoConnector.MagentoProviders;
    using Sitecore.Commerce.Pipelines;
    using System;
    using Pmi.Spx.Foundation.Profile.Services;
    using System.Linq;
    using Pmi.Spx.Feature.Payment.Razorpay.Constants;
    using Pmi.Spx.Foundation.Framework.Exceptions;
    using ErrorCodes = Pmi.Spx.Feature.Payment.Razorpay.Constants.ErrorCodes;

    public class PreauthRazorpayOrderCreateProcessor
    {
        private readonly ICustomerCartProvider _apiProvider;
        private readonly IUserProfileService _userProfileService;
        private readonly ITaskService _taskService;
        private readonly ILogger _logger;
        private readonly ILocationService _locationService;
        private readonly ISitecoreServiceFactory _sitecoreServiceFactory;

        public PreauthRazorpayOrderCreateProcessor(ICustomerCartProvider apiProvider,
            IUserProfileService userProfileService, ITaskService taskService, ILocationService locationService, ILoggerFactory loggerFactory, ISitecoreServiceFactory sitecoreServiceFactory)
        { 
            _apiProvider = apiProvider;
            _userProfileService = userProfileService;
            _taskService = taskService;
            _apiProvider = apiProvider;
            _logger = loggerFactory.GetLogger(GetType());
            _locationService = locationService;
            _sitecoreServiceFactory = sitecoreServiceFactory;
        }

        private string GetEmailAddressFromProfie() 
        {
            var email = _userProfileService.GetAllEmails().FirstOrDefault(e=> e.IsPreferred);
            return email?.Address;
        
        }

        public void Process(ServicePipelineArgs args)
        {
            if(!args.Result.Success) return;

            if(!(args.Request is CreateOrderRequest request) ||
              !(args.Result is CreateOrderResult result))
                return;

            var sitecoreService = this._sitecoreServiceFactory.Create();
            var razorpaySettings = sitecoreService.GetItem<IRazorpaySettings>(Guid.Parse(RazorpaySitecoreSettings.RazorpaySettingsItemId));
            if(!razorpaySettings.IsPreAuthRequired)
                return;

            var location = this._locationService.GetCurrentLocation();

            try
            {
                var preauthRequest = new PreauthOrderCreateRequest();
                preauthRequest.PaymentMethod.Method = $"razorpay_{request.RequestData.PaymentMethod}";
                preauthRequest.PaymentMethod.ExtensionAttributes.Add("customer_ip", location.IpAddress);
                preauthRequest.PaymentMethod.ExtensionAttributes.Add("customer_email", request.UserCart?.Email ?? GetEmailAddressFromProfie());
                preauthRequest.PaymentMethod.ExtensionAttributes.Add("ip_country_code", location.CountryCode);
                preauthRequest.PaymentMethod.ExtensionAttributes.Add("ip_region_code", location.RegionCode);
                var storeCode = args.Request.Properties[CommercePipelineParameters.ProductStoreCodeKey] as string;
                args.Result.Success = _taskService.SyncInvoke<bool>((context) => _apiProvider.PreAuthorizeCreateOrder(storeCode, preauthRequest,context));
                _logger.Info($"Results of RazorPay Preauth: {args.Result.Success}");
                
            }
            catch(CodedException ex)
            {
                _logger.Error("RazorPay Preauth validation failure", ex);
                throw;
            }
            catch(Exception ex)
            {
                _logger.Error("RazorPay Preauth validation failure", ex);
                throw new CodedException(ErrorCodes.RazorpayPreAuthFailed, ex.Message, ex);
            }
        } 
    }
}