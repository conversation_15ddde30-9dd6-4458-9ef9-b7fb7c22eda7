﻿namespace Pmi.Spx.Project.Functional.Tests.Pages
{
    using OpenQA.Selenium;
    using OpenQA.Selenium.Support.UI;
    using Pmi.Spx.Project.Functional.Tests.Models;
    using Pmi.Spx.Project.SPX.Functional.Tests.Hooks;
    using Pmi.Web.Ui.Framework.Page;
    using System.Linq;
    using TechTalk.SpecFlow;

    public class AdyenPayPalPage : BasePage<AdyenPayPalPage>
    {
        private IWebElement PayPalUserName => WebDriver.FindElement(By.XPath("//input[@id='email']"));
        private IWebElement PayPalNextButton => WebDriver.FindElement(By.XPath("//button[@id='btnNext']"));
        private IWebElement PayPal_Password => WebDriver.FindElement(By.XPath("//input[@id='password']"));
        private IWebElement PayPal_Login_Button => WebDriver.FindElement(By.XPath("//button[@id='btnLogin']"));
        private IWebElement SelectPaypalPayment => WebDriver.FindElement(By.XPath("//span[contains(text(),'CREDIT UNION 1 (AK)')]"));
        private IWebElement SubmitPayPalButton => WebDriver.FindElement(By.XPath("//button[text()='Complete Purchase'] | //button[@data-id='payment-submit-btn']"));

        private bool AdyenPayPalUserNameDisplayed() => Extensions.CatchUnavailableElement(() => PayPalUserName.Displayed, false);
        private bool AdyenPayPalPasswordDisplayed() => Extensions.CatchUnavailableElement(() => PayPal_Password.Displayed, false);
        private bool AdyenPayPalPaymentTypeDisplayed() => Extensions.CatchUnavailableElement(() => SelectPaypalPayment.Displayed, false);

        private readonly WebDriverWait _wait;

        private readonly UserSettings _userSettings;

        public AdyenPayPalPage(IWebDriver driver) : base(driver)
        {
            _userSettings = new UserSettings();
            _wait = new WebDriverWait(WebDriver, _userSettings.DefaultExplicitWaitTimeout);
        }
        public override string BaseUrl => _userSettings.BaseUrl;
        public override string RelativePath => "";

        private const string AdyenPayPalUrl = "sandbox.paypal";

        public override AdyenPayPalPage VerifyPageLoaded()
        {
            WebDriver.WaitForAllLoadersInvisible();
            WebDriver.LogCurrentUrl();
            return base.VerifyPageLoaded(); 
        }

        public AdyenPayPalPage LoginToAdyenPayPal(TestData testData, ScenarioContext context)
        {
            _wait.Until(_ => WebDriver.Url.Contains(AdyenPayPalUrl));

            LoginToPayPal(testData.GetAdyenCredential());

            var isPayPalPayLater = context.ScenarioInfo.Title.Contains("Paypal PayLater");

            if(isPayPalPayLater)
            {
                SubmitPayPalPayLaterPayment();
            }
            else
            {
                SubmitPayPalPayment();
            }

            WebDriver.SwitchTo().Window(WebDriver.WindowHandles.First());

            return this;
        }

        private void LoginToPayPal(AdyenCredential adyenCredential)
        {
            EnterUsername(adyenCredential.UserName);
            EnterPassword(adyenCredential.Password);
            PayPal_Login_Button.ScrollAndClickAndWait(WebDriver);
            _wait.Until(_ => AdyenPayPalPaymentTypeDisplayed());
        }

        private void EnterUsername(string username)
        {
            _wait.Until(_ => AdyenPayPalUserNameDisplayed());
            PayPalUserName.SendKeys(username);
            PayPalNextButton.ScrollAndClickAndWait(WebDriver);
        }

        private void EnterPassword(string password)
        {
            _wait.Until(_ => AdyenPayPalPasswordDisplayed());
            PayPal_Password.SendKeys(password);
        }

        private void SubmitPayPalPayment()
        {
            SelectPaypalPayment.ScrollAndClick(WebDriver);
            SubmitPayPalButton.ScrollAndClick(WebDriver);
        }

        private IWebElement SelectPayLaterPayment => WebDriver.FindElement(By.XPath("//span[contains(text(),'Pay in 4')]"));

        private IWebElement ContinueToPayIn4PaymentsButton => WebDriver.FindElement(By.XPath("//button[@data-id='payment-submit-btn']"));

        private IWebElement Continue => WebDriver.FindElement(By.XPath("//button[@id='submitButton'][text()='Continue']"));

        private IWebElement AgreeAndApply => WebDriver.FindElement(By.XPath("//button[@id='submitButton'][text()='Agree and Apply']"));

        private IWebElement SelectPayLaterPaymentType => WebDriver.FindElement(By.XPath("//button//p[text()='CREDIT UNION 1 (AK)']"));

        private IWebElement PaymentTermsCheckbox => WebDriver.FindElement(By.XPath("//input[@class='ppvx_checkbox__input']"));

        private IWebElement AgreeAndContinueButton => WebDriver.FindElement(By.XPath("//button[@id='submitButton'][text()='Agree and Continue']"));

        private IWebElement ApprovedStatus => WebDriver.FindElement(By.XPath("//span[contains(text(),'Approved')]"));

        private IWebElement PayNowButton => WebDriver.FindElement(By.XPath("//button[@data-id='payment-submit-btn']"));

        private bool PayNowButtonDisplayed() => Extensions.CatchUnavailableElement(() => PayNowButton.Displayed, false);

        private bool ContinueButtonDisplayed() => Extensions.CatchUnavailableElement(() => Continue.Displayed, false);

        private bool AgreeAndApplyButtonDisplayed() => Extensions.CatchUnavailableElement(() => AgreeAndApply.Displayed, false);

        private bool SelectPayLaterPaymentTypeDisplayed() => Extensions.CatchUnavailableElement(() => SelectPayLaterPaymentType.Displayed, false);


        private const string FirstIFrame = "//iframe[@data-testid='cap-iframe']";

        private const string SecondIFrame = "//iframe[@id='ppfnfnclsiframe']";

        private void SwitchToIframe(string iframeXpath) => WebDriver.SwitchTo().Frame(WebDriver.FindElement(By.XPath(iframeXpath)));

        public void ContinueButton()
        {
            SwitchToIframe(FirstIFrame);
            _wait.Until(_ => ContinueButtonDisplayed());
            Continue.ScrollAndClick(WebDriver);
        }

        public void AgreeAndApplyButton()
        {
            SwitchToIframe(SecondIFrame);
            WebDriver.SwitchTo().DefaultContent();
            SwitchToIframe(FirstIFrame);
            _wait.Until(_ => AgreeAndApplyButtonDisplayed());
            AgreeAndApply.ScrollAndClick(WebDriver);
        }

        public void SelectPaymentType()
        {
            _wait.Until(_ => SelectPayLaterPaymentTypeDisplayed());
            SelectPayLaterPaymentType.ScrollAndClick(WebDriver);
            PaymentTermsCheckbox.ScrollAndClick(WebDriver);
            AgreeAndContinueButton.ScrollAndClick(WebDriver);
        }

        public void PayButton()
        {
            WebDriver.SwitchTo().DefaultContent();
            _wait.Until(_ => PayNowButtonDisplayed());
            WebDriver.LockExecution(_userSettings.WaitforWebElementTimeout);
            PayNowButton.ScrollAndClick(WebDriver);
        }

        private void SubmitPayPalPayLaterPayment()
        {

            SelectPayLaterPayment.ScrollAndClick(WebDriver);

            ContinueToPayIn4PaymentsButton.ScrollAndClick(WebDriver);

            ContinueButton();

            AgreeAndApplyButton();

            SelectPaymentType();

            PayButton();

        }

    }
}
