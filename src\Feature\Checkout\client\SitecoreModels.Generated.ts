// tslint:disable:indent array-type
// tslint:disable:no-use-before-declare
// tslint:disable:no-namespace
// @ts-ignore
  import * as ReactJssModule from 'Foundation/ReactJss/client';
// @ts-ignore

  // The AddressChangeOptions template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/AddressChangeOptions
  // ID: f3d84f3f-ac51-430c-8b56-39623fda6801
  export interface AddressChangeOptionsDataSource extends ReactJssModule.BaseDataSourceItem {
    // The AddressUpdateLink field.
    // Short description:
    // Field Type: General Link
    // Field ID: e4dc03ec-101c-4141-8a1d-cc0fa7d64401
    // Custom Data:
    addressUpdateLink: ReactJssModule.LinkField;
    // The UpdateAddressSuggestionText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 80ba908d-a978-40c2-8c86-738c30a2f09a
    // Custom Data:
    updateAddressSuggestionText: ReactJssModule.TextField;
  }
  // The Apply Voucher Folder template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Apply Voucher/Apply Voucher Folder
  // ID: 25460bc1-0b9a-4b18-bc31-7f88b8deb89f
  export interface ApplyVoucherFolderDataSource extends ReactJssModule.BaseDataSourceItem {
  }
  // The AutoSaveUserData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/AutoSaveUserData
  // ID: 1f9ff9a3-ea4a-40f0-8a08-e50eb9b0c7e5
  export interface AutoSaveUserDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The AutoSaveText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: d4c5b41d-5842-4d3c-a6f1-26b6d94caa99
    // Custom Data:
    autoSaveText: ReactJssModule.TextField;
  }
  // The Brazil CPF Code Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Brazil CPF Code Data
  // ID: 60ebfc58-4381-4302-a9b9-6379eb5575bf
  export interface BrazilCPFCodeDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Add Cpf Button Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: df269c6d-7c82-4614-9e7f-bd34372c3d3e
    // Custom Data:
    addCpfButtonText: ReactJssModule.TextField;
    // The Allow CNPJ field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: a38ce8d9-43fd-4708-92db-704e48f4a1f2
    // Custom Data:
    allowCNPJ: ReactJssModule.Field<boolean>;
    // The Cpf Field Label field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 43eb3754-bd05-4aa7-9237-81026762a304
    // Custom Data:
    cpfFieldLabel: ReactJssModule.TextField;
    // The Cpf Field Placeholder field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: aaa82e5b-64b0-434a-9aad-161af0216d1d
    // Custom Data:
    cpfFieldPlaceholder: ReactJssModule.TextField;
    // The Cpf Invalid Message field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 563cc6ec-81b1-42f2-9cba-6347baf9c171
    // Custom Data:
    cpfInvalidMessage: ReactJssModule.TextField;
    // The Cpf Required Message field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 21cf7574-1d3a-4ff7-8137-7ca9ac7c291c
    // Custom Data:
    cpfRequiredMessage: ReactJssModule.TextField;
    // The Expand Link Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 525d0e85-9a4c-48c5-82f3-dc335a98308a
    // Custom Data:
    expandLinkText: ReactJssModule.TextField;
    // The Heading Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 8eb26979-aaa2-4d15-9e2f-8f5c1268ce31
    // Custom Data:
    headingText: ReactJssModule.TextField;
  }
  // The Cart Folder template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Cart Folder
  // ID: b5a8fe0a-c066-4b42-9a20-6584bc2441f7
  export interface CartFolderDataSource extends ReactJssModule.BaseDataSourceItem {
  }
  // The Cart Membership Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Cart Membership Data
  // ID: 2be48eb8-644d-4bc2-b3b1-5ba830f97a02
  export interface CartMembershipDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Email field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: b808958a-4292-41a6-be94-324e60efe676
    // Custom Data:
    email: ReactJssModule.TextField;
    // The MembershipSection field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 0a6272b6-3163-49ee-b431-6619744709cc
    // Custom Data:
    membershipSection: ReactJssModule.Item<ReactJssModule.BaseDataSourceItem>;
    // The RegisteredSection field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 57377190-db60-492a-b7a8-4fdc6ac86c32
    // Custom Data:
    registeredSection: ReactJssModule.Item<ReactJssModule.BaseDataSourceItem>;
    // The SheerIDParameters field.
    // Short description:
    // Field Type: Droplink
    // Field ID: feb268d7-1be2-496c-b447-32e7814b073e
    // Custom Data:
    sheerIDParameters: ReactJssModule.Item<ReactJssModule.BaseDataSourceItem>;
    // The SheerIdUrl field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 7bea4473-24f0-4c98-8294-a0589cda17bd
    // Custom Data:
    sheerIdUrl: ReactJssModule.TextField;
    // The StudentMembershipDisclaimerText field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 13483cc2-71a8-46d4-a6a8-a8a9dfb8de4b
    // Custom Data:
    studentMembershipDisclaimerText: ReactJssModule.TextField;
  }
  // The CartMembershipOptions template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/CartMembershipOptions
  // ID: e4cd7788-e383-49ee-a0b4-e1e7058ec7bb
  export interface CartMembershipOptionsDataSource extends ReactJssModule.BaseDataSourceItem {
    // The ApplicationFeeText field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: cb9f6fce-5035-47c6-99a5-97f378c4ee4a
    // Custom Data:
    applicationFeeText: ReactJssModule.TextField;
    // The ListItems field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 269a0fb9-6bfe-419c-afb9-034e0cbdfca7
    // Custom Data:
    listItems: ReactJssModule.TextField;
    // The OptionsLink field.
    // Short description:
    // Field Type: General Link
    // Field ID: 5821391d-31c6-49f3-97bf-40f7042c47e5
    // Custom Data:
    optionsLink: ReactJssModule.LinkField;
    // The StandardTariffText field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 8c0979fa-17b7-4af5-a93d-3afb6aa3f3de
    // Custom Data:
    standardTariffText: ReactJssModule.TextField;
    // The SubTitle field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 1a99fc2d-8f9b-45df-8320-d49da519ad95
    // Custom Data:
    subTitle: ReactJssModule.TextField;
    // The Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: b121896f-c2e4-45c8-afa9-9db3c900cdc4
    // Custom Data:
    title: ReactJssModule.TextField;
  }
  // The CartMembershipRenderingParameters template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Rendering Parameters/CartMembershipRenderingParameters
  // ID: e76ad388-95e5-4536-8c4a-94fe003aa384
  export interface CartMembershipRenderingParametersDataSource extends ReactJssModule.BaseDataSourceItem {
    // The isABTestEnabled field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 2440ff2a-be58-4d03-9a56-3a10bf8211f4
    // Custom Data:
    isABTestEnabled: ReactJssModule.Field<boolean>;
  }
  // The Cart Totals Rendering Parameters template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Rendering Parameters/Cart Totals Rendering Parameters
  // ID: 8a074b16-c179-4a9e-a684-eafab5ae4474
  export interface CartTotalsRenderingParametersDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Cart Totals Data Source field.
    // Short description:
    // Field Type: Droplist
    // Field ID: 8d7cc943-dc3f-480b-8778-04cbfe046c82
    // Custom Data:
    cartTotalsDataSource: ReactJssModule.TextField;
    // The Show Taxes field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 4d2a45a5-2239-4c58-ba48-437f7ca69a5d
    // Custom Data:
    showTaxes: ReactJssModule.Field<boolean>;
  }
  // The Cart Totals Row Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Cart Totals Row Data
  // ID: a2d41f52-8c1f-48d2-8781-ecbea6b70501
  export interface CartTotalsRowDataDataSource extends ReactJssModule.BaseDataSourceItem {
  }
  // The Cart Totals Row template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Cart Totals Row
  // ID: eee924d7-b03e-4f69-af42-a13ff0804826
  export interface CartTotalsRowDataSource extends ReactJssModule.BaseDataSourceItem {
    // The SubText field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 564d64be-ac0d-49de-9e13-baea011a215e
    // Custom Data:
    subText: ReactJssModule.TextField;
    // The Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 078b384a-dab1-4b10-b06d-db14167b292d
    // Custom Data:
    title: ReactJssModule.TextField;
  }
  // The Cart Totals Row Rendering Parameters template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Rendering Parameters/Cart Totals Row Rendering Parameters
  // ID: 1525fca3-8f8f-41a9-b0a5-efaa00cb5f35
  export interface CartTotalsRowRenderingParametersDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Field field.
    // Short description:
    // Field Type: Droplink
    // Field ID: c4721439-a7bc-4f10-9647-ba78c850f7eb
    // Custom Data:
    field: ReactJssModule.Item<ReactJssModule.BaseDataSourceItem>;
    // The Is Bold field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 7ac0d283-7d2d-4704-99b1-b8c5e3b149ff
    // Custom Data:
    isBold: ReactJssModule.Field<boolean>;
    // The Is Red field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 064e998c-2880-42e8-884d-45d2fbcfd33e
    // Custom Data:
    isRed: ReactJssModule.Field<boolean>;
    // The UseLocalCurrencyTotal field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 1ddf4958-3262-44b8-836c-1b54cef164e0
    // Custom Data:
    useLocalCurrencyTotal: ReactJssModule.Field<boolean>;
  }
  // The CertificationRenewalAgreementOptions template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/CertificationRenewalAgreementOptions
  // ID: e3bef9d3-e703-4f73-b693-32c9114eddab
  export interface CertificationRenewalAgreementOptionsDataSource extends ReactJssModule.BaseDataSourceItem {
    // The AgreeIsChecked field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 12bf1b68-3d7b-49c2-8f4a-f5f9e5616460
    // Custom Data:
    agreeIsChecked: ReactJssModule.Field<boolean>;
    // The AgreementText field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 8adf48be-fa45-47b5-970b-fb1077af32c8
    // Custom Data:
    agreementText: ReactJssModule.TextField;
  }
  // The Change Single Membership Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Change Single Membership Data
  // ID: 10911b27-4abf-4ee9-8e33-a9e2dbb19af4
  export interface ChangeSingleMembershipDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Accordion Button Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 964dcce7-f808-4b0e-a802-9307c6554449
    // Custom Data:
    accordionButtonTitle: ReactJssModule.TextField;
    // The Expanded Description field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: dd026107-7891-4a67-94fb-7fe567fef875
    // Custom Data:
    expandedDescription: ReactJssModule.TextField;
    // The Expanded Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: e62f6dc0-b1f9-443b-b987-089ae80ebe82
    // Custom Data:
    expandedTitle: ReactJssModule.TextField;
    // The Header Description field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: 8b657aef-5407-4973-8a00-4be3494e01af
    // Custom Data:
    headerDescription: ReactJssModule.TextField;
    // The Header Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: d0070b11-1fac-498f-8f06-74d1f06ae985
    // Custom Data:
    headerTitle: ReactJssModule.TextField;
  }
  // The Checkout Buttons Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Checkout Buttons Data
  // ID: a3388754-626a-4f5a-aeb7-1e0359134b91
  export interface CheckoutButtonsDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Continue Shopping Link field.
    // Short description:
    // Field Type: General Link
    // Field ID: 76feda09-65d0-4ef0-9efd-a72ef14ae5b4
    // Custom Data:
    continueShoppingLink: ReactJssModule.LinkField;
    // The Go to Checkout Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 7f00e387-fdf4-4c4d-9c11-2aef12f7e131
    // Custom Data:
    goToCheckoutText: ReactJssModule.TextField;
    // The Go to Shopping Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 9ff0d674-ceba-4256-8855-fccd75b4348c
    // Custom Data:
    goToShoppingText: ReactJssModule.TextField;
  }
  // The CheckoutContainerData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/CheckoutContainerData
  // ID: 2a2002ea-d572-476f-b06c-d051eff55d86
  export interface CheckoutContainerDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The BackButtonText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: b05270e6-1011-4569-95d9-9f9b6c166a59
    // Custom Data:
    backButtonText: ReactJssModule.TextField;
    // The GoToCartLink field.
    // Short description:
    // Field Type: General Link
    // Field ID: 35c8c0ab-8113-4b48-8dc6-dd8035708d6e
    // Custom Data:
    goToCartLink: ReactJssModule.LinkField;
    // The Heading field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: a3b0ae12-c267-4603-8d3f-9c38200d8e0a
    // Custom Data:
    heading: ReactJssModule.TextField;
    // The ShowBackButton field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 8c779791-dcb3-489a-b5e7-29c6061d5058
    // Custom Data:
    showBackButton: ReactJssModule.Field<boolean>;
  }
  // The CheckoutDonationData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/CheckoutDonationData
  // ID: 9a1f7ffe-c342-4689-922e-61680a7cc18a
  export interface CheckoutDonationDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The DonationAddToCartText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 438141d1-cf3f-491a-8446-652b7aed0ade
    // Custom Data:
    donationAddToCartText: ReactJssModule.TextField;
    // The DonationHeader field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 083b673b-4345-44a7-ac2e-988d1713d9fc
    // Custom Data:
    donationHeader: ReactJssModule.TextField;
    // The DonationMadeSubText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 2d1da220-c44a-4ab6-976e-c61dbd655597
    // Custom Data:
    donationMadeSubText: ReactJssModule.TextField;
    // The DonationMadeText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 21353792-43f6-4cf8-b11e-707d8e7bb86d
    // Custom Data:
    donationMadeText: ReactJssModule.TextField;
    // The DonationOtherAmountPlaceholderText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 9f82eb10-4338-4f9b-aaeb-eb2ca9fcab0b
    // Custom Data:
    donationOtherAmountPlaceholderText: ReactJssModule.TextField;
    // The DonationSubHeader field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 919c0bef-5c86-4e5c-b749-ea160d8edc0e
    // Custom Data:
    donationSubHeader: ReactJssModule.TextField;
  }
  // The Checkout Folder template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Checkout Folder
  // ID: 8cc4ca69-c783-4d05-8880-5a8dc5688a4f
  export interface CheckoutFolderDataSource extends ReactJssModule.BaseDataSourceItem {
  }
  // The Checkout Navigation Rendering Parameters template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Rendering Parameters/Checkout Navigation Rendering Parameters
  // ID: d22423bc-190c-4767-9d87-ebebf5f1b18e
  export interface CheckoutNavigationRenderingParametersDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Hide Navigation field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 0af848f4-3790-46f6-b769-f4417c8ca903
    // Custom Data:
    hideNavigation: ReactJssModule.Field<boolean>;
  }
  // The Checkout Navigation Step template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Checkout Navigation Step
  // ID: 57cfa14b-718e-43c4-a1e0-5553ec8000b8
  export interface CheckoutNavigationStepDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Step Id field.
    // Short description:
    // Field Type: Integer
    // Field ID: cb7502d8-cf47-4919-a528-ac7dcc1b636a
    // Custom Data:
    stepId: ReactJssModule.Field<number>;
    // The Step Link field.
    // Short description:
    // Field Type: General Link
    // Field ID: 89ad9809-f42a-444b-b484-e2c2be27b3ff
    // Custom Data:
    stepLink: ReactJssModule.LinkField;
    // The Step Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 6a4ad5b5-01c6-483b-8d2f-bf4e777acdc9
    // Custom Data:
    stepTitle: ReactJssModule.TextField;
  }
  // The Checkout Navigation Steps template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Checkout Navigation Steps
  // ID: f266a718-18e6-4978-b827-5c6c81e43350
  export interface CheckoutNavigationStepsDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Mixed products field.
    // Short description:
    // Field Type: Multilist
    // Field ID: 1e74ff70-991e-42cb-ba2b-6045fc514461
    // Custom Data:
    mixedProducts: ReactJssModule.ItemList<ReactJssModule.BaseDataSourceItem>;
    // The Virtual products field.
    // Short description:
    // Field Type: Multilist
    // Field ID: e6cbc372-49b4-404d-a02a-af58af7f8003
    // Custom Data:
    virtualProducts: ReactJssModule.ItemList<ReactJssModule.BaseDataSourceItem>;
  }
  // The Checkout Settings template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Settings/Checkout Settings
  // ID: 54c627b0-7790-40b3-978b-93d1de2857e4
  export interface CheckoutSettingsDataSource extends ReactJssModule.BaseDataSourceItem {
  }
  // The Checkout Step Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Checkout Step Data
  // ID: 76e62021-2e33-48bd-86ec-60d3f8bfad56
  export interface CheckoutStepDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Button Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 24aaf99c-1449-4344-b6f2-a37018414501
    // Custom Data:
    buttonText: ReactJssModule.TextField;
    // The Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: a294e893-34eb-452d-9a7c-cf8f7d229f49
    // Custom Data:
    title: ReactJssModule.TextField;
  }
  // The ConfirmationAccessItemsData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/ConfirmationAccessItemsData
  // ID: e66b582c-235a-4240-b268-2f28310887c9
  export interface ConfirmationAccessItemsDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The CoursesButtonLink field.
    // Short description:
    // Field Type: General Link
    // Field ID: cded1ad3-4027-471b-9f4f-b508303c5752
    // Custom Data:
    coursesButtonLink: ReactJssModule.LinkField;
    // The CoursesText field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: e0926c3c-1cdb-407e-b92d-8d4ce0a5fcc7
    // Custom Data:
    coursesText: ReactJssModule.TextField;
    // The DashboardButtonLink field.
    // Short description:
    // Field Type: General Link
    // Field ID: 1147b3f6-9b5f-4708-a908-42e14fbf3c24
    // Custom Data:
    dashboardButtonLink: ReactJssModule.LinkField;
    // The DashboardText field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: 74064b98-5359-4a83-8c10-8d8e6cb51aba
    // Custom Data:
    dashboardText: ReactJssModule.TextField;
    // The EligibleProductTypesToViewCourses field.
    // Short description:
    // Field Type: Multilist
    // Field ID: d4550c38-7c5d-4e3b-966d-fb44a0fbd5f3
    // Custom Data:
    eligibleProductTypesToViewCourses: ReactJssModule.ItemList<ReactJssModule.BaseDataSourceItem>;
    // The Header field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: c8328c4c-26fd-4563-95f4-1d596e8d1145
    // Custom Data:
    header: ReactJssModule.TextField;
  }
  // The ConfirmationContainerData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/ConfirmationContainerData
  // ID: 933b6f7f-bfa4-456f-ae36-d87e5bf34eb0
  export interface ConfirmationContainerDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The PendingOrderTitle field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 4ab87d62-6354-48d2-ba2f-3747e0039801
    // Custom Data:
    pendingOrderTitle: ReactJssModule.TextField;
    // The QuoteTitle field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: cf74a28d-c5e1-4e3d-8063-2f005139b131
    // Custom Data:
    quoteTitle: ReactJssModule.TextField;
    // The Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: f35841e1-5e81-4992-97ee-fdbe5a06b8ea
    // Custom Data:
    title: ReactJssModule.TextField;
  }
  // The ConfirmationDetailsData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/ConfirmationDetailsData
  // ID: ec050c0a-da75-4903-988d-4247e51095ae
  export interface ConfirmationDetailsDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The EmailSentText field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: b4ca8985-9b4f-4c76-a778-0ac4167ef6c5
    // Custom Data:
    emailSentText: ReactJssModule.TextField;
    // The GeneralBannerInfoForPendingOpenOrders field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 7da93986-697c-43e0-8c8d-d828ba426471
    // Custom Data:
    generalBannerInfoForPendingOpenOrders: ReactJssModule.TextField;
    // The NotRecievedEmailText field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: 5b26716f-4419-45a4-a5a2-d8340fb78756
    // Custom Data:
    notRecievedEmailText: ReactJssModule.TextField;
    // The QuoteSentText field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: c61f577c-51dd-4683-8a94-b2a83d73fbc4
    // Custom Data:
    quoteSentText: ReactJssModule.TextField;
  }
  // The ConfirmationFolder template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/ConfirmationFolder
  // ID: 9030f3a6-2642-48af-ac57-320b2188015d
  export interface ConfirmationFolderDataSource extends ReactJssModule.BaseDataSourceItem {
  }
  // The Confirmation Page Footer Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Confirmation Page Footer Data
  // ID: a4829610-0df2-472e-96e0-e9ae43732afa
  export interface ConfirmationPageFooterDataDataSource extends ReactJssModule.BaseDataSourceItem {
  }
  // The Confirmation Payment Method Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Confirmation Payment Method Data
  // ID: 070bca51-8f8e-41ba-a025-62341d919606
  export interface ConfirmationPaymentMethodDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The imageLookUp field.
    // Short description:
    // Field Type: Name Value List
    // Field ID: 8238bfc2-a114-4746-be7c-65bdf1eafa3c
    // Custom Data:
    imageLookUp: ReactJssModule.TextField;
  }
  // The Congatulations Gift Membership template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Congatulations Gift Membership
  // ID: c5135fca-cfd0-409e-9031-4f62ee03625e
  export interface CongatulationsGiftMembershipDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Heading field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: efc50d87-cac4-42fb-9f56-6550d3970bfc
    // Custom Data:
    heading: ReactJssModule.TextField;
    // The Text field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 159ac32f-fb4d-4817-9d0b-d5d1fff65ea3
    // Custom Data:
    text: ReactJssModule.TextField;
  }
  // The Congratulations Certification template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Congratulations Certification
  // ID: 186c9b38-0c8b-4b58-bb8c-9524cb51f7c4
  export interface CongratulationsCertificationDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Congratulations Text field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 45630122-5cfc-4309-a305-c2a8f31ffa37
    // Custom Data:
    congratulationsText: ReactJssModule.TextField;
  }
  // The Congratulations Membership template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Congratulations Membership
  // ID: 10eae8c9-0f23-4f28-8387-5966dbe8be6c
  export interface CongratulationsMembershipDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Description field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 496b5054-8d0e-4d66-9370-cb298cf38151
    // Custom Data:
    description: ReactJssModule.TextField;
    // The MembershipDescriptionWithoutRenewalStatement field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 4008fe49-a9a4-4cec-9e8d-bc11a49f1e12
    // Custom Data:
    membershipDescriptionWithoutRenewalStatement: ReactJssModule.TextField;
    // The Student Description field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 3b573eee-6b6c-4587-9b56-c5b361e62089
    // Custom Data:
    studentDescription: ReactJssModule.TextField;
    // The Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: feb57d55-f208-4d57-81e7-9dbd11503407
    // Custom Data:
    title: ReactJssModule.TextField;
  }
  // The ContactUsNow template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/ContactUsNow
  // ID: c422339a-1f48-4dcd-99b8-2a2ee6be96a1
  export interface ContactUsNowDataSource extends ReactJssModule.BaseDataSourceItem {
    // The ContactUsLink field.
    // Short description:
    // Field Type: General Link
    // Field ID: 59fb7856-6381-4b36-a5f1-0addacb97aa4
    // Custom Data:
    contactUsLink: ReactJssModule.LinkField;
    // The Header field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: b8c41268-3e4e-4e7c-b938-74d3003aeeb8
    // Custom Data:
    header: ReactJssModule.TextField;
    // The SubText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 8d72f01b-9396-4a6d-a5eb-6d6a0ee61b1f
    // Custom Data:
    subText: ReactJssModule.TextField;
  }
  // The Continue Shopping Button template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Continue Shopping Button
  // ID: 46b4f640-2b0a-48ba-997d-77a19ea5b048
  export interface ContinueShoppingButtonDataSource extends ReactJssModule.BaseDataSourceItem {
    // The continue shopping button text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 8e461f6d-d8ae-4668-a1d6-ed70802cc72c
    // Custom Data:
    continueShoppingButtonText: ReactJssModule.TextField;
    // The continue shopping link field.
    // Short description:
    // Field Type: General Link
    // Field ID: ff6a4db0-017c-4eb8-bcf5-efa97b7a84c7
    // Custom Data:
    continueShoppingLink: ReactJssModule.LinkField;
  }
  // The convertquote template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/convertquote
  // ID: dd5bc92a-0233-4f4a-a9d2-6f473990ede5
  export interface ConvertquoteDataSource extends ReactJssModule.BaseDataSourceItem {
    // The componentImage field.
    // Short description:
    // Field Type: Image
    // Field ID: fda37110-7889-460e-9838-03d4f0a7b7f7
    // Custom Data:
    componentImage: ReactJssModule.ImageField;
    // The problemDescriptionText field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 6d3bb524-f4fd-4308-bcf7-4841987300c1
    // Custom Data:
    problemDescriptionText: ReactJssModule.TextField;
  }
  // The Course Library Link template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Course Library Link
  // ID: d47f4f92-96e8-458b-8e3e-7736ffaf968d
  export interface CourseLibraryLinkDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Eligible Product Types to Show Link field.
    // Short description:
    // Field Type: Multilist
    // Field ID: 1a03e9f7-581c-41ba-8533-4b0c6829e4d4
    // Custom Data: name=EligibleProductTypesToShowLink
    eligibleProductTypesToShowLink: ReactJssModule.ItemList<ReactJssModule.BaseDataSourceItem>;
    // The Link Text field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 44d60258-3b50-4d27-9a45-9cc03f3cff17
    // Custom Data:
    linkText: ReactJssModule.TextField;
  }
  // The Create Quote Button Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Create Quote Button Data
  // ID: c05b1cb0-c6b1-474a-9ea3-a8d2aa2d6a84
  export interface CreateQuoteButtonDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The ButtonText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 7d39ca00-1f81-49b5-9f09-471d0d930528
    // Custom Data:
    buttonText: ReactJssModule.TextField;
    // The Confirmation Page Url field.
    // Short description:
    // Field Type: General Link
    // Field ID: 2865a7e7-4fbb-41b7-9348-ffd855173cf2
    // Custom Data:
    confirmationPageUrl: ReactJssModule.LinkField;
  }
  // The Currency Confirmation template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Currency Confirmation
  // ID: 8538a146-6ff1-4686-989d-5da0f4f1f746
  export interface CurrencyConfirmationDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Checkout Button Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 2d2128af-5975-4556-92d2-1101a88635e4
    // Custom Data:
    checkoutButtonText: ReactJssModule.TextField;
    // The Pilot Text field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 47d46a0e-860e-4131-b2cb-5457d0826b02
    // Custom Data:
    pilotText: ReactJssModule.TextField;
    // The Preference Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 34882baa-fbf4-4212-80fc-0e1dc3c533a0
    // Custom Data:
    preferenceText: ReactJssModule.TextField;
    // The Preference Texts field.
    // Short description:
    // Field Type: Name Value List
    // Field ID: ef40d425-b6ae-4d16-8bf9-098eb93dc28a
    // Custom Data:
    preferenceTexts: ReactJssModule.TextField;
    // The Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: e52f20be-6fc4-4001-b3b5-eb2efa2066e7
    // Custom Data:
    title: ReactJssModule.TextField;
  }
  // The Discount Component Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Discount Component Data
  // ID: 355ac908-29ed-497a-91a0-ea30c3b38111
  export interface DiscountComponentDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Promo Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 9da7dae4-0948-40a9-bd11-1615353fb1dc
    // Custom Data:
    promoTitle: ReactJssModule.TextField;
    // The Voucher Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 2ef6b614-3b5b-45e5-b323-5f690058623e
    // Custom Data:
    voucherTitle: ReactJssModule.TextField;
  }
  // The Donation Component Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Donation Component Data
  // ID: 329c4051-88fc-4979-a8cd-7ca63a507172
  export interface DonationComponentDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Add To Cart Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 00011827-cb1e-48b3-80b6-4fdd924d98eb
    // Custom Data:
    addToCartText: ReactJssModule.TextField;
    // The Donate To Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: cc6d3be1-bebc-4f7f-ad84-b59cea837e95
    // Custom Data:
    donateToText: ReactJssModule.TextField;
    // The Donation Made Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: f79ae4b5-8323-4d0e-9632-cdb12b1a16f2
    // Custom Data:
    donationMadeText: ReactJssModule.TextField;
    // The Learn More Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: c6628cb3-d810-4d2f-9bff-aa8649587024
    // Custom Data:
    learnMoreText: ReactJssModule.TextField;
    // The Placeholder Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 058c3891-7c4d-464d-a161-8b2553b7a913
    // Custom Data:
    placeholderText: ReactJssModule.TextField;
  }
  // The Donation Rendering Parameters template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Rendering Parameters/Donation Rendering Parameters
  // ID: 81b82cde-de46-4a85-a743-a7bea528f353
  export interface DonationRenderingParametersDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Donation Data Source field.
    // Short description:
    // Field Type: Droplist
    // Field ID: f1563f56-1de4-4973-a7c6-f1349263090f
    // Custom Data:
    donationDataSource: ReactJssModule.TextField;
    // The Fixed Amounts field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 276e2726-1de3-48f3-a1f2-df879ff2fe32
    // Custom Data:
    fixedAmounts: ReactJssModule.TextField;
    // The Readonly field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: e662b13b-7913-474b-8a12-863e8de8218a
    // Custom Data:
    readonly: ReactJssModule.Field<boolean>;
  }
  // The Feature Switch template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Settings/Feature Switch
  // ID: cf066ab5-d371-4a4f-8d01-548e1f349bb8
  export interface FeatureSwitchDataSource extends ReactJssModule.BaseDataSourceItem {
    // The ShouldUpdateGetCartCache field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 6e460d08-f2af-4c85-a827-a212a97e07c1
    // Custom Data:
    shouldUpdateGetCartCache: ReactJssModule.Field<boolean>;
  }
  // The General Order Info template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/General Order Info
  // ID: 65b26cdb-4f6f-4b3a-8a88-36769a4ac001
  export interface GeneralOrderInfoDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Banner Info For Pending Open Orders field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 054d809b-3996-43fe-bfca-7c66469c01fc
    // Custom Data:
    bannerInfoForPendingOpenOrders: ReactJssModule.TextField;
    // The Email Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 45c0aec5-526f-426a-a72c-65b4f94cd2ed
    // Custom Data:
    emailText: ReactJssModule.TextField;
    // The General Info For Pending Open Orders field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 3fc7ab76-821b-4b0c-a34f-f21c50d69575
    // Custom Data:
    generalInfoForPendingOpenOrders: ReactJssModule.TextField;
    // The Open Quote Button Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 4ac190d1-caaa-4d3b-b523-eaa4ed8815e9
    // Custom Data:
    openQuoteButtonText: ReactJssModule.TextField;
    // The Print Invoice Button Link field.
    // Short description:
    // Field Type: General Link
    // Field ID: bcaecf0a-04d9-45d4-b58d-271e23b37e19
    // Custom Data:
    printInvoiceButtonLink: ReactJssModule.LinkField;
    // The Print Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 5fe5cc29-723a-46d8-b15f-4d6e22837867
    // Custom Data:
    printText: ReactJssModule.TextField;
    // The Review Order Status field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 49614c24-95b3-493b-b200-7f7db0c972a5
    // Custom Data:
    reviewOrderStatus: ReactJssModule.TextField;
    // The Standard Banner Info field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 9e210be7-1121-48db-b54f-23b97f207860
    // Custom Data:
    standardBannerInfo: ReactJssModule.TextField;
  }
  // The GetOrder Failed Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/GetOrder Failed Data
  // ID: 4630305a-4872-4a94-9e04-ee7df6731088
  export interface GetOrderFailedDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Header field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 387ff6b7-863b-42b0-b367-db368068b7b6
    // Custom Data:
    header: ReactJssModule.TextField;
    // The Message Body field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: fe46fa4d-6383-4d2c-9cd4-7dba3b6447d9
    // Custom Data:
    messageBody: ReactJssModule.TextField;
  }
  // The Global Footer Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Global Footer Data
  // ID: a00f3ed0-34fe-418e-864a-6c818799688f
  export interface GlobalFooterDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Advertising Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: e31cc922-35d8-4772-9fe7-07c3cafd88e4
    // Custom Data:
    advertisingText: ReactJssModule.TextField;
    // The Copyright Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 0363a5ba-43bf-4691-a5fa-f63b389ffe1b
    // Custom Data:
    copyrightText: ReactJssModule.TextField;
    // The Privacy Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: ************************************
    // Custom Data:
    privacyText: ReactJssModule.TextField;
    // The Processing Country field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 3a3e15d4-4480-4548-8660-2a401f684e9f
    // Custom Data:
    processingCountry: ReactJssModule.TextField;
    // The Sitemap Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 24d45f18-f40c-46da-8e62-852dd930e1c5
    // Custom Data:
    sitemapText: ReactJssModule.TextField;
    // The Terms and Conditions Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: eeef88e0-6bb0-4a8d-ad84-d850314aa57f
    // Custom Data:
    termsAndConditionsText: ReactJssModule.TextField;
    // The Terms of Use Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: c001c0ad-5fce-431f-9f43-4ae101d313c5
    // Custom Data:
    termsOfUseText: ReactJssModule.TextField;
  }
  // The Google Analytics Event Rendering Parameters template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Rendering Parameters/Google Analytics Event Rendering Parameters
  // ID: bbb59dd1-70e4-492f-a1f9-69e79c67a8bb
  export interface GoogleAnalyticsEventRenderingParametersDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Event Body field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 777818fb-0c51-4c70-ad27-aa0b535095a4
    // Custom Data:
    eventBody: ReactJssModule.TextField;
    // The Event type field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 767ad006-3b8f-48a3-86a2-4804f926f34e
    // Custom Data:
    eventType: ReactJssModule.TextField;
  }
  // The Go To Checkout Button Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Go To Checkout Button Data
  // ID: 044a26bf-d809-45de-81b7-7a12dc38f704
  export interface GoToCheckoutButtonDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The ButtonText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 95f0adda-ff8e-4423-a7fd-be0b96b2c9f9
    // Custom Data:
    buttonText: ReactJssModule.TextField;
    // The Checkout Page Url field.
    // Short description:
    // Field Type: General Link
    // Field ID: 1eb46e9d-e9ad-492a-be15-60bd2cbb966c
    // Custom Data:
    checkoutPageUrl: ReactJssModule.LinkField;
    // The LocalCurrency field.
    // Short description:
    // Field Type: Droplink
    // Field ID: c91c4565-4228-48f1-85e8-e45881546755
    // Custom Data:
    localCurrency: ReactJssModule.Item<ReactJssModule.BaseDataSourceItem>;
  }
  // The Live Person Chat Rendering Parameters template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Rendering Parameters/Live Person Chat Rendering Parameters
  // ID: f0a59d6a-7756-4b26-9f10-ab04f249fe64
  export interface LivePersonChatRenderingParametersDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Chat Script Body field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 87521e4e-1558-49ee-b586-7a31c77a094a
    // Custom Data:
    chatScriptBody: ReactJssModule.TextField;
    // The livePersonId field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: ce39f825-8603-4b2f-a3a8-a169271899ed
    // Custom Data:
    livePersonId: ReactJssModule.TextField;
  }
  // The Local Chapters Search Parameters Template template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Local Chapters Search Parameters Template
  // ID: 7964d8c4-68c1-4a5f-939c-88f9bec7a2fe
  export interface LocalChaptersSearchParametersTemplateDataSource extends ReactJssModule.BaseDataSourceItem {
    // The distance field.
    // Short description:
    // Field Type: Integer
    // Field ID: d292e9f1-9f0d-47ac-ae64-d5fc56c03f84
    // Custom Data:
    distance: ReactJssModule.Field<number>;
    // The maxResult field.
    // Short description:
    // Field Type: Integer
    // Field ID: cf0dee42-013b-4ed7-96ac-b410087e5601
    // Custom Data:
    maxResult: ReactJssModule.Field<number>;
  }
  // The Membership Automatic Renewal Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Membership Automatic Renewal Data
  // ID: 9a9f20c4-fbe4-49b2-be4d-dbe6d5cc488e
  export interface MembershipAutomaticRenewalDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Header Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 1972ef95-6f4e-4f04-9b59-90bad6146a04
    // Custom Data:
    headerText: ReactJssModule.TextField;
    // The Text field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: eae2ea93-e07b-44a1-9da1-0115853e64ec
    // Custom Data:
    text: ReactJssModule.TextField;
  }
  // The MembershipBenefitsCardData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/MembershipBenefitsCardData
  // ID: 00ec4c3e-d5e6-40af-8694-ff54bf79497f
  export interface MembershipBenefitsCardDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The HeadingText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 0c49a53d-a6d9-4328-a248-7ddf06e9196c
    // Custom Data:
    headingText: ReactJssModule.TextField;
    // The Link field.
    // Short description:
    // Field Type: General Link
    // Field ID: bf3885d9-6fa5-446a-b448-7737bb87f7d0
    // Custom Data:
    link: ReactJssModule.LinkField;
    // The Text field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 8f7e882b-46a7-4679-bd75-c5a2a360d541
    // Custom Data:
    text: ReactJssModule.TextField;
  }
  // The MembershipBenefitsCardDataFolder template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/MembershipBenefitsCardDataFolder
  // ID: 245408cf-86ec-4da7-b650-6b9891f57af8
  export interface MembershipBenefitsCardDataFolderDataSource extends ReactJssModule.BaseDataSourceItem {
  }
  // The MembershipBenefitsCarouselData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/MembershipBenefitsCarouselData
  // ID: 2e147a6d-4c76-4ccb-beea-3678613a39ad
  export interface MembershipBenefitsCarouselDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Cards field.
    // Short description:
    // Field Type: Multilist
    // Field ID: 7aefd21f-244e-4309-af4d-ee7a723b554a
    // Custom Data:
    cards: ReactJssModule.ItemList<ReactJssModule.BaseDataSourceItem>;
  }
  // The MembershipCongratulationsData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/MembershipCongratulationsData
  // ID: be8bd67a-67ad-46f4-a15f-f7a54e483b02
  export interface MembershipCongratulationsDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The GiftMembershipDescription field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 23d32d26-c14f-4ce6-8d8c-646d586c643f
    // Custom Data:
    giftMembershipDescription: ReactJssModule.TextField;
    // The GiftMembershipTitle field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: ca906d6a-304e-4cc8-ab35-5d40ecdd6e65
    // Custom Data:
    giftMembershipTitle: ReactJssModule.TextField;
    // The MembershipDescription field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 94eb25c6-3b48-48a9-8f56-3210c0641f53
    // Custom Data:
    membershipDescription: ReactJssModule.TextField;
    // The PurchaseTermsWithAutoRenewalStatement field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 8f90f0de-a2c9-44dc-9f7f-03c7e9f08747
    // Custom Data:
    purchaseTermsWithAutoRenewalStatement: ReactJssModule.TextField;
    // The PurchaseTermsWithoutAutoRenewStatement field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: d1818cf7-ca50-4939-a1a5-c0fa99f63738
    // Custom Data:
    purchaseTermsWithoutAutoRenewStatement: ReactJssModule.TextField;
    // The StudentMembershipDescription field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: f157ccff-51ce-4b0b-abae-81e4f3808489
    // Custom Data:
    studentMembershipDescription: ReactJssModule.TextField;
    // The Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: dd56682d-b4cb-45ac-8f4d-f4788fb46caa
    // Custom Data:
    title: ReactJssModule.TextField;
  }
  // The Membership Promo Modal Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Membership Promo Modal Data
  // ID: 1179143f-e6b1-41d0-9fda-88876e4e9772
  export interface MembershipPromoModalDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Add to Cart Button Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 290afceb-a251-4ede-a265-d2e8b8cc3ce6
    // Custom Data:
    addToCartButtonText: ReactJssModule.TextField;
    // The Cancel Button Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 6f8b11e1-7139-4474-b32d-82a031695313
    // Custom Data:
    cancelButtonText: ReactJssModule.TextField;
    // The List Items field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 77eb4005-57ef-4c83-9b06-f099e4f4d33f
    // Custom Data:
    listItems: ReactJssModule.TextField;
    // The Membership Items field.
    // Short description:
    // Field Type: Multilist
    // Field ID: 1ced9246-fffe-41b9-a6a9-199728b650ae
    // Custom Data:
    membershipItems: ReactJssModule.ItemList<ReactJssModule.BaseDataSourceItem>;
    // The Options Link field.
    // Short description:
    // Field Type: General Link
    // Field ID: 1b02c3ed-047e-4730-8358-e9267bd7b7ad
    // Custom Data:
    optionsLink: ReactJssModule.LinkField;
    // The SheerIDParameters field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 456e7905-9148-411e-8c0a-6e2c9668f0be
    // Custom Data:
    sheerIDParameters: ReactJssModule.Item<ReactJssModule.BaseDataSourceItem>;
    // The Student Membership Validation Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: ca6a915f-f89d-4ab6-abde-0782087a6c95
    // Custom Data:
    studentMembershipValidationText: ReactJssModule.TextField;
    // The Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 19a6235e-6b03-4d65-826a-64c914515654
    // Custom Data:
    title: ReactJssModule.TextField;
    // The Total Savings Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: cc7ea172-aac6-4129-aeda-2a655c5e16b7
    // Custom Data:
    totalSavingsText: ReactJssModule.TextField;
  }
  // The Membership Terms And Conditions Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Membership Terms And Conditions Data
  // ID: cd5f92a9-4830-452a-b41c-54b6a41874c0
  export interface MembershipTermsAndConditionsDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Agree Text field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: eba2367c-55e9-4c52-9d00-8805afa48081
    // Custom Data:
    agreeText: ReactJssModule.TextField;
    // The Optional Agree Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: d2e43e1b-1c7a-420e-b0d9-3acd4fef758e
    // Custom Data:
    optionalAgreeText: ReactJssModule.TextField;
    // The Retiree Text field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: 4674e971-12f9-46a5-9d88-702638fa817f
    // Custom Data:
    retireeText: ReactJssModule.TextField;
    // The Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: be820b1b-148d-490c-a078-8823a81e2ea7
    // Custom Data:
    title: ReactJssModule.TextField;
  }
  // The MembershipUpsellData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/MembershipUpsellData
  // ID: 92d970b3-cb54-4608-b7d8-08c865e175e4
  export interface MembershipUpsellDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The ActionText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 5916c534-d5ee-4e41-8b53-840f6ed923c2
    // Custom Data:
    actionText: ReactJssModule.TextField;
    // The Body field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: ec9ec963-7183-487d-9d3f-654b8079a7e8
    // Custom Data:
    body: ReactJssModule.TextField;
    // The BodyNoDiscount field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 037dd51f-1177-47fd-a1bf-a052f44c35c5
    // Custom Data:
    bodyNoDiscount: ReactJssModule.TextField;
    // The Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: d6bd48a1-9037-42b2-b605-77850e80c449
    // Custom Data:
    title: ReactJssModule.TextField;
    // The TitleNoDiscount field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: cf9e7883-25f7-4b16-b5bc-609917064e15
    // Custom Data:
    titleNoDiscount: ReactJssModule.TextField;
  }
  // The MembershipUpsellNewData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/MembershipUpsellNewData
  // ID: 49e657d1-4651-4bb9-80ac-21b9a875dc5c
  export interface MembershipUpsellNewDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The ActionText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 028e82a8-ed4c-491b-be92-c3926d3e290d
    // Custom Data:
    actionText: ReactJssModule.TextField;
    // The Body field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 913cbb9b-2bae-49cd-bd25-b03ef8aaf485
    // Custom Data:
    body: ReactJssModule.TextField;
    // The BodyNoDiscount field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 27874113-5714-4d5b-b4c7-78938273845f
    // Custom Data:
    bodyNoDiscount: ReactJssModule.TextField;
  }
  // The Order Confirmation Summary Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Order Confirmation Summary Data
  // ID: 2441d74e-3f66-412b-b0be-89e7e24ff7ba
  export interface OrderConfirmationSummaryDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Header field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: c8344e1f-4414-4372-b9ca-6af6b4ae693e
    // Custom Data:
    header: ReactJssModule.TextField;
    // The Order discount field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: acf10980-1bba-4bfb-9b91-add3a79fbfd4
    // Custom Data:
    orderDiscount: ReactJssModule.TextField;
    // The Order total field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 54d0c72b-29c0-4fff-8f1c-bcd1a6f1a2f8
    // Custom Data:
    orderTotal: ReactJssModule.TextField;
    // The Quote Header field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 54c7d12b-ff1a-4d8f-bcc4-0aa76ddda3f1
    // Custom Data:
    quoteHeader: ReactJssModule.TextField;
    // The Subtotal field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 79a7c00a-4d7b-4a7b-8efd-266e279f6a12
    // Custom Data:
    subtotal: ReactJssModule.TextField;
    // The Taxes field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 499c31bc-afd4-498e-b344-fef9a2c9adfd
    // Custom Data:
    taxes: ReactJssModule.TextField;
    // The Voucher discount field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 1c36ce4c-c4c6-4abc-bfa2-69e21458005c
    // Custom Data:
    voucherDiscount: ReactJssModule.TextField;
  }
  // The Order Review Payment template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Order Review Payment/Order Review Payment
  // ID: e353f941-9075-4120-9d15-8449c6e3312f
  export interface OrderReviewPaymentDataSource extends ReactJssModule.BaseDataSourceItem {
    // The BillingAddress field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: b48b0aba-0b7e-4749-8036-b0a64506a881
    // Custom Data:
    billingAddress: ReactJssModule.TextField;
    // The PaymentMethods field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 3194781b-bddc-498a-a453-fc8deb7485f4
    // Custom Data:
    paymentMethods: ReactJssModule.TextField;
    // The PurchaseOrder field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 74d82428-4d66-4fdc-949b-13f5e7a34086
    // Custom Data:
    purchaseOrder: ReactJssModule.TextField;
    // The VoucherAsPayment field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 41179a35-3ee4-43f8-bd74-862bbf080d12
    // Custom Data:
    voucherAsPayment: ReactJssModule.TextField;
  }
  // The Order Review Payment Folder template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Order Review Payment/Order Review Payment Folder
  // ID: 12ef9d96-959a-4210-b79d-7417fdf8a974
  export interface OrderReviewPaymentFolderDataSource extends ReactJssModule.BaseDataSourceItem {
  }
  // The Order Summary Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Order Summary Data
  // ID: 85d5d7a4-84d7-485e-a0f1-bc8eb8ee84fe
  export interface OrderSummaryDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Checkout Button Text field.
    // Short description: [Deprecated]
    // Field Type: Single-Line Text
    // Field ID: d71cbad5-2157-42a3-a64c-f48825fccce2
    // Custom Data:
    checkoutButtonText: ReactJssModule.TextField;
    // The Estimated Tax field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 017d8701-3629-4604-8dfd-9413561546ea
    // Custom Data:
    estimatedTax: ReactJssModule.TextField;
    // The Estimated Total field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: d96e6f00-c16f-465f-b41a-cae94fd161c6
    // Custom Data:
    estimatedTotal: ReactJssModule.TextField;
    // The GoToCartLink field.
    // Short description:
    // Field Type: General Link
    // Field ID: bc15aaca-d136-4d97-a1e8-86dccc4b72fc
    // Custom Data:
    goToCartLink: ReactJssModule.LinkField;
    // The Go To Checkout Url field.
    // Short description: [Deprecated]
    // Field Type: Single-Line Text
    // Field ID: bcf8be2a-89c3-481b-95ae-84855f29b936
    // Custom Data:
    goToCheckoutUrl: ReactJssModule.TextField;
    // The Order Discount field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: e6520a5c-23c8-41be-ad6d-69a6efca5b62
    // Custom Data:
    orderDiscount: ReactJssModule.TextField;
    // The Subtotal field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 81ab94dc-acde-43d5-891a-b456ece203cc
    // Custom Data:
    subtotal: ReactJssModule.TextField;
    // The Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 0355c5c6-8ee6-4326-abae-68b3d1c1c93c
    // Custom Data:
    title: ReactJssModule.TextField;
    // The Total Cart Discount field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: fc5cab7b-61bf-4590-9793-074e3a155e86
    // Custom Data:
    totalCartDiscount: ReactJssModule.TextField;
    // The Voucher Order Discount field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 80f752fb-6e85-4fae-8907-eaa9b92412b5
    // Custom Data:
    voucherOrderDiscount: ReactJssModule.TextField;
  }
  // The Order Summary Mobile Accordion Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Order Summary Mobile Accordion Data
  // ID: 1d42e4e8-ae13-4ab3-bfdc-886b60a5c7bf
  export interface OrderSummaryMobileAccordionDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Hide Button Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: d6f85730-e614-4197-bf5e-51618d35eb13
    // Custom Data:
    hideButtonTitle: ReactJssModule.TextField;
    // The Show Button Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: a323f0da-5f46-4df4-99b6-606af70ceaa2
    // Custom Data:
    showButtonTitle: ReactJssModule.TextField;
  }
  // The Order Summary Mobile Rendering Parameters template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Rendering Parameters/Order Summary Mobile Rendering Parameters
  // ID: ff9ee7cc-f2d8-4542-b012-faa347be9e13
  export interface OrderSummaryMobileRenderingParametersDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Header Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: c3e40bf0-28bb-4139-96a7-c3c3277cc810
    // Custom Data:
    headerText: ReactJssModule.TextField;
    // The Show Button field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 65ddbc3a-f5b0-42c1-a521-8de3d1f66d44
    // Custom Data:
    showButton: ReactJssModule.Field<boolean>;
  }
  // The OrderSummaryRenderingParameters template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Rendering Parameters/OrderSummaryRenderingParameters
  // ID: fec63381-1ec9-43a3-8e41-7379331ad16d
  export interface OrderSummaryRenderingParametersDataSource extends ReactJssModule.BaseDataSourceItem {
    // The DisplayCartLines field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 5b124922-8c2e-4cd5-b484-62301ffee276
    // Custom Data:
    displayCartLines: ReactJssModule.Field<boolean>;
    // The HideGoToCheckoutButton field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 29894129-cf60-4c37-af5f-7513e519a311
    // Custom Data:
    hideGoToCheckoutButton: ReactJssModule.Field<boolean>;
  }
  // The PageLevelLoadingModalData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/PageLevelLoadingModalData
  // ID: 99c2fe4a-e06e-4387-94d4-a7cd9184fadb
  export interface PageLevelLoadingModalDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Message field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 5edd740d-2ecb-4a0b-849a-916305b11548
    // Custom Data:
    message: ReactJssModule.TextField;
    // The Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 2c912f1c-be1b-4eef-8fdb-8419c7022a1a
    // Custom Data:
    title: ReactJssModule.TextField;
  }
  // The PaymentAccordionData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/PaymentAccordionData
  // ID: 5770faed-ef53-4c93-961a-b4e2c599031d
  export interface PaymentAccordionDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The BillingExpandLinkText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: c4bf2f82-39cb-49e1-be18-ebfd5690ef23
    // Custom Data:
    billingExpandLinkText: ReactJssModule.TextField;
    // The BillingHeadingText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: e6bdb463-1bf1-4b7a-83f4-5f012db01011
    // Custom Data:
    billingHeadingText: ReactJssModule.TextField;
    // The ContactEmailTooltipText field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 1002bea8-0920-4c8b-9e32-a2326754b3e8
    // Custom Data:
    contactEmailTooltipText: ReactJssModule.TextField;
    // The ContactExpandLinkText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: b66a6ea3-43c3-413d-a6de-2af427073489
    // Custom Data:
    contactExpandLinkText: ReactJssModule.TextField;
    // The ContactHeadingText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: c7c3d82d-866a-4f6d-81e4-f3be1e3054c3
    // Custom Data:
    contactHeadingText: ReactJssModule.TextField;
    // The ContactPhoneToolTipText field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 8218bc0c-e875-4bfa-9847-f06f1e2f1c58
    // Custom Data:
    contactPhoneToolTipText: ReactJssModule.TextField;
    // The PaymentMethodExpandLinkText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 90eb0cfb-4dc9-454c-9db9-2a08b5082209
    // Custom Data:
    paymentMethodExpandLinkText: ReactJssModule.TextField;
    // The PaymentMethodHeadingText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: cd5f5642-9100-451e-8c7d-79cd531d0d63
    // Custom Data:
    paymentMethodHeadingText: ReactJssModule.TextField;
    // The PaymentSecureInformationText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 309dd2d3-602d-4eda-9624-d80b6b805509
    // Custom Data:
    paymentSecureInformationText: ReactJssModule.TextField;
  }
  // The PaymentAccordionRenderingParameters template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Rendering Parameters/PaymentAccordionRenderingParameters
  // ID: 44454d74-556d-4f94-87a9-143a175292b6
  export interface PaymentAccordionRenderingParametersDataSource extends ReactJssModule.BaseDataSourceItem {
    // The HidePaymentSection field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: f2321705-637a-44f7-b962-ab8f2356d0c3
    // Custom Data:
    hidePaymentSection: ReactJssModule.Field<boolean>;
    // The IsQuotePage field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 5eeefd54-5c7a-4dee-a383-65f02a0fb110
    // Custom Data:
    isQuotePage: ReactJssModule.Field<boolean>;
  }
  // The PaymentAddress template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/PaymentAddress/PaymentAddress
  // ID: 9d969a4a-e75d-4237-aade-3372d3288ec7
  export interface PaymentAddressDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Address Label field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: c7723a57-de99-4aec-b8ae-53c74f09e967
    // Custom Data:
    addressLabel: ReactJssModule.TextField;
  }
  // The PaymentAddressFolder template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/PaymentAddress/PaymentAddressFolder
  // ID: 934b7ee8-441d-406d-89bd-00a423eada7c
  export interface PaymentAddressFolderDataSource extends ReactJssModule.BaseDataSourceItem {
  }
  // The PaymentBillingData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/PaymentBillingData
  // ID: 04b1e545-2d1a-480a-9e44-8f3dafb657e7
  export interface PaymentBillingDataDataSource extends AutoSaveUserDataDataSource {
    // The AddAddressButtonText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 501749fb-a955-45a6-a18b-9480b5de397e
    // Custom Data:
    addAddressButtonText: ReactJssModule.TextField;
    // The AddressLine1Empty field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 75a03b59-6a8c-4b50-8aec-fc8671a77e04
    // Custom Data:
    addressLine1empty: ReactJssModule.TextField;
    // The AddressLine1Label field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 4866b101-d404-45cd-a9bf-a6a5726b0d1a
    // Custom Data:
    addressLine1label: ReactJssModule.TextField;
    // The AddressLine1Placeholder field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 41655a0e-1b74-41ad-b83a-e3adec1e3020
    // Custom Data:
    addressLine1placeholder: ReactJssModule.TextField;
    // The AddressLine2Label field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: d1e3eaca-6ee5-4767-b5f5-71c82084559c
    // Custom Data:
    addressLine2label: ReactJssModule.TextField;
    // The AddressLine2Placeholder field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: eededc02-5e37-43c7-8a3f-de9e01850fb9
    // Custom Data:
    addressLine2placeholder: ReactJssModule.TextField;
    // The CancelButtonText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 23dd9bb5-4766-4cf5-9345-f6b32fbda163
    // Custom Data:
    cancelButtonText: ReactJssModule.TextField;
    // The CityEmpty field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 7a952589-6785-48d7-bc0d-14ab04593ec4
    // Custom Data:
    cityEmpty: ReactJssModule.TextField;
    // The CityLabel field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 5a6c302f-3ae6-4c20-9d10-eb8b09ad0c3c
    // Custom Data:
    cityLabel: ReactJssModule.TextField;
    // The CityPlaceholder field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 4849055a-3090-4d8a-abf4-0c02c39d0468
    // Custom Data:
    cityPlaceholder: ReactJssModule.TextField;
    // The CountryEmpty field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 486e4247-0f57-4ebe-a6a2-f52c9ef3ccce
    // Custom Data:
    countryEmpty: ReactJssModule.TextField;
    // The CountryLabel field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: ad0da3ad-3305-4c24-b55e-d41bd0d6e408
    // Custom Data:
    countryLabel: ReactJssModule.TextField;
    // The CountryPlaceholder field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 23e8d7d4-2fdb-45ea-805c-c384f31c762c
    // Custom Data:
    countryPlaceholder: ReactJssModule.TextField;
    // The EditButtonText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 875ccc00-e88a-4004-ac01-78e04b3c2f89
    // Custom Data:
    editButtonText: ReactJssModule.TextField;
    // The HeadingText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 09c4c50d-f38a-4a54-bd68-c8cde60934ee
    // Custom Data:
    headingText: ReactJssModule.TextField;
    // The LoadingText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: d3216b2a-bed6-49d2-81ff-f58887d064c5
    // Custom Data:
    loadingText: ReactJssModule.TextField;
    // The PostalCodeEmpty field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 30f97923-9953-48a7-bbdf-481386cabd9a
    // Custom Data:
    postalCodeEmpty: ReactJssModule.TextField;
    // The PostalCodeLabel field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 7d1b469a-f65d-46cc-b5f3-b956bd7ea82b
    // Custom Data:
    postalCodeLabel: ReactJssModule.TextField;
    // The PostalCodePlaceholder field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: fa3e47e7-bebc-49f9-b54e-4a90c26db27b
    // Custom Data:
    postalCodePlaceholder: ReactJssModule.TextField;
    // The RequiredFieldsMessage field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 9e8be739-b6af-45dc-ae9d-fd9673d26ca3
    // Custom Data:
    requiredFieldsMessage: ReactJssModule.TextField;
    // The SaveAsPrimaryLabel field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 766391f2-b7a4-4435-ace0-aa33f640d87c
    // Custom Data:
    saveAsPrimaryLabel: ReactJssModule.TextField;
    // The StateProvinceEmpty field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: bdaf1b2a-e492-4d0e-84a2-ebdf7f9d211b
    // Custom Data:
    stateProvinceEmpty: ReactJssModule.TextField;
    // The StateProvinceLabel field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 531dc32c-37be-4bf9-bfa0-589e0ea41f10
    // Custom Data:
    stateProvinceLabel: ReactJssModule.TextField;
    // The StateProvincePlaceholder field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 9e9305fe-9f58-48b9-a5a5-5ab47ebb5b57
    // Custom Data:
    stateProvincePlaceholder: ReactJssModule.TextField;
    // The SubmitButtonText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 86d5965c-1e16-4d37-ba4f-57c3bb990830
    // Custom Data:
    submitButtonText: ReactJssModule.TextField;
    // The UseAddressButtonText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: d5c104a3-a9e0-4323-a913-41af0be987e9
    // Custom Data:
    useAddressButtonText: ReactJssModule.TextField;
  }
  // The Payment Contact Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Payment Contact Data
  // ID: 34c0f7db-6a35-4e1d-8f99-aa99cd0bb93d
  export interface PaymentContactDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Dialing Code Label field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 25c18c93-e965-4f7b-b249-4e1dce93c813
    // Custom Data:
    dialingCodeLabel: ReactJssModule.TextField;
    // The DialingCountryCodeRequiredMessage field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 6dad62e1-7f4b-421b-a633-9811f1b5831f
    // Custom Data:
    dialingCountryCodeRequiredMessage: ReactJssModule.TextField;
    // The EmailAddressToolTipMessage field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 9e3bb5c3-24fe-4686-8ca7-622a55d15d4b
    // Custom Data:
    emailAddressToolTipMessage: ReactJssModule.TextField;
    // The email-field-label field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 56593a34-7faa-48e0-bd54-93bbd4f83ddd
    // Custom Data:
    emailFieldLabel: ReactJssModule.TextField;
    // The EmailFieldPlaceholderText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 434e70cf-3d5a-4b70-964a-e00c03fe1f0a
    // Custom Data:
    emailFieldPlaceholderText: ReactJssModule.TextField;
    // The EmailInvalidMessage field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 7ba732ec-e6ac-4fa3-b5e5-cfb65ec22da3
    // Custom Data:
    emailInvalidMessage: ReactJssModule.TextField;
    // The EmailRequiredMessage field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 8712ca1c-09b1-47a7-8474-f678cc8a679f
    // Custom Data:
    emailRequiredMessage: ReactJssModule.TextField;
    // The name-change-disclaimer field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: d4376d7f-14f3-4a21-a15d-4d98e3cce9b8
    // Custom Data:
    nameChangeDisclaimer: ReactJssModule.TextField;
    // The Phone Character Limit Message field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 3cb45669-be04-4669-a538-0a7642ad1a83
    // Custom Data:
    phoneCharacterLimitMessage: ReactJssModule.TextField;
    // The Phone Number Label field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: e45344ea-fe19-4df8-8b0c-6736823af875
    // Custom Data:
    phoneNumberLabel: ReactJssModule.TextField;
    // The Phone Number Placeholder Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: a66fe0c5-9962-49cd-adc9-909969ae0024
    // Custom Data:
    phoneNumberPlaceholderText: ReactJssModule.TextField;
    // The PhoneNumberToolTipMessage field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 5830762b-e31f-4cc9-a83c-4ecd90dedb69
    // Custom Data:
    phoneNumberToolTipMessage: ReactJssModule.TextField;
    // The PhoneRequiredMessage field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 2c106d5d-82bf-435a-90b2-3babdb847df3
    // Custom Data:
    phoneRequiredMessage: ReactJssModule.TextField;
    // The Phone Section Label Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 56886598-9698-4bfa-9fff-bd338555a106
    // Custom Data:
    phoneSectionLabelText: ReactJssModule.TextField;
    // The PhoneValidMessage field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: bada59f7-21f0-4e28-8c3f-2c0fd6837ced
    // Custom Data:
    phoneValidMessage: ReactJssModule.TextField;
    // The submit-button-text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 6c301623-81a6-4f48-9f3b-f1f4d5ec4ae6
    // Custom Data:
    submitButtonText: ReactJssModule.TextField;
  }
  // The Payment Email Main Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Payment Email Main Data
  // ID: 3a2a7965-7384-4412-a91f-2032b2200a87
  export interface PaymentEmailMainDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Component Header field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 76435e9e-6389-4d60-9417-4fe696368b8d
    // Custom Data:
    componentHeader: ReactJssModule.TextField;
  }
  // The Payment Method Adyen Dropin Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Payment Method Adyen Dropin Data
  // ID: a4cf769a-72a5-4024-b028-b1f3e0fe28fc
  export interface PaymentMethodAdyenDropinDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The AllowedPaymentMethodsMapping field.
    // Short description:
    // Field Type: Name Value List
    // Field ID: 4139a6eb-368f-4fc9-8569-bab309b2654f
    // Custom Data:
    allowedPaymentMethodsMapping: ReactJssModule.TextField;
    // The MembershipAndSubscriptionAutoRenewMessage field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: beab2724-4aa1-41b4-a27e-584aec2704b2
    // Custom Data:
    membershipAndSubscriptionAutoRenewMessage: ReactJssModule.TextField;
    // The MembershipAutoRenewMessage field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: 4f99e786-5ca0-41cb-862e-a8e9b75ea8a8
    // Custom Data:
    membershipAutoRenewMessage: ReactJssModule.TextField;
    // The Page Loader Body field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 639e7940-918e-4593-9c87-7772698b2c9b
    // Custom Data:
    pageLoaderBody: ReactJssModule.TextField;
    // The Page Loader Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: f177716d-c9c5-41c1-9cc1-ed76e2719c59
    // Custom Data:
    pageLoaderTitle: ReactJssModule.TextField;
    // The Settings field.
    // Short description:
    // Field Type: Droplink
    // Field ID: d2cd81a7-bdfd-46eb-a780-1bc03a00219a
    // Custom Data:
    settings: ReactJssModule.Item<ReactJssModule.BaseDataSourceItem>;
    // The SubscriptionAutoRenewMessage field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: 68e47ac8-0a05-4ed3-97c6-c1484f93c691
    // Custom Data:
    subscriptionAutoRenewMessage: ReactJssModule.TextField;
    // The TranslationOverrides field.
    // Short description:
    // Field Type: Name Value List
    // Field ID: e6403750-bf79-4b9f-bfec-56b25569868e
    // Custom Data:
    translationOverrides: ReactJssModule.TextField;
  }
  // The Payment Method Adyen Pay by Secure Link Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Payment Method Adyen Pay by Secure Link Data
  // ID: 9e33af76-0e15-4f50-9917-b3c9eda84954
  export interface PaymentMethodAdyenPayBySecureLinkDataDataSource extends PaymentMethodDataDataSource {
  }
  // The Payment Method Alipay Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Payment Method Alipay Data
  // ID: cae07e06-aaab-4081-8bb4-9890091fa9d1
  export interface PaymentMethodAlipayDataDataSource extends PaymentMethodDataDataSource {
    // The RedirectInformationMessage field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 6aef76ce-873c-48b9-a176-a7ec4356e235
    // Custom Data:
    redirectInformationMessage: ReactJssModule.TextField;
  }
  // The PaymentMethodData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/PaymentMethodData
  // ID: 94f9771e-b024-4dd7-9d21-0c367c971e23
  export interface PaymentMethodDataDataSource extends AutoSaveUserDataDataSource {
    // The Cancel Button Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: f198938f-f938-4cbb-a56a-842a1b7c770b
    // Custom Data:
    cancelButtonText: ReactJssModule.TextField;
    // The Description field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 04210b7b-97c7-474a-b96e-36ca670d4dcf
    // Custom Data:
    description: ReactJssModule.TextField;
    // The Submit Button Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 2824a5b7-4bc2-40b4-a592-9d408e30143b
    // Custom Data:
    submitButtonText: ReactJssModule.TextField;
  }
  // The PaymentMethodPaypalData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/PaymentMethodPaypalData
  // ID: cbab6a60-78e5-47ce-b253-7109b9533a1a
  export interface PaymentMethodPaypalDataDataSource extends PaymentMethodDataDataSource {
    // The RedirectInformationMessage field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 782f47b8-108b-48d7-a75e-2188a18e2d3a
    // Custom Data:
    redirectInformationMessage: ReactJssModule.TextField;
    // The SavePaymentDescription field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 20214907-45b9-4abc-b9a7-c7d5517d0205
    // Custom Data:
    savePaymentDescription: ReactJssModule.TextField;
    // The SavePaymentSubTitle field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 4409e088-ef0c-4690-9767-cb18be2e38c5
    // Custom Data:
    savePaymentSubTitle: ReactJssModule.TextField;
  }
  // The Payment Method Razorpay Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Payment Method Razorpay Data
  // ID: 610e0589-93d8-4c8e-a2c6-07f557058746
  export interface PaymentMethodRazorpayDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The CreditCardLabel field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: c2b1437e-2371-4d2f-8bdd-6319d6aa20a2
    // Custom Data:
    creditCardLabel: ReactJssModule.TextField;
    // The CreditCardSubtext field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: d6c35601-1201-4828-ac43-c8c4ba3dc380
    // Custom Data:
    creditCardSubtext: ReactJssModule.TextField;
    // The MembershipAndSubscriptionAutoRenewMessage field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: 9f3f2185-13c7-4941-8a78-2067cfdd1720
    // Custom Data:
    membershipAndSubscriptionAutoRenewMessage: ReactJssModule.TextField;
    // The NetbankingLabel field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: c37d78ca-21f8-4bea-a74c-57cf8ed7a588
    // Custom Data:
    netbankingLabel: ReactJssModule.TextField;
    // The NetbankingSubtext field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: e7662200-52aa-4554-86da-3e15c6c78ee8
    // Custom Data:
    netbankingSubtext: ReactJssModule.TextField;
    // The UpiLabel field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: f59678a9-3a7c-4c27-b9e4-4d43e81f6d8a
    // Custom Data:
    upiLabel: ReactJssModule.TextField;
    // The UPILimitAmount field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 5560e6c3-6143-4bb7-be63-aa54250605f6
    // Custom Data:
    upiLimitAmount: ReactJssModule.TextField;
    // The UpiSubtext field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 4d3a8376-12de-4f63-9015-2af1536b5778
    // Custom Data:
    upiSubtext: ReactJssModule.TextField;
  }
  // The Payment Method Razorpay Pay by Secure Link Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Payment Method Razorpay Pay by Secure Link Data
  // ID: 2ea5a2a6-131e-43cd-9c7e-b307d421abaf
  export interface PaymentMethodRazorpayPayBySecureLinkDataDataSource extends PaymentMethodDataDataSource {
  }
  // The PaymentMethodWorldpayData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/PaymentMethodWorldpayData
  // ID: 58621709-4f02-4e32-9b0d-69cb289015bb
  export interface PaymentMethodWorldpayDataDataSource extends PaymentMethodDataDataSource {
    // The CardHolderNameEmpty field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: a3b57088-1ddf-404a-9542-7b4b7005d99d
    // Custom Data:
    cardHolderNameEmpty: ReactJssModule.TextField;
    // The CardHolderNameLabel field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: b29af014-647c-4297-8dc7-29c896a95087
    // Custom Data:
    cardHolderNameLabel: ReactJssModule.TextField;
    // The CardHolderNamePlaceholder field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: b191ae6e-51bd-4180-b049-f0688cacb36b
    // Custom Data:
    cardHolderNamePlaceholder: ReactJssModule.TextField;
    // The CardNumberInvalid field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 7decd6c3-0d29-4fe8-878b-ed50e71b74c0
    // Custom Data:
    cardNumberInvalid: ReactJssModule.TextField;
    // The CardNumberLabel field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 5395cc4a-54cd-4f02-acb1-8b87126facdc
    // Custom Data:
    cardNumberLabel: ReactJssModule.TextField;
    // The CardNumberPlaceholder field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 6f82954f-68c3-4763-8f72-c0260d9bce8a
    // Custom Data:
    cardNumberPlaceholder: ReactJssModule.TextField;
    // The ExpirationMonthEmpty field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: eb76077d-050e-49f3-a0d0-73729a485977
    // Custom Data:
    expirationMonthEmpty: ReactJssModule.TextField;
    // The ExpirationMonthLabel field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 1a2b6fe4-b9c9-47c3-b42a-52dfb45f07cb
    // Custom Data:
    expirationMonthLabel: ReactJssModule.TextField;
    // The ExpirationMonthPlaceholder field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 2e8b5b7b-5330-482c-9e8a-fb73b95d370a
    // Custom Data:
    expirationMonthPlaceholder: ReactJssModule.TextField;
    // The ExpirationMonthYearExpired field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 70f0c073-d728-4db7-958e-f0dfc1f5524f
    // Custom Data:
    expirationMonthYearExpired: ReactJssModule.TextField;
    // The ExpirationYearEmpty field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: ed863d43-2c99-4535-ab41-2fa5845789de
    // Custom Data:
    expirationYearEmpty: ReactJssModule.TextField;
    // The ExpirationYearLabel field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 2021ca8d-39e8-426f-843a-427d9e5dcdc1
    // Custom Data:
    expirationYearLabel: ReactJssModule.TextField;
    // The ExpirationYearPlaceholder field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 8176e2d9-e1fb-4eda-b18d-69ee1a4dc14d
    // Custom Data:
    expirationYearPlaceholder: ReactJssModule.TextField;
    // The SaveAsDefaultLabel field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 207ba64d-938c-4854-890b-cf8dd87fbe93
    // Custom Data:
    saveAsDefaultLabel: ReactJssModule.TextField;
    // The SaveAsDefaultSubText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: ebc7aaca-62ec-4006-b652-f7d12cbb9e59
    // Custom Data:
    saveAsDefaultSubText: ReactJssModule.TextField;
    // The SecurityCodeEmpty field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: debb3ef5-bca7-4282-a84e-8be2fba157fc
    // Custom Data:
    securityCodeEmpty: ReactJssModule.TextField;
    // The SecurityCodeLabel field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 969a8c3b-4ae6-4d9b-bf8c-841a2a37305f
    // Custom Data:
    securityCodeLabel: ReactJssModule.TextField;
    // The SecurityCodePlaceholder field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 20f75892-6ea3-4179-b2ac-eca90f57000f
    // Custom Data:
    securityCodePlaceholder: ReactJssModule.TextField;
    // The SecurityCodeTooltipText field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: d288099b-604c-48d0-a89b-c7d37b49e4ed
    // Custom Data:
    securityCodeTooltipText: ReactJssModule.TextField;
  }
  // The PaymentMethodWorldpayHostedPayment template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/PaymentMethodWorldpayHostedPayment
  // ID: bac98afe-a2a8-4cee-9e9d-f1bdd1c8b972
  export interface PaymentMethodWorldpayHostedPaymentDataSource extends PaymentMethodDataDataSource {
  }
  // The PayPal Pay Later Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/PayPal Pay Later Data
  // ID: 62a2a28a-1a63-4cef-b356-35d827d46ad6
  export interface PayPalPayLaterDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The AdyenSetting field.
    // Short description:
    // Field Type: Droplink
    // Field ID: f288ba3a-55a8-47d9-a658-3e55f013994c
    // Custom Data:
    adyenSetting: ReactJssModule.Item<ReactJssModule.BaseDataSourceItem>;
  }
  // The PayPalPlaceOrderButton template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/PayPalPlaceOrderButton
  // ID: d5688d77-4ab0-4ce2-a027-114dc5fcd57a
  export interface PayPalPlaceOrderButtonDataSource extends SubmitOrderButtonDataDataSource {
    // The AuthorizationCancelledErrorMessage field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 3aafc5f0-bc68-45fb-834d-2ddfa9384321
    // Custom Data:
    authorizationCancelledErrorMessage: ReactJssModule.TextField;
    // The AuthorizationFailedErrorMessage field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 0b84211d-6b81-43a7-9454-1c7f85bce807
    // Custom Data:
    authorizationFailedErrorMessage: ReactJssModule.TextField;
    // The AuthorizationFallbackErrorMessage field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 02aca0a8-aa6d-437e-90f2-5e057b7159f4
    // Custom Data:
    authorizationFallbackErrorMessage: ReactJssModule.TextField;
    // The OrderProcessingStateBody field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 5afc11d7-87cc-4fd0-a443-b1bfcfdf3b89
    // Custom Data:
    orderProcessingStateBody: ReactJssModule.TextField;
    // The OrderProcessingStateTitle field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 004179d4-d4b2-4371-a204-88e9ff75a2b8
    // Custom Data:
    orderProcessingStateTitle: ReactJssModule.TextField;
    // The RedirectWaitingStateBody field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: edf8b99a-1d26-4577-abaa-33207e7f0e8f
    // Custom Data:
    redirectWaitingStateBody: ReactJssModule.TextField;
    // The RedirectWaitingStateTitle field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 552f73b2-f725-4b56-af3a-592d1103054d
    // Custom Data:
    redirectWaitingStateTitle: ReactJssModule.TextField;
  }
  // The PresumptiveChaptersData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/PresumptiveChaptersData
  // ID: 71925bb2-5e7c-4386-bf21-f97a1df48044
  export interface PresumptiveChaptersDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The AccordionButtonText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: ab35db1b-53a8-4179-90bd-99f90df7370e
    // Custom Data:
    accordionButtonText: ReactJssModule.TextField;
    // The AccordionInnerHeader field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 59b96202-371b-4561-93c3-fbca65192b30
    // Custom Data:
    accordionInnerHeader: ReactJssModule.TextField;
    // The AccordionSubtitle field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: 256c21d4-2ec5-4bed-830b-585367bd66f9
    // Custom Data:
    accordionSubtitle: ReactJssModule.TextField;
    // The CloseButtonText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: dc606e39-6c0c-42a3-ab2e-f6b5a4ab0673
    // Custom Data:
    closeButtonText: ReactJssModule.TextField;
    // The ExploreMoreChaptersButtonText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 35f05231-167e-4831-b559-b6f8c8928488
    // Custom Data:
    exploreMoreChaptersButtonText: ReactJssModule.TextField;
    // The ExploreMoreChaptersUrl field.
    // Short description:
    // Field Type: General Link
    // Field ID: 99eaec6f-4bad-46fe-8fc8-6f6529e33753
    // Custom Data:
    exploreMoreChaptersUrl: ReactJssModule.LinkField;
    // The Header Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 559d9860-b159-41c7-908e-365f279897ee
    // Custom Data:
    headerTitle: ReactJssModule.TextField;
    // The Text field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: 2d26cce3-6151-4d86-92ac-d2331459b2da
    // Custom Data:
    text: ReactJssModule.TextField;
  }
  // The Primary Phone Number Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Primary Phone Number Data
  // ID: 94f87600-167d-41cd-89d8-46a4227c6681
  export interface PrimaryPhoneNumberDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Area Code field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 0f345a51-f3c7-4108-912f-2fa01fd01f48
    // Custom Data:
    areaCode: ReactJssModule.TextField;
    // The Country Code field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: b62bf734-869b-4d5a-8660-067473eb4b06
    // Custom Data:
    countryCode: ReactJssModule.TextField;
    // The Ext field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 539070fb-6919-4235-b901-6d42397faf2d
    // Custom Data:
    ext: ReactJssModule.TextField;
    // The Number field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: a712c2c6-20c6-4b3f-b26b-e986f4446bea
    // Custom Data:
    number: ReactJssModule.TextField;
    // The Phone Text field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: 529c6153-168e-47dc-9307-4c71d94c9228
    // Custom Data:
    phoneText: ReactJssModule.TextField;
    // The Phone Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 13de693c-3ebe-42db-b505-2e7ce7acf438
    // Custom Data:
    phoneTitle: ReactJssModule.TextField;
    // The Phone Tooltip Link field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 0162e61f-8b24-46b9-9cce-acd17faeebf3
    // Custom Data:
    phoneTooltipLink: ReactJssModule.TextField;
    // The Phone Tooltip Text field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: 89ca608c-dd03-4748-97a3-7f3fe75eecee
    // Custom Data:
    phoneTooltipText: ReactJssModule.TextField;
    // The Phone Tooltip Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 9f1ae551-c855-4fd3-ada4-cc508a162b65
    // Custom Data:
    phoneTooltipTitle: ReactJssModule.TextField;
  }
  // The Product Items Rendering Parameters template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Rendering Parameters/Product Items Rendering Parameters
  // ID: 97267763-dc8b-411a-9419-f068c4b24243
  export interface ProductItemsRenderingParametersDataSource extends ReactJssModule.BaseDataSourceItem {
    // The CartHeaderText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 20f58c76-264a-4f39-af47-7db39d29ae83
    // Custom Data:
    cartHeaderText: ReactJssModule.TextField;
    // The HideMemberPrice field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 896ea8e2-4851-4801-be58-ec371df0b459
    // Custom Data:
    hideMemberPrice: ReactJssModule.Field<boolean>;
    // The HidePriceInfoWhenMember field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 889aeafb-2f52-4f58-9ac2-1ba519fd2223
    // Custom Data:
    hidePriceInfoWhenMember: ReactJssModule.Field<boolean>;
    // The HideProductItemsQuantity field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 25045e8d-5629-4298-b956-5712000fa363
    // Custom Data:
    hideProductItemsQuantity: ReactJssModule.Field<boolean>;
    // The HideProductTypeIcon field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 7b0f93e9-680a-4b08-8f4e-d6887fe69242
    // Custom Data:
    hideProductTypeIcon: ReactJssModule.Field<boolean>;
    // The HideRegularPrice field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 005c43f8-1abe-4ffe-b708-5164b0906ab5
    // Custom Data:
    hideRegularPrice: ReactJssModule.Field<boolean>;
    // The isABTestEnabled field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: b93a99e2-2dab-4efc-bee6-97bfd685a9d8
    // Custom Data:
    isABTestEnabled: ReactJssModule.Field<boolean>;
    // The Product Items Data Source field.
    // Short description:
    // Field Type: Droplist
    // Field ID: 95999174-2ae6-4f33-bd6f-3b002f4770c8
    // Custom Data:
    productItemsDataSource: ReactJssModule.TextField;
    // The Read Only field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 98b350ac-aee8-442e-a53b-381bfedaa360
    // Custom Data:
    readOnly: ReactJssModule.Field<boolean>;
    // The Search Timeout After Key Press in Milliseconds field.
    // Short description:
    // Field Type: Integer
    // Field ID: af0ab8dd-77c6-407c-9162-a2b148e91881
    // Custom Data:
    searchTimeoutAfterKeyPressInMilliseconds: ReactJssModule.Field<number>;
    // The Show Cart Header field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 4dc6d222-ad7d-4893-a781-43726bf9f5fe
    // Custom Data:
    showCartHeader: ReactJssModule.Field<boolean>;
  }
  // The Promocode Component Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Promocode Component Data
  // ID: 4721390f-fdd6-464f-b36e-14374c72fc55
  export interface PromocodeComponentDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Apply Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 5ab9af36-37f6-4f2f-bd99-ef9670b33770
    // Custom Data:
    applyText: ReactJssModule.TextField;
    // The Promocode Placeholder field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 7f93d878-0c73-4309-b2d3-a1fdbcc56993
    // Custom Data:
    promocodePlaceholder: ReactJssModule.TextField;
  }
  // The Promocode Component template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Promocode Component
  // ID: 50ded6f7-ecc0-425e-887a-81ff66d73a2e
  export interface PromocodeComponentDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Button Label field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 47b3e476-7658-48bb-be41-a0a8188c559b
    // Custom Data:
    buttonLabel: ReactJssModule.TextField;
    // The Placeholder field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 6fd235be-7576-4329-91ee-c3b5bb0d2bfc
    // Custom Data:
    placeholder: ReactJssModule.TextField;
    // The Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 1ed708fd-5f16-4eea-ae8f-938db338df33
    // Custom Data:
    title: ReactJssModule.TextField;
    // The Tooltip Message field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 69855f0a-70a2-476e-8c6f-74735f011928
    // Custom Data:
    tooltipMessage: ReactJssModule.TextField;
  }
  // The Promo Modal Membership Item Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Promo Modal Membership Item Data
  // ID: 9edf6451-8e07-46f0-9be5-7d1e96e6ad82
  export interface PromoModalMembershipItemDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Membership Text field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: bc326365-14bd-4023-afcc-180fe8a7b83c
    // Custom Data:
    membershipText: ReactJssModule.TextField;
    // The ProductSku field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 329a816e-e846-4e58-8ff2-d57371df9d6c
    // Custom Data:
    productSku: ReactJssModule.TextField;
  }
  // The Quote Folder template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Quote Folder
  // ID: 438aa786-2e07-430b-8dcf-77fb56f39f51
  export interface QuoteFolderDataSource extends ReactJssModule.BaseDataSourceItem {
  }
  // The Razorpay Submit Button Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Razorpay Submit Button Data
  // ID: 7650089d-abad-4e43-abae-a10fa4d93a40
  export interface RazorpaySubmitButtonDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The ButtonText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: bba61f04-e6d6-417c-bc7c-7920e2b6b01b
    // Custom Data:
    buttonText: ReactJssModule.TextField;
    // The OverlayBody field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: a201f942-68d3-4270-97d6-deb4fb2a7b74
    // Custom Data:
    overlayBody: ReactJssModule.TextField;
    // The OverlayTitle field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 7dbdb759-a0de-48fb-8297-b91b7b2c05d5
    // Custom Data:
    overlayTitle: ReactJssModule.TextField;
    // The Settings field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 8a083ef4-6a8c-4bbb-bc2f-481c37245f81
    // Custom Data:
    settings: ReactJssModule.Item<ReactJssModule.BaseDataSourceItem>;
  }
  // The RequestQuoteLinkData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/RequestQuoteLinkData
  // ID: d59f8d58-8434-4e4f-8c52-b0a1421908d3
  export interface RequestQuoteLinkDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The GoToCheckoutLink field.
    // Short description:
    // Field Type: General Link
    // Field ID: 04b4ad17-0193-4622-8a1f-a2497ea3d7d4
    // Custom Data:
    goToCheckoutLink: ReactJssModule.LinkField;
    // The LocalCurrency field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 57c14d32-dfc1-4b32-9541-aec67167baf1
    // Custom Data:
    localCurrency: ReactJssModule.Item<ReactJssModule.BaseDataSourceItem>;
    // The TooltipMessage field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 472181c2-2a1e-4f8c-8c95-9156957fb4e4
    // Custom Data:
    tooltipMessage: ReactJssModule.TextField;
  }
  // The Return To PmiOrg Button template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Return To PmiOrg Button
  // ID: 4b9b60f0-4b8b-4da1-81e0-d109cba82416
  export interface ReturnToPmiOrgButtonDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Button Link field.
    // Short description:
    // Field Type: General Link
    // Field ID: b500daa3-51eb-41f9-bb76-b00a26796435
    // Custom Data:
    buttonLink: ReactJssModule.LinkField;
    // The Button Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 02716444-8e97-4334-8f1a-da3c337d207f
    // Custom Data:
    buttonText: ReactJssModule.TextField;
  }
  // The Review Total template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Review Total
  // ID: b6480775-4af2-4203-9cf1-596b074bbeea
  export interface ReviewTotalDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Purchase field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 8298b455-5680-44f7-ac9c-92c7a33b2901
    // Custom Data:
    purchase: ReactJssModule.TextField;
    // The Total field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: d9447702-8e87-4a3d-a202-eb502e609d03
    // Custom Data:
    total: ReactJssModule.TextField;
    // The TotalSaving field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 17ee808c-4c6a-4a66-9a30-41e4aea2536f
    // Custom Data:
    totalSaving: ReactJssModule.TextField;
  }
  // The SavedPaymentCardsData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/SavedPaymentCardsData
  // ID: fd5a70f7-9b1e-4eb7-906d-36e3c5ef1aec
  export interface SavedPaymentCardsDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The AddNewButtonText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: d209b6a4-dc32-45ed-88d1-4b08313a9601
    // Custom Data:
    addNewButtonText: ReactJssModule.TextField;
    // The EditButtonText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: ecd5395d-fffb-4027-b816-2cb80f04b67d
    // Custom Data:
    editButtonText: ReactJssModule.TextField;
    // The HeadingText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: dd4c711c-56ab-441f-8f57-a9f0b08d4f48
    // Custom Data:
    headingText: ReactJssModule.TextField;
    // The SubmitButtonText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 6ea3ce6f-1415-4e78-9c34-906fd43b114a
    // Custom Data:
    submitButtonText: ReactJssModule.TextField;
  }
  // The ShoppingCartData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/ShoppingCartData
  // ID: c9cf08a0-3889-4e3d-872b-825d4c5865ee
  export interface ShoppingCartDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The EmptyCartBody field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 5e63b29d-bbfa-4aad-942f-aef0f922beed
    // Custom Data:
    emptyCartBody: ReactJssModule.TextField;
    // The EmptyCartHeading field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 5b1549b5-2e97-47c8-9b4c-5fd154f48177
    // Custom Data:
    emptyCartHeading: ReactJssModule.TextField;
    // The EmptyCartNavigateToLink field.
    // Short description:
    // Field Type: General Link
    // Field ID: 117b5cfd-1f53-40aa-a795-5c8e684fa0e6
    // Custom Data:
    emptyCartNavigateToLink: ReactJssModule.LinkField;
  }
  // The Student Membership Confirmation Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Student Membership Confirmation Data
  // ID: ad640985-3d73-4f88-b101-114809ebb82c
  export interface StudentMembershipConfirmationDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Item Box Text field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 98279841-8fa6-4b74-b93a-193246812b40
    // Custom Data:
    itemBoxText: ReactJssModule.TextField;
    // The Item Box Title Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 0d789bcc-0b21-4373-8339-578d84d8031b
    // Custom Data:
    itemBoxTitleText: ReactJssModule.TextField;
    // The Note Text field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: f61fac9f-fad3-47b0-8187-bb91e171f457
    // Custom Data:
    noteText: ReactJssModule.TextField;
    // The Note Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 8170b282-0c05-4c87-8993-ce82d4487d12
    // Custom Data:
    noteTitle: ReactJssModule.TextField;
    // The Submit Button Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: c53ed6a5-df0e-4cd2-8153-d578313e7fcf
    // Custom Data:
    submitButtonText: ReactJssModule.TextField;
    // The Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 2e4a9761-d4d2-464f-a245-a17db594a547
    // Custom Data:
    title: ReactJssModule.TextField;
  }
  // The Submit Order Button Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Submit Order Button Data
  // ID: f2958f7d-bd23-4016-a727-35214bee6872
  export interface SubmitOrderButtonDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Adyen Pay by Secure Link Button Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: e56d408f-340f-4e91-af78-db68bc5b1818
    // Custom Data:
    adyenPayBySecureLinkButtonText: ReactJssModule.TextField;
    // The Alipay Button Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 78d0c51e-f011-44ae-929a-1327e8f26bfa
    // Custom Data:
    alipayButtonText: ReactJssModule.TextField;
    // The Alipay Order Processing State Body field.
    // Short description: [Deprecated]
    // Field Type: Single-Line Text
    // Field ID: ff1dac78-3599-4587-b410-2f708f608c59
    // Custom Data:
    alipayOrderProcessingStateBody: ReactJssModule.TextField;
    // The Alipay Order Processing State Title field.
    // Short description: [Deprecated]
    // Field Type: Single-Line Text
    // Field ID: 690fe3ae-5351-4226-a546-bfb8c0c44b0a
    // Custom Data:
    alipayOrderProcessingStateTitle: ReactJssModule.TextField;
    // The Alipay Redirect Waiting State Body field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 21eb6a42-e889-4d12-9241-234f41fa8cf0
    // Custom Data:
    alipayRedirectWaitingStateBody: ReactJssModule.TextField;
    // The Alipay Redirect Waiting State Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 84ae3cd7-a87f-4809-b100-d41855ab85c0
    // Custom Data:
    alipayRedirectWaitingStateTitle: ReactJssModule.TextField;
    // The ButtonText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 0acdeea8-2118-4977-8830-8da76eb6ac97
    // Custom Data:
    buttonText: ReactJssModule.TextField;
    // The Confirmation Page Url field.
    // Short description:
    // Field Type: General Link
    // Field ID: 08921a6a-9812-42c6-acba-a1bb40d327ce
    // Custom Data:
    confirmationPageUrl: ReactJssModule.LinkField;
    // The OrderProcessingBody field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 1cb59b7a-8bcc-4e5c-b8fa-a56fb2664cde
    // Custom Data:
    orderProcessingBody: ReactJssModule.TextField;
    // The OrderProcessingTitle field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 212449d4-e2c0-433e-b165-5103b69be9bc
    // Custom Data:
    orderProcessingTitle: ReactJssModule.TextField;
    // The Razorpay Pay by Secure Link Button Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: ef0e5b60-0fcd-4e7d-93d9-129295750ab1
    // Custom Data:
    razorpayPayBySecureLinkButtonText: ReactJssModule.TextField;
    // The WorldpayHostedPaymentPlaceOrderText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 0c751bfb-eeb4-494a-997f-55c4d8486cf1
    // Custom Data:
    worldpayHostedPaymentPlaceOrderText: ReactJssModule.TextField;
  }
  // The Thank You Message template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Thank You Message
  // ID: c1e332ff-95ab-4613-96d9-89b13b612b41
  export interface ThankYouMessageDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Order Processing Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 67195295-6fd7-495f-a52d-a84e5c1048b4
    // Custom Data:
    orderProcessingText: ReactJssModule.TextField;
    // The Thank Inquiry Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: b86e3b7a-68e6-4718-916f-727bab832922
    // Custom Data:
    thankInquiryText: ReactJssModule.TextField;
    // The Thank Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 4bd62f9f-2c5a-4975-91a9-f9738e54f98c
    // Custom Data:
    thankText: ReactJssModule.TextField;
  }
  // The ViewInvoiceButtonData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/ViewInvoiceButtonData
  // ID: 5f740f0d-7d69-475d-bf40-52d105dd4728
  export interface ViewInvoiceButtonDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The CheckOrderStatusLink field.
    // Short description:
    // Field Type: General Link
    // Field ID: e5b2c3f0-029f-4f6f-88b3-836862eec64f
    // Custom Data:
    checkOrderStatusLink: ReactJssModule.LinkField;
    // The ViewInvoiceButtonLink field.
    // Short description:
    // Field Type: General Link
    // Field ID: b23d817a-ceca-4314-a285-a9d9ecd5580b
    // Custom Data:
    viewInvoiceButtonLink: ReactJssModule.LinkField;
  }
  // The ViewInvoicePendingButtonData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/ViewInvoicePendingButtonData
  // ID: ba398f9b-4446-419b-b3e4-15c7ab63ac31
  export interface ViewInvoicePendingButtonDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The CheckOrderStatusLink field.
    // Short description:
    // Field Type: General Link
    // Field ID: f8683af4-6379-48fe-b134-83ab4ff44687
    // Custom Data:
    checkOrderStatusLink: ReactJssModule.LinkField;
    // The ViewInvoiceButtonLink field.
    // Short description:
    // Field Type: General Link
    // Field ID: b2d820be-ff8a-4612-984d-eb93439ad995
    // Custom Data:
    viewInvoiceButtonLink: ReactJssModule.LinkField;
    // The ViewOrderStatusText field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: fd292bfd-59c6-4e1e-9cdc-3742b350b6eb
    // Custom Data:
    viewOrderStatusText: ReactJssModule.TextField;
  }
  // The Vouchercode Component template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Vouchercode Component
  // ID: 122d2f80-0720-49b7-84e0-1e8324019091
  export interface VouchercodeComponentDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Button Label field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: de4f5815-5574-4563-bc96-31fbea9894c0
    // Custom Data:
    buttonLabel: ReactJssModule.TextField;
    // The Placeholder field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: caccba1a-b833-4458-8c5f-b5684cd47274
    // Custom Data:
    placeholder: ReactJssModule.TextField;
    // The Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: f6f7f6c2-61b9-4ac9-b92e-e12d061e90a4
    // Custom Data:
    title: ReactJssModule.TextField;
    // The Tooltip Message field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: dda9e5d5-7f87-41ae-afc6-c3bef3aea52b
    // Custom Data:
    tooltipMessage: ReactJssModule.TextField;
  }
  // The Voucher Component Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Apply Voucher/Voucher Component Data
  // ID: 6695a9ee-e354-463b-baec-b92e810cebe7
  export interface VoucherComponentDataDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Agreement Text field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: eb419c1e-983d-48c5-933e-fabb79ee9c62
    // Custom Data:
    agreementText: ReactJssModule.TextField;
    // The Applied Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: bf0fd84b-2822-40c4-891c-100000abc65b
    // Custom Data:
    appliedText: ReactJssModule.TextField;
    // The Apply Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: be5fd7b5-f8ab-4278-9950-b9a0b373bfcb
    // Custom Data:
    applyText: ReactJssModule.TextField;
    // The Learn Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 5a47ed0c-a423-4600-b433-955d21cdd9cc
    // Custom Data:
    learnText: ReactJssModule.TextField;
    // The Placeholder Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: db4c1b84-aac3-43af-86da-35009f5c05d4
    // Custom Data:
    placeholderText: ReactJssModule.TextField;
    // The Remove Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 5e6f8770-e652-42dd-a1c0-d70d8a55ac2d
    // Custom Data:
    removeText: ReactJssModule.TextField;
    // The Tooltip Description field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: 0c6ec74e-18ac-4521-97ad-5a538bffe306
    // Custom Data:
    tooltipDescription: ReactJssModule.TextField;
    // The Tooltip Header field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 0ba798ab-ef89-431f-a5ff-520f545a1294
    // Custom Data:
    tooltipHeader: ReactJssModule.TextField;
    // The Tooltip Link field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 7e966440-890c-47eb-8983-7a5b1513d5b1
    // Custom Data:
    tooltipLink: ReactJssModule.TextField;
    // The Tooltip Linkname field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 70ecb8e1-6fc0-42ce-bfed-1fd8aff1338b
    // Custom Data:
    tooltipLinkname: ReactJssModule.TextField;
  }

  // The AddressChangeOptions template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/AddressChangeOptions
  // ID: f3d84f3f-ac51-430c-8b56-39623fda6801
  export interface AddressChangeOptionsRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The AddressUpdateLink field.
    // Short description:
    // Field Type: General Link
    // Field ID: e4dc03ec-101c-4141-8a1d-cc0fa7d64401
    // Custom Data:
    addressUpdateLink: string;
    // The UpdateAddressSuggestionText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 80ba908d-a978-40c2-8c86-738c30a2f09a
    // Custom Data:
    updateAddressSuggestionText: string;
  }
  // The Apply Voucher Folder template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Apply Voucher/Apply Voucher Folder
  // ID: 25460bc1-0b9a-4b18-bc31-7f88b8deb89f
  export interface ApplyVoucherFolderRenderingParams extends ReactJssModule.BaseRenderingParam {
  }
  // The AutoSaveUserData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/AutoSaveUserData
  // ID: 1f9ff9a3-ea4a-40f0-8a08-e50eb9b0c7e5
  export interface AutoSaveUserDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The AutoSaveText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: d4c5b41d-5842-4d3c-a6f1-26b6d94caa99
    // Custom Data:
    autoSaveText: string;
  }
  // The Brazil CPF Code Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Brazil CPF Code Data
  // ID: 60ebfc58-4381-4302-a9b9-6379eb5575bf
  export interface BrazilCPFCodeDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Add Cpf Button Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: df269c6d-7c82-4614-9e7f-bd34372c3d3e
    // Custom Data:
    addCpfButtonText: string;
    // The Allow CNPJ field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: a38ce8d9-43fd-4708-92db-704e48f4a1f2
    // Custom Data:
    allowCNPJ: boolean;
    // The Cpf Field Label field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 43eb3754-bd05-4aa7-9237-81026762a304
    // Custom Data:
    cpfFieldLabel: string;
    // The Cpf Field Placeholder field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: aaa82e5b-64b0-434a-9aad-161af0216d1d
    // Custom Data:
    cpfFieldPlaceholder: string;
    // The Cpf Invalid Message field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 563cc6ec-81b1-42f2-9cba-6347baf9c171
    // Custom Data:
    cpfInvalidMessage: string;
    // The Cpf Required Message field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 21cf7574-1d3a-4ff7-8137-7ca9ac7c291c
    // Custom Data:
    cpfRequiredMessage: string;
    // The Expand Link Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 525d0e85-9a4c-48c5-82f3-dc335a98308a
    // Custom Data:
    expandLinkText: string;
    // The Heading Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 8eb26979-aaa2-4d15-9e2f-8f5c1268ce31
    // Custom Data:
    headingText: string;
  }
  // The Cart Folder template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Cart Folder
  // ID: b5a8fe0a-c066-4b42-9a20-6584bc2441f7
  export interface CartFolderRenderingParams extends ReactJssModule.BaseRenderingParam {
  }
  // The Cart Membership Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Cart Membership Data
  // ID: 2be48eb8-644d-4bc2-b3b1-5ba830f97a02
  export interface CartMembershipDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Email field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: b808958a-4292-41a6-be94-324e60efe676
    // Custom Data:
    email: string;
    // The MembershipSection field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 0a6272b6-3163-49ee-b431-6619744709cc
    // Custom Data:
    membershipSection: string;
    // The RegisteredSection field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 57377190-db60-492a-b7a8-4fdc6ac86c32
    // Custom Data:
    registeredSection: string;
    // The SheerIDParameters field.
    // Short description:
    // Field Type: Droplink
    // Field ID: feb268d7-1be2-496c-b447-32e7814b073e
    // Custom Data:
    sheerIDParameters: string;
    // The SheerIdUrl field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 7bea4473-24f0-4c98-8294-a0589cda17bd
    // Custom Data:
    sheerIdUrl: string;
    // The StudentMembershipDisclaimerText field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 13483cc2-71a8-46d4-a6a8-a8a9dfb8de4b
    // Custom Data:
    studentMembershipDisclaimerText: string;
  }
  // The CartMembershipOptions template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/CartMembershipOptions
  // ID: e4cd7788-e383-49ee-a0b4-e1e7058ec7bb
  export interface CartMembershipOptionsRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The ApplicationFeeText field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: cb9f6fce-5035-47c6-99a5-97f378c4ee4a
    // Custom Data:
    applicationFeeText: string;
    // The ListItems field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 269a0fb9-6bfe-419c-afb9-034e0cbdfca7
    // Custom Data:
    listItems: string;
    // The OptionsLink field.
    // Short description:
    // Field Type: General Link
    // Field ID: 5821391d-31c6-49f3-97bf-40f7042c47e5
    // Custom Data:
    optionsLink: string;
    // The StandardTariffText field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 8c0979fa-17b7-4af5-a93d-3afb6aa3f3de
    // Custom Data:
    standardTariffText: string;
    // The SubTitle field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 1a99fc2d-8f9b-45df-8320-d49da519ad95
    // Custom Data:
    subTitle: string;
    // The Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: b121896f-c2e4-45c8-afa9-9db3c900cdc4
    // Custom Data:
    title: string;
  }
  // The CartMembershipRenderingParameters template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Rendering Parameters/CartMembershipRenderingParameters
  // ID: e76ad388-95e5-4536-8c4a-94fe003aa384
  export interface CartMembershipRenderingParametersRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The isABTestEnabled field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 2440ff2a-be58-4d03-9a56-3a10bf8211f4
    // Custom Data:
    isABTestEnabled: boolean;
  }
  // The Cart Totals Rendering Parameters template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Rendering Parameters/Cart Totals Rendering Parameters
  // ID: 8a074b16-c179-4a9e-a684-eafab5ae4474
  export interface CartTotalsRenderingParametersRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Cart Totals Data Source field.
    // Short description:
    // Field Type: Droplist
    // Field ID: 8d7cc943-dc3f-480b-8778-04cbfe046c82
    // Custom Data:
    cartTotalsDataSource: string;
    // The Show Taxes field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 4d2a45a5-2239-4c58-ba48-437f7ca69a5d
    // Custom Data:
    showTaxes: boolean;
  }
  // The Cart Totals Row Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Cart Totals Row Data
  // ID: a2d41f52-8c1f-48d2-8781-ecbea6b70501
  export interface CartTotalsRowDataRenderingParams extends ReactJssModule.BaseRenderingParam {
  }
  // The Cart Totals Row Rendering Parameters template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Rendering Parameters/Cart Totals Row Rendering Parameters
  // ID: 1525fca3-8f8f-41a9-b0a5-efaa00cb5f35
  export interface CartTotalsRowRenderingParametersRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Field field.
    // Short description:
    // Field Type: Droplink
    // Field ID: c4721439-a7bc-4f10-9647-ba78c850f7eb
    // Custom Data:
    field: string;
    // The Is Bold field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 7ac0d283-7d2d-4704-99b1-b8c5e3b149ff
    // Custom Data:
    isBold: boolean;
    // The Is Red field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 064e998c-2880-42e8-884d-45d2fbcfd33e
    // Custom Data:
    isRed: boolean;
    // The UseLocalCurrencyTotal field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 1ddf4958-3262-44b8-836c-1b54cef164e0
    // Custom Data:
    useLocalCurrencyTotal: boolean;
  }
  // The Cart Totals Row template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Cart Totals Row
  // ID: eee924d7-b03e-4f69-af42-a13ff0804826
  export interface CartTotalsRowRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The SubText field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 564d64be-ac0d-49de-9e13-baea011a215e
    // Custom Data:
    subText: string;
    // The Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 078b384a-dab1-4b10-b06d-db14167b292d
    // Custom Data:
    title: string;
  }
  // The CertificationRenewalAgreementOptions template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/CertificationRenewalAgreementOptions
  // ID: e3bef9d3-e703-4f73-b693-32c9114eddab
  export interface CertificationRenewalAgreementOptionsRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The AgreeIsChecked field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 12bf1b68-3d7b-49c2-8f4a-f5f9e5616460
    // Custom Data:
    agreeIsChecked: boolean;
    // The AgreementText field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 8adf48be-fa45-47b5-970b-fb1077af32c8
    // Custom Data:
    agreementText: string;
  }
  // The Change Single Membership Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Change Single Membership Data
  // ID: 10911b27-4abf-4ee9-8e33-a9e2dbb19af4
  export interface ChangeSingleMembershipDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Accordion Button Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 964dcce7-f808-4b0e-a802-9307c6554449
    // Custom Data:
    accordionButtonTitle: string;
    // The Expanded Description field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: dd026107-7891-4a67-94fb-7fe567fef875
    // Custom Data:
    expandedDescription: string;
    // The Expanded Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: e62f6dc0-b1f9-443b-b987-089ae80ebe82
    // Custom Data:
    expandedTitle: string;
    // The Header Description field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: 8b657aef-5407-4973-8a00-4be3494e01af
    // Custom Data:
    headerDescription: string;
    // The Header Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: d0070b11-1fac-498f-8f06-74d1f06ae985
    // Custom Data:
    headerTitle: string;
  }
  // The Checkout Buttons Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Checkout Buttons Data
  // ID: a3388754-626a-4f5a-aeb7-1e0359134b91
  export interface CheckoutButtonsDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Continue Shopping Link field.
    // Short description:
    // Field Type: General Link
    // Field ID: 76feda09-65d0-4ef0-9efd-a72ef14ae5b4
    // Custom Data:
    continueShoppingLink: string;
    // The Go to Checkout Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 7f00e387-fdf4-4c4d-9c11-2aef12f7e131
    // Custom Data:
    goToCheckoutText: string;
    // The Go to Shopping Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 9ff0d674-ceba-4256-8855-fccd75b4348c
    // Custom Data:
    goToShoppingText: string;
  }
  // The CheckoutContainerData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/CheckoutContainerData
  // ID: 2a2002ea-d572-476f-b06c-d051eff55d86
  export interface CheckoutContainerDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The BackButtonText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: b05270e6-1011-4569-95d9-9f9b6c166a59
    // Custom Data:
    backButtonText: string;
    // The GoToCartLink field.
    // Short description:
    // Field Type: General Link
    // Field ID: 35c8c0ab-8113-4b48-8dc6-dd8035708d6e
    // Custom Data:
    goToCartLink: string;
    // The Heading field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: a3b0ae12-c267-4603-8d3f-9c38200d8e0a
    // Custom Data:
    heading: string;
    // The ShowBackButton field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 8c779791-dcb3-489a-b5e7-29c6061d5058
    // Custom Data:
    showBackButton: boolean;
  }
  // The CheckoutDonationData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/CheckoutDonationData
  // ID: 9a1f7ffe-c342-4689-922e-61680a7cc18a
  export interface CheckoutDonationDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The DonationAddToCartText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 438141d1-cf3f-491a-8446-652b7aed0ade
    // Custom Data:
    donationAddToCartText: string;
    // The DonationHeader field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 083b673b-4345-44a7-ac2e-988d1713d9fc
    // Custom Data:
    donationHeader: string;
    // The DonationMadeSubText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 2d1da220-c44a-4ab6-976e-c61dbd655597
    // Custom Data:
    donationMadeSubText: string;
    // The DonationMadeText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 21353792-43f6-4cf8-b11e-707d8e7bb86d
    // Custom Data:
    donationMadeText: string;
    // The DonationOtherAmountPlaceholderText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 9f82eb10-4338-4f9b-aaeb-eb2ca9fcab0b
    // Custom Data:
    donationOtherAmountPlaceholderText: string;
    // The DonationSubHeader field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 919c0bef-5c86-4e5c-b749-ea160d8edc0e
    // Custom Data:
    donationSubHeader: string;
  }
  // The Checkout Folder template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Checkout Folder
  // ID: 8cc4ca69-c783-4d05-8880-5a8dc5688a4f
  export interface CheckoutFolderRenderingParams extends ReactJssModule.BaseRenderingParam {
  }
  // The Checkout Navigation Rendering Parameters template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Rendering Parameters/Checkout Navigation Rendering Parameters
  // ID: d22423bc-190c-4767-9d87-ebebf5f1b18e
  export interface CheckoutNavigationRenderingParametersRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Hide Navigation field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 0af848f4-3790-46f6-b769-f4417c8ca903
    // Custom Data:
    hideNavigation: boolean;
  }
  // The Checkout Navigation Step template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Checkout Navigation Step
  // ID: 57cfa14b-718e-43c4-a1e0-5553ec8000b8
  export interface CheckoutNavigationStepRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Step Id field.
    // Short description:
    // Field Type: Integer
    // Field ID: cb7502d8-cf47-4919-a528-ac7dcc1b636a
    // Custom Data:
    stepId: number;
    // The Step Link field.
    // Short description:
    // Field Type: General Link
    // Field ID: 89ad9809-f42a-444b-b484-e2c2be27b3ff
    // Custom Data:
    stepLink: string;
    // The Step Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 6a4ad5b5-01c6-483b-8d2f-bf4e777acdc9
    // Custom Data:
    stepTitle: string;
  }
  // The Checkout Navigation Steps template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Checkout Navigation Steps
  // ID: f266a718-18e6-4978-b827-5c6c81e43350
  export interface CheckoutNavigationStepsRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Mixed products field.
    // Short description:
    // Field Type: Multilist
    // Field ID: 1e74ff70-991e-42cb-ba2b-6045fc514461
    // Custom Data:
    mixedProducts: string;
    // The Virtual products field.
    // Short description:
    // Field Type: Multilist
    // Field ID: e6cbc372-49b4-404d-a02a-af58af7f8003
    // Custom Data:
    virtualProducts: string;
  }
  // The Checkout Settings template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Settings/Checkout Settings
  // ID: 54c627b0-7790-40b3-978b-93d1de2857e4
  export interface CheckoutSettingsRenderingParams extends ReactJssModule.BaseRenderingParam {
  }
  // The Checkout Step Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Checkout Step Data
  // ID: 76e62021-2e33-48bd-86ec-60d3f8bfad56
  export interface CheckoutStepDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Button Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 24aaf99c-1449-4344-b6f2-a37018414501
    // Custom Data:
    buttonText: string;
    // The Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: a294e893-34eb-452d-9a7c-cf8f7d229f49
    // Custom Data:
    title: string;
  }
  // The ConfirmationAccessItemsData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/ConfirmationAccessItemsData
  // ID: e66b582c-235a-4240-b268-2f28310887c9
  export interface ConfirmationAccessItemsDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The CoursesButtonLink field.
    // Short description:
    // Field Type: General Link
    // Field ID: cded1ad3-4027-471b-9f4f-b508303c5752
    // Custom Data:
    coursesButtonLink: string;
    // The CoursesText field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: e0926c3c-1cdb-407e-b92d-8d4ce0a5fcc7
    // Custom Data:
    coursesText: string;
    // The DashboardButtonLink field.
    // Short description:
    // Field Type: General Link
    // Field ID: 1147b3f6-9b5f-4708-a908-42e14fbf3c24
    // Custom Data:
    dashboardButtonLink: string;
    // The DashboardText field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: 74064b98-5359-4a83-8c10-8d8e6cb51aba
    // Custom Data:
    dashboardText: string;
    // The EligibleProductTypesToViewCourses field.
    // Short description:
    // Field Type: Multilist
    // Field ID: d4550c38-7c5d-4e3b-966d-fb44a0fbd5f3
    // Custom Data:
    eligibleProductTypesToViewCourses: string;
    // The Header field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: c8328c4c-26fd-4563-95f4-1d596e8d1145
    // Custom Data:
    header: string;
  }
  // The ConfirmationContainerData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/ConfirmationContainerData
  // ID: 933b6f7f-bfa4-456f-ae36-d87e5bf34eb0
  export interface ConfirmationContainerDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The PendingOrderTitle field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 4ab87d62-6354-48d2-ba2f-3747e0039801
    // Custom Data:
    pendingOrderTitle: string;
    // The QuoteTitle field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: cf74a28d-c5e1-4e3d-8063-2f005139b131
    // Custom Data:
    quoteTitle: string;
    // The Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: f35841e1-5e81-4992-97ee-fdbe5a06b8ea
    // Custom Data:
    title: string;
  }
  // The ConfirmationDetailsData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/ConfirmationDetailsData
  // ID: ec050c0a-da75-4903-988d-4247e51095ae
  export interface ConfirmationDetailsDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The EmailSentText field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: b4ca8985-9b4f-4c76-a778-0ac4167ef6c5
    // Custom Data:
    emailSentText: string;
    // The GeneralBannerInfoForPendingOpenOrders field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 7da93986-697c-43e0-8c8d-d828ba426471
    // Custom Data:
    generalBannerInfoForPendingOpenOrders: string;
    // The NotRecievedEmailText field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: 5b26716f-4419-45a4-a5a2-d8340fb78756
    // Custom Data:
    notRecievedEmailText: string;
    // The QuoteSentText field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: c61f577c-51dd-4683-8a94-b2a83d73fbc4
    // Custom Data:
    quoteSentText: string;
  }
  // The ConfirmationFolder template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/ConfirmationFolder
  // ID: 9030f3a6-2642-48af-ac57-320b2188015d
  export interface ConfirmationFolderRenderingParams extends ReactJssModule.BaseRenderingParam {
  }
  // The Confirmation Page Footer Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Confirmation Page Footer Data
  // ID: a4829610-0df2-472e-96e0-e9ae43732afa
  export interface ConfirmationPageFooterDataRenderingParams extends ReactJssModule.BaseRenderingParam {
  }
  // The Confirmation Payment Method Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Confirmation Payment Method Data
  // ID: 070bca51-8f8e-41ba-a025-62341d919606
  export interface ConfirmationPaymentMethodDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The imageLookUp field.
    // Short description:
    // Field Type: Name Value List
    // Field ID: 8238bfc2-a114-4746-be7c-65bdf1eafa3c
    // Custom Data:
    imageLookUp: string;
  }
  // The Congatulations Gift Membership template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Congatulations Gift Membership
  // ID: c5135fca-cfd0-409e-9031-4f62ee03625e
  export interface CongatulationsGiftMembershipRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Heading field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: efc50d87-cac4-42fb-9f56-6550d3970bfc
    // Custom Data:
    heading: string;
    // The Text field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 159ac32f-fb4d-4817-9d0b-d5d1fff65ea3
    // Custom Data:
    text: string;
  }
  // The Congratulations Certification template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Congratulations Certification
  // ID: 186c9b38-0c8b-4b58-bb8c-9524cb51f7c4
  export interface CongratulationsCertificationRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Congratulations Text field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 45630122-5cfc-4309-a305-c2a8f31ffa37
    // Custom Data:
    congratulationsText: string;
  }
  // The Congratulations Membership template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Congratulations Membership
  // ID: 10eae8c9-0f23-4f28-8387-5966dbe8be6c
  export interface CongratulationsMembershipRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Description field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 496b5054-8d0e-4d66-9370-cb298cf38151
    // Custom Data:
    description: string;
    // The MembershipDescriptionWithoutRenewalStatement field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 4008fe49-a9a4-4cec-9e8d-bc11a49f1e12
    // Custom Data:
    membershipDescriptionWithoutRenewalStatement: string;
    // The Student Description field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 3b573eee-6b6c-4587-9b56-c5b361e62089
    // Custom Data:
    studentDescription: string;
    // The Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: feb57d55-f208-4d57-81e7-9dbd11503407
    // Custom Data:
    title: string;
  }
  // The ContactUsNow template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/ContactUsNow
  // ID: c422339a-1f48-4dcd-99b8-2a2ee6be96a1
  export interface ContactUsNowRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The ContactUsLink field.
    // Short description:
    // Field Type: General Link
    // Field ID: 59fb7856-6381-4b36-a5f1-0addacb97aa4
    // Custom Data:
    contactUsLink: string;
    // The Header field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: b8c41268-3e4e-4e7c-b938-74d3003aeeb8
    // Custom Data:
    header: string;
    // The SubText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 8d72f01b-9396-4a6d-a5eb-6d6a0ee61b1f
    // Custom Data:
    subText: string;
  }
  // The Continue Shopping Button template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Continue Shopping Button
  // ID: 46b4f640-2b0a-48ba-997d-77a19ea5b048
  export interface ContinueShoppingButtonRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The continue shopping button text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 8e461f6d-d8ae-4668-a1d6-ed70802cc72c
    // Custom Data:
    continueShoppingButtonText: string;
    // The continue shopping link field.
    // Short description:
    // Field Type: General Link
    // Field ID: ff6a4db0-017c-4eb8-bcf5-efa97b7a84c7
    // Custom Data:
    continueShoppingLink: string;
  }
  // The convertquote template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/convertquote
  // ID: dd5bc92a-0233-4f4a-a9d2-6f473990ede5
  export interface ConvertquoteRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The componentImage field.
    // Short description:
    // Field Type: Image
    // Field ID: fda37110-7889-460e-9838-03d4f0a7b7f7
    // Custom Data:
    componentImage: string;
    // The problemDescriptionText field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 6d3bb524-f4fd-4308-bcf7-4841987300c1
    // Custom Data:
    problemDescriptionText: string;
  }
  // The Course Library Link template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Course Library Link
  // ID: d47f4f92-96e8-458b-8e3e-7736ffaf968d
  export interface CourseLibraryLinkRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Eligible Product Types to Show Link field.
    // Short description:
    // Field Type: Multilist
    // Field ID: 1a03e9f7-581c-41ba-8533-4b0c6829e4d4
    // Custom Data: name=EligibleProductTypesToShowLink
    eligibleProductTypesToShowLink: string;
    // The Link Text field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 44d60258-3b50-4d27-9a45-9cc03f3cff17
    // Custom Data:
    linkText: string;
  }
  // The Create Quote Button Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Create Quote Button Data
  // ID: c05b1cb0-c6b1-474a-9ea3-a8d2aa2d6a84
  export interface CreateQuoteButtonDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The ButtonText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 7d39ca00-1f81-49b5-9f09-471d0d930528
    // Custom Data:
    buttonText: string;
    // The Confirmation Page Url field.
    // Short description:
    // Field Type: General Link
    // Field ID: 2865a7e7-4fbb-41b7-9348-ffd855173cf2
    // Custom Data:
    confirmationPageUrl: string;
  }
  // The Currency Confirmation template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Currency Confirmation
  // ID: 8538a146-6ff1-4686-989d-5da0f4f1f746
  export interface CurrencyConfirmationRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Checkout Button Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 2d2128af-5975-4556-92d2-1101a88635e4
    // Custom Data:
    checkoutButtonText: string;
    // The Pilot Text field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 47d46a0e-860e-4131-b2cb-5457d0826b02
    // Custom Data:
    pilotText: string;
    // The Preference Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 34882baa-fbf4-4212-80fc-0e1dc3c533a0
    // Custom Data:
    preferenceText: string;
    // The Preference Texts field.
    // Short description:
    // Field Type: Name Value List
    // Field ID: ef40d425-b6ae-4d16-8bf9-098eb93dc28a
    // Custom Data:
    preferenceTexts: string;
    // The Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: e52f20be-6fc4-4001-b3b5-eb2efa2066e7
    // Custom Data:
    title: string;
  }
  // The Discount Component Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Discount Component Data
  // ID: 355ac908-29ed-497a-91a0-ea30c3b38111
  export interface DiscountComponentDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Promo Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 9da7dae4-0948-40a9-bd11-1615353fb1dc
    // Custom Data:
    promoTitle: string;
    // The Voucher Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 2ef6b614-3b5b-45e5-b323-5f690058623e
    // Custom Data:
    voucherTitle: string;
  }
  // The Donation Component Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Donation Component Data
  // ID: 329c4051-88fc-4979-a8cd-7ca63a507172
  export interface DonationComponentDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Add To Cart Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 00011827-cb1e-48b3-80b6-4fdd924d98eb
    // Custom Data:
    addToCartText: string;
    // The Donate To Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: cc6d3be1-bebc-4f7f-ad84-b59cea837e95
    // Custom Data:
    donateToText: string;
    // The Donation Made Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: f79ae4b5-8323-4d0e-9632-cdb12b1a16f2
    // Custom Data:
    donationMadeText: string;
    // The Learn More Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: c6628cb3-d810-4d2f-9bff-aa8649587024
    // Custom Data:
    learnMoreText: string;
    // The Placeholder Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 058c3891-7c4d-464d-a161-8b2553b7a913
    // Custom Data:
    placeholderText: string;
  }
  // The Donation Rendering Parameters template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Rendering Parameters/Donation Rendering Parameters
  // ID: 81b82cde-de46-4a85-a743-a7bea528f353
  export interface DonationRenderingParametersRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Donation Data Source field.
    // Short description:
    // Field Type: Droplist
    // Field ID: f1563f56-1de4-4973-a7c6-f1349263090f
    // Custom Data:
    donationDataSource: string;
    // The Fixed Amounts field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 276e2726-1de3-48f3-a1f2-df879ff2fe32
    // Custom Data:
    fixedAmounts: string;
    // The Readonly field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: e662b13b-7913-474b-8a12-863e8de8218a
    // Custom Data:
    readonly: boolean;
  }
  // The Feature Switch template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Settings/Feature Switch
  // ID: cf066ab5-d371-4a4f-8d01-548e1f349bb8
  export interface FeatureSwitchRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The ShouldUpdateGetCartCache field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 6e460d08-f2af-4c85-a827-a212a97e07c1
    // Custom Data:
    shouldUpdateGetCartCache: boolean;
  }
  // The General Order Info template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/General Order Info
  // ID: 65b26cdb-4f6f-4b3a-8a88-36769a4ac001
  export interface GeneralOrderInfoRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Banner Info For Pending Open Orders field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 054d809b-3996-43fe-bfca-7c66469c01fc
    // Custom Data:
    bannerInfoForPendingOpenOrders: string;
    // The Email Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 45c0aec5-526f-426a-a72c-65b4f94cd2ed
    // Custom Data:
    emailText: string;
    // The General Info For Pending Open Orders field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 3fc7ab76-821b-4b0c-a34f-f21c50d69575
    // Custom Data:
    generalInfoForPendingOpenOrders: string;
    // The Open Quote Button Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 4ac190d1-caaa-4d3b-b523-eaa4ed8815e9
    // Custom Data:
    openQuoteButtonText: string;
    // The Print Invoice Button Link field.
    // Short description:
    // Field Type: General Link
    // Field ID: bcaecf0a-04d9-45d4-b58d-271e23b37e19
    // Custom Data:
    printInvoiceButtonLink: string;
    // The Print Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 5fe5cc29-723a-46d8-b15f-4d6e22837867
    // Custom Data:
    printText: string;
    // The Review Order Status field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 49614c24-95b3-493b-b200-7f7db0c972a5
    // Custom Data:
    reviewOrderStatus: string;
    // The Standard Banner Info field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 9e210be7-1121-48db-b54f-23b97f207860
    // Custom Data:
    standardBannerInfo: string;
  }
  // The GetOrder Failed Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/GetOrder Failed Data
  // ID: 4630305a-4872-4a94-9e04-ee7df6731088
  export interface GetOrderFailedDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Header field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 387ff6b7-863b-42b0-b367-db368068b7b6
    // Custom Data:
    header: string;
    // The Message Body field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: fe46fa4d-6383-4d2c-9cd4-7dba3b6447d9
    // Custom Data:
    messageBody: string;
  }
  // The Global Footer Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Global Footer Data
  // ID: a00f3ed0-34fe-418e-864a-6c818799688f
  export interface GlobalFooterDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Advertising Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: e31cc922-35d8-4772-9fe7-07c3cafd88e4
    // Custom Data:
    advertisingText: string;
    // The Copyright Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 0363a5ba-43bf-4691-a5fa-f63b389ffe1b
    // Custom Data:
    copyrightText: string;
    // The Privacy Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: ************************************
    // Custom Data:
    privacyText: string;
    // The Processing Country field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 3a3e15d4-4480-4548-8660-2a401f684e9f
    // Custom Data:
    processingCountry: string;
    // The Sitemap Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 24d45f18-f40c-46da-8e62-852dd930e1c5
    // Custom Data:
    sitemapText: string;
    // The Terms and Conditions Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: eeef88e0-6bb0-4a8d-ad84-d850314aa57f
    // Custom Data:
    termsAndConditionsText: string;
    // The Terms of Use Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: c001c0ad-5fce-431f-9f43-4ae101d313c5
    // Custom Data:
    termsOfUseText: string;
  }
  // The Google Analytics Event Rendering Parameters template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Rendering Parameters/Google Analytics Event Rendering Parameters
  // ID: bbb59dd1-70e4-492f-a1f9-69e79c67a8bb
  export interface GoogleAnalyticsEventRenderingParametersRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Event Body field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 777818fb-0c51-4c70-ad27-aa0b535095a4
    // Custom Data:
    eventBody: string;
    // The Event type field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 767ad006-3b8f-48a3-86a2-4804f926f34e
    // Custom Data:
    eventType: string;
  }
  // The Go To Checkout Button Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Go To Checkout Button Data
  // ID: 044a26bf-d809-45de-81b7-7a12dc38f704
  export interface GoToCheckoutButtonDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The ButtonText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 95f0adda-ff8e-4423-a7fd-be0b96b2c9f9
    // Custom Data:
    buttonText: string;
    // The Checkout Page Url field.
    // Short description:
    // Field Type: General Link
    // Field ID: 1eb46e9d-e9ad-492a-be15-60bd2cbb966c
    // Custom Data:
    checkoutPageUrl: string;
    // The LocalCurrency field.
    // Short description:
    // Field Type: Droplink
    // Field ID: c91c4565-4228-48f1-85e8-e45881546755
    // Custom Data:
    localCurrency: string;
  }
  // The Live Person Chat Rendering Parameters template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Rendering Parameters/Live Person Chat Rendering Parameters
  // ID: f0a59d6a-7756-4b26-9f10-ab04f249fe64
  export interface LivePersonChatRenderingParametersRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Chat Script Body field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 87521e4e-1558-49ee-b586-7a31c77a094a
    // Custom Data:
    chatScriptBody: string;
    // The livePersonId field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: ce39f825-8603-4b2f-a3a8-a169271899ed
    // Custom Data:
    livePersonId: string;
  }
  // The Local Chapters Search Parameters Template template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Local Chapters Search Parameters Template
  // ID: 7964d8c4-68c1-4a5f-939c-88f9bec7a2fe
  export interface LocalChaptersSearchParametersTemplateRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The distance field.
    // Short description:
    // Field Type: Integer
    // Field ID: d292e9f1-9f0d-47ac-ae64-d5fc56c03f84
    // Custom Data:
    distance: number;
    // The maxResult field.
    // Short description:
    // Field Type: Integer
    // Field ID: cf0dee42-013b-4ed7-96ac-b410087e5601
    // Custom Data:
    maxResult: number;
  }
  // The Membership Automatic Renewal Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Membership Automatic Renewal Data
  // ID: 9a9f20c4-fbe4-49b2-be4d-dbe6d5cc488e
  export interface MembershipAutomaticRenewalDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Header Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 1972ef95-6f4e-4f04-9b59-90bad6146a04
    // Custom Data:
    headerText: string;
    // The Text field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: eae2ea93-e07b-44a1-9da1-0115853e64ec
    // Custom Data:
    text: string;
  }
  // The MembershipBenefitsCardDataFolder template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/MembershipBenefitsCardDataFolder
  // ID: 245408cf-86ec-4da7-b650-6b9891f57af8
  export interface MembershipBenefitsCardDataFolderRenderingParams extends ReactJssModule.BaseRenderingParam {
  }
  // The MembershipBenefitsCardData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/MembershipBenefitsCardData
  // ID: 00ec4c3e-d5e6-40af-8694-ff54bf79497f
  export interface MembershipBenefitsCardDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The HeadingText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 0c49a53d-a6d9-4328-a248-7ddf06e9196c
    // Custom Data:
    headingText: string;
    // The Link field.
    // Short description:
    // Field Type: General Link
    // Field ID: bf3885d9-6fa5-446a-b448-7737bb87f7d0
    // Custom Data:
    link: string;
    // The Text field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 8f7e882b-46a7-4679-bd75-c5a2a360d541
    // Custom Data:
    text: string;
  }
  // The MembershipBenefitsCarouselData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/MembershipBenefitsCarouselData
  // ID: 2e147a6d-4c76-4ccb-beea-3678613a39ad
  export interface MembershipBenefitsCarouselDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Cards field.
    // Short description:
    // Field Type: Multilist
    // Field ID: 7aefd21f-244e-4309-af4d-ee7a723b554a
    // Custom Data:
    cards: string;
  }
  // The MembershipCongratulationsData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/MembershipCongratulationsData
  // ID: be8bd67a-67ad-46f4-a15f-f7a54e483b02
  export interface MembershipCongratulationsDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The GiftMembershipDescription field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 23d32d26-c14f-4ce6-8d8c-646d586c643f
    // Custom Data:
    giftMembershipDescription: string;
    // The GiftMembershipTitle field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: ca906d6a-304e-4cc8-ab35-5d40ecdd6e65
    // Custom Data:
    giftMembershipTitle: string;
    // The MembershipDescription field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 94eb25c6-3b48-48a9-8f56-3210c0641f53
    // Custom Data:
    membershipDescription: string;
    // The PurchaseTermsWithAutoRenewalStatement field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 8f90f0de-a2c9-44dc-9f7f-03c7e9f08747
    // Custom Data:
    purchaseTermsWithAutoRenewalStatement: string;
    // The PurchaseTermsWithoutAutoRenewStatement field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: d1818cf7-ca50-4939-a1a5-c0fa99f63738
    // Custom Data:
    purchaseTermsWithoutAutoRenewStatement: string;
    // The StudentMembershipDescription field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: f157ccff-51ce-4b0b-abae-81e4f3808489
    // Custom Data:
    studentMembershipDescription: string;
    // The Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: dd56682d-b4cb-45ac-8f4d-f4788fb46caa
    // Custom Data:
    title: string;
  }
  // The Membership Promo Modal Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Membership Promo Modal Data
  // ID: 1179143f-e6b1-41d0-9fda-88876e4e9772
  export interface MembershipPromoModalDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Add to Cart Button Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 290afceb-a251-4ede-a265-d2e8b8cc3ce6
    // Custom Data:
    addToCartButtonText: string;
    // The Cancel Button Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 6f8b11e1-7139-4474-b32d-82a031695313
    // Custom Data:
    cancelButtonText: string;
    // The List Items field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 77eb4005-57ef-4c83-9b06-f099e4f4d33f
    // Custom Data:
    listItems: string;
    // The Membership Items field.
    // Short description:
    // Field Type: Multilist
    // Field ID: 1ced9246-fffe-41b9-a6a9-199728b650ae
    // Custom Data:
    membershipItems: string;
    // The Options Link field.
    // Short description:
    // Field Type: General Link
    // Field ID: 1b02c3ed-047e-4730-8358-e9267bd7b7ad
    // Custom Data:
    optionsLink: string;
    // The SheerIDParameters field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 456e7905-9148-411e-8c0a-6e2c9668f0be
    // Custom Data:
    sheerIDParameters: string;
    // The Student Membership Validation Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: ca6a915f-f89d-4ab6-abde-0782087a6c95
    // Custom Data:
    studentMembershipValidationText: string;
    // The Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 19a6235e-6b03-4d65-826a-64c914515654
    // Custom Data:
    title: string;
    // The Total Savings Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: cc7ea172-aac6-4129-aeda-2a655c5e16b7
    // Custom Data:
    totalSavingsText: string;
  }
  // The Membership Terms And Conditions Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Membership Terms And Conditions Data
  // ID: cd5f92a9-4830-452a-b41c-54b6a41874c0
  export interface MembershipTermsAndConditionsDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Agree Text field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: eba2367c-55e9-4c52-9d00-8805afa48081
    // Custom Data:
    agreeText: string;
    // The Optional Agree Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: d2e43e1b-1c7a-420e-b0d9-3acd4fef758e
    // Custom Data:
    optionalAgreeText: string;
    // The Retiree Text field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: 4674e971-12f9-46a5-9d88-702638fa817f
    // Custom Data:
    retireeText: string;
    // The Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: be820b1b-148d-490c-a078-8823a81e2ea7
    // Custom Data:
    title: string;
  }
  // The MembershipUpsellData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/MembershipUpsellData
  // ID: 92d970b3-cb54-4608-b7d8-08c865e175e4
  export interface MembershipUpsellDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The ActionText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 5916c534-d5ee-4e41-8b53-840f6ed923c2
    // Custom Data:
    actionText: string;
    // The Body field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: ec9ec963-7183-487d-9d3f-654b8079a7e8
    // Custom Data:
    body: string;
    // The BodyNoDiscount field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 037dd51f-1177-47fd-a1bf-a052f44c35c5
    // Custom Data:
    bodyNoDiscount: string;
    // The Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: d6bd48a1-9037-42b2-b605-77850e80c449
    // Custom Data:
    title: string;
    // The TitleNoDiscount field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: cf9e7883-25f7-4b16-b5bc-609917064e15
    // Custom Data:
    titleNoDiscount: string;
  }
  // The MembershipUpsellNewData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/MembershipUpsellNewData
  // ID: 49e657d1-4651-4bb9-80ac-21b9a875dc5c
  export interface MembershipUpsellNewDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The ActionText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 028e82a8-ed4c-491b-be92-c3926d3e290d
    // Custom Data:
    actionText: string;
    // The Body field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 913cbb9b-2bae-49cd-bd25-b03ef8aaf485
    // Custom Data:
    body: string;
    // The BodyNoDiscount field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 27874113-5714-4d5b-b4c7-78938273845f
    // Custom Data:
    bodyNoDiscount: string;
  }
  // The Order Confirmation Summary Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Order Confirmation Summary Data
  // ID: 2441d74e-3f66-412b-b0be-89e7e24ff7ba
  export interface OrderConfirmationSummaryDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Header field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: c8344e1f-4414-4372-b9ca-6af6b4ae693e
    // Custom Data:
    header: string;
    // The Order discount field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: acf10980-1bba-4bfb-9b91-add3a79fbfd4
    // Custom Data:
    orderDiscount: string;
    // The Order total field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 54d0c72b-29c0-4fff-8f1c-bcd1a6f1a2f8
    // Custom Data:
    orderTotal: string;
    // The Quote Header field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 54c7d12b-ff1a-4d8f-bcc4-0aa76ddda3f1
    // Custom Data:
    quoteHeader: string;
    // The Subtotal field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 79a7c00a-4d7b-4a7b-8efd-266e279f6a12
    // Custom Data:
    subtotal: string;
    // The Taxes field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 499c31bc-afd4-498e-b344-fef9a2c9adfd
    // Custom Data:
    taxes: string;
    // The Voucher discount field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 1c36ce4c-c4c6-4abc-bfa2-69e21458005c
    // Custom Data:
    voucherDiscount: string;
  }
  // The Order Review Payment Folder template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Order Review Payment/Order Review Payment Folder
  // ID: 12ef9d96-959a-4210-b79d-7417fdf8a974
  export interface OrderReviewPaymentFolderRenderingParams extends ReactJssModule.BaseRenderingParam {
  }
  // The Order Review Payment template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Order Review Payment/Order Review Payment
  // ID: e353f941-9075-4120-9d15-8449c6e3312f
  export interface OrderReviewPaymentRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The BillingAddress field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: b48b0aba-0b7e-4749-8036-b0a64506a881
    // Custom Data:
    billingAddress: string;
    // The PaymentMethods field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 3194781b-bddc-498a-a453-fc8deb7485f4
    // Custom Data:
    paymentMethods: string;
    // The PurchaseOrder field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 74d82428-4d66-4fdc-949b-13f5e7a34086
    // Custom Data:
    purchaseOrder: string;
    // The VoucherAsPayment field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 41179a35-3ee4-43f8-bd74-862bbf080d12
    // Custom Data:
    voucherAsPayment: string;
  }
  // The Order Summary Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Order Summary Data
  // ID: 85d5d7a4-84d7-485e-a0f1-bc8eb8ee84fe
  export interface OrderSummaryDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Checkout Button Text field.
    // Short description: [Deprecated]
    // Field Type: Single-Line Text
    // Field ID: d71cbad5-2157-42a3-a64c-f48825fccce2
    // Custom Data:
    checkoutButtonText: string;
    // The Estimated Tax field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 017d8701-3629-4604-8dfd-9413561546ea
    // Custom Data:
    estimatedTax: string;
    // The Estimated Total field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: d96e6f00-c16f-465f-b41a-cae94fd161c6
    // Custom Data:
    estimatedTotal: string;
    // The GoToCartLink field.
    // Short description:
    // Field Type: General Link
    // Field ID: bc15aaca-d136-4d97-a1e8-86dccc4b72fc
    // Custom Data:
    goToCartLink: string;
    // The Go To Checkout Url field.
    // Short description: [Deprecated]
    // Field Type: Single-Line Text
    // Field ID: bcf8be2a-89c3-481b-95ae-84855f29b936
    // Custom Data:
    goToCheckoutUrl: string;
    // The Order Discount field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: e6520a5c-23c8-41be-ad6d-69a6efca5b62
    // Custom Data:
    orderDiscount: string;
    // The Subtotal field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 81ab94dc-acde-43d5-891a-b456ece203cc
    // Custom Data:
    subtotal: string;
    // The Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 0355c5c6-8ee6-4326-abae-68b3d1c1c93c
    // Custom Data:
    title: string;
    // The Total Cart Discount field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: fc5cab7b-61bf-4590-9793-074e3a155e86
    // Custom Data:
    totalCartDiscount: string;
    // The Voucher Order Discount field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 80f752fb-6e85-4fae-8907-eaa9b92412b5
    // Custom Data:
    voucherOrderDiscount: string;
  }
  // The Order Summary Mobile Accordion Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Order Summary Mobile Accordion Data
  // ID: 1d42e4e8-ae13-4ab3-bfdc-886b60a5c7bf
  export interface OrderSummaryMobileAccordionDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Hide Button Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: d6f85730-e614-4197-bf5e-51618d35eb13
    // Custom Data:
    hideButtonTitle: string;
    // The Show Button Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: a323f0da-5f46-4df4-99b6-606af70ceaa2
    // Custom Data:
    showButtonTitle: string;
  }
  // The Order Summary Mobile Rendering Parameters template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Rendering Parameters/Order Summary Mobile Rendering Parameters
  // ID: ff9ee7cc-f2d8-4542-b012-faa347be9e13
  export interface OrderSummaryMobileRenderingParametersRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Header Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: c3e40bf0-28bb-4139-96a7-c3c3277cc810
    // Custom Data:
    headerText: string;
    // The Show Button field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 65ddbc3a-f5b0-42c1-a521-8de3d1f66d44
    // Custom Data:
    showButton: boolean;
  }
  // The OrderSummaryRenderingParameters template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Rendering Parameters/OrderSummaryRenderingParameters
  // ID: fec63381-1ec9-43a3-8e41-7379331ad16d
  export interface OrderSummaryRenderingParametersRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The DisplayCartLines field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 5b124922-8c2e-4cd5-b484-62301ffee276
    // Custom Data:
    displayCartLines: boolean;
    // The HideGoToCheckoutButton field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 29894129-cf60-4c37-af5f-7513e519a311
    // Custom Data:
    hideGoToCheckoutButton: boolean;
  }
  // The PageLevelLoadingModalData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/PageLevelLoadingModalData
  // ID: 99c2fe4a-e06e-4387-94d4-a7cd9184fadb
  export interface PageLevelLoadingModalDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Message field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 5edd740d-2ecb-4a0b-849a-916305b11548
    // Custom Data:
    message: string;
    // The Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 2c912f1c-be1b-4eef-8fdb-8419c7022a1a
    // Custom Data:
    title: string;
  }
  // The PaymentAccordionData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/PaymentAccordionData
  // ID: 5770faed-ef53-4c93-961a-b4e2c599031d
  export interface PaymentAccordionDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The BillingExpandLinkText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: c4bf2f82-39cb-49e1-be18-ebfd5690ef23
    // Custom Data:
    billingExpandLinkText: string;
    // The BillingHeadingText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: e6bdb463-1bf1-4b7a-83f4-5f012db01011
    // Custom Data:
    billingHeadingText: string;
    // The ContactEmailTooltipText field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 1002bea8-0920-4c8b-9e32-a2326754b3e8
    // Custom Data:
    contactEmailTooltipText: string;
    // The ContactExpandLinkText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: b66a6ea3-43c3-413d-a6de-2af427073489
    // Custom Data:
    contactExpandLinkText: string;
    // The ContactHeadingText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: c7c3d82d-866a-4f6d-81e4-f3be1e3054c3
    // Custom Data:
    contactHeadingText: string;
    // The ContactPhoneToolTipText field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 8218bc0c-e875-4bfa-9847-f06f1e2f1c58
    // Custom Data:
    contactPhoneToolTipText: string;
    // The PaymentMethodExpandLinkText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 90eb0cfb-4dc9-454c-9db9-2a08b5082209
    // Custom Data:
    paymentMethodExpandLinkText: string;
    // The PaymentMethodHeadingText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: cd5f5642-9100-451e-8c7d-79cd531d0d63
    // Custom Data:
    paymentMethodHeadingText: string;
    // The PaymentSecureInformationText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 309dd2d3-602d-4eda-9624-d80b6b805509
    // Custom Data:
    paymentSecureInformationText: string;
  }
  // The PaymentAccordionRenderingParameters template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Rendering Parameters/PaymentAccordionRenderingParameters
  // ID: 44454d74-556d-4f94-87a9-143a175292b6
  export interface PaymentAccordionRenderingParametersRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The HidePaymentSection field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: f2321705-637a-44f7-b962-ab8f2356d0c3
    // Custom Data:
    hidePaymentSection: boolean;
    // The IsQuotePage field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 5eeefd54-5c7a-4dee-a383-65f02a0fb110
    // Custom Data:
    isQuotePage: boolean;
  }
  // The PaymentAddressFolder template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/PaymentAddress/PaymentAddressFolder
  // ID: 934b7ee8-441d-406d-89bd-00a423eada7c
  export interface PaymentAddressFolderRenderingParams extends ReactJssModule.BaseRenderingParam {
  }
  // The PaymentAddress template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/PaymentAddress/PaymentAddress
  // ID: 9d969a4a-e75d-4237-aade-3372d3288ec7
  export interface PaymentAddressRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Address Label field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: c7723a57-de99-4aec-b8ae-53c74f09e967
    // Custom Data:
    addressLabel: string;
  }
  // The PaymentBillingData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/PaymentBillingData
  // ID: 04b1e545-2d1a-480a-9e44-8f3dafb657e7
  export interface PaymentBillingDataRenderingParams extends AutoSaveUserDataRenderingParams {
    // The AddAddressButtonText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 501749fb-a955-45a6-a18b-9480b5de397e
    // Custom Data:
    addAddressButtonText: string;
    // The AddressLine1Empty field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 75a03b59-6a8c-4b50-8aec-fc8671a77e04
    // Custom Data:
    addressLine1empty: string;
    // The AddressLine1Label field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 4866b101-d404-45cd-a9bf-a6a5726b0d1a
    // Custom Data:
    addressLine1label: string;
    // The AddressLine1Placeholder field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 41655a0e-1b74-41ad-b83a-e3adec1e3020
    // Custom Data:
    addressLine1placeholder: string;
    // The AddressLine2Label field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: d1e3eaca-6ee5-4767-b5f5-71c82084559c
    // Custom Data:
    addressLine2label: string;
    // The AddressLine2Placeholder field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: eededc02-5e37-43c7-8a3f-de9e01850fb9
    // Custom Data:
    addressLine2placeholder: string;
    // The CancelButtonText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 23dd9bb5-4766-4cf5-9345-f6b32fbda163
    // Custom Data:
    cancelButtonText: string;
    // The CityEmpty field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 7a952589-6785-48d7-bc0d-14ab04593ec4
    // Custom Data:
    cityEmpty: string;
    // The CityLabel field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 5a6c302f-3ae6-4c20-9d10-eb8b09ad0c3c
    // Custom Data:
    cityLabel: string;
    // The CityPlaceholder field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 4849055a-3090-4d8a-abf4-0c02c39d0468
    // Custom Data:
    cityPlaceholder: string;
    // The CountryEmpty field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 486e4247-0f57-4ebe-a6a2-f52c9ef3ccce
    // Custom Data:
    countryEmpty: string;
    // The CountryLabel field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: ad0da3ad-3305-4c24-b55e-d41bd0d6e408
    // Custom Data:
    countryLabel: string;
    // The CountryPlaceholder field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 23e8d7d4-2fdb-45ea-805c-c384f31c762c
    // Custom Data:
    countryPlaceholder: string;
    // The EditButtonText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 875ccc00-e88a-4004-ac01-78e04b3c2f89
    // Custom Data:
    editButtonText: string;
    // The HeadingText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 09c4c50d-f38a-4a54-bd68-c8cde60934ee
    // Custom Data:
    headingText: string;
    // The LoadingText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: d3216b2a-bed6-49d2-81ff-f58887d064c5
    // Custom Data:
    loadingText: string;
    // The PostalCodeEmpty field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 30f97923-9953-48a7-bbdf-481386cabd9a
    // Custom Data:
    postalCodeEmpty: string;
    // The PostalCodeLabel field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 7d1b469a-f65d-46cc-b5f3-b956bd7ea82b
    // Custom Data:
    postalCodeLabel: string;
    // The PostalCodePlaceholder field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: fa3e47e7-bebc-49f9-b54e-4a90c26db27b
    // Custom Data:
    postalCodePlaceholder: string;
    // The RequiredFieldsMessage field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 9e8be739-b6af-45dc-ae9d-fd9673d26ca3
    // Custom Data:
    requiredFieldsMessage: string;
    // The SaveAsPrimaryLabel field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 766391f2-b7a4-4435-ace0-aa33f640d87c
    // Custom Data:
    saveAsPrimaryLabel: string;
    // The StateProvinceEmpty field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: bdaf1b2a-e492-4d0e-84a2-ebdf7f9d211b
    // Custom Data:
    stateProvinceEmpty: string;
    // The StateProvinceLabel field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 531dc32c-37be-4bf9-bfa0-589e0ea41f10
    // Custom Data:
    stateProvinceLabel: string;
    // The StateProvincePlaceholder field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 9e9305fe-9f58-48b9-a5a5-5ab47ebb5b57
    // Custom Data:
    stateProvincePlaceholder: string;
    // The SubmitButtonText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 86d5965c-1e16-4d37-ba4f-57c3bb990830
    // Custom Data:
    submitButtonText: string;
    // The UseAddressButtonText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: d5c104a3-a9e0-4323-a913-41af0be987e9
    // Custom Data:
    useAddressButtonText: string;
  }
  // The Payment Contact Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Payment Contact Data
  // ID: 34c0f7db-6a35-4e1d-8f99-aa99cd0bb93d
  export interface PaymentContactDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Dialing Code Label field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 25c18c93-e965-4f7b-b249-4e1dce93c813
    // Custom Data:
    dialingCodeLabel: string;
    // The DialingCountryCodeRequiredMessage field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 6dad62e1-7f4b-421b-a633-9811f1b5831f
    // Custom Data:
    dialingCountryCodeRequiredMessage: string;
    // The EmailAddressToolTipMessage field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 9e3bb5c3-24fe-4686-8ca7-622a55d15d4b
    // Custom Data:
    emailAddressToolTipMessage: string;
    // The email-field-label field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 56593a34-7faa-48e0-bd54-93bbd4f83ddd
    // Custom Data:
    emailFieldLabel: string;
    // The EmailFieldPlaceholderText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 434e70cf-3d5a-4b70-964a-e00c03fe1f0a
    // Custom Data:
    emailFieldPlaceholderText: string;
    // The EmailInvalidMessage field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 7ba732ec-e6ac-4fa3-b5e5-cfb65ec22da3
    // Custom Data:
    emailInvalidMessage: string;
    // The EmailRequiredMessage field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 8712ca1c-09b1-47a7-8474-f678cc8a679f
    // Custom Data:
    emailRequiredMessage: string;
    // The name-change-disclaimer field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: d4376d7f-14f3-4a21-a15d-4d98e3cce9b8
    // Custom Data:
    nameChangeDisclaimer: string;
    // The Phone Character Limit Message field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 3cb45669-be04-4669-a538-0a7642ad1a83
    // Custom Data:
    phoneCharacterLimitMessage: string;
    // The Phone Number Label field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: e45344ea-fe19-4df8-8b0c-6736823af875
    // Custom Data:
    phoneNumberLabel: string;
    // The Phone Number Placeholder Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: a66fe0c5-9962-49cd-adc9-909969ae0024
    // Custom Data:
    phoneNumberPlaceholderText: string;
    // The PhoneNumberToolTipMessage field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 5830762b-e31f-4cc9-a83c-4ecd90dedb69
    // Custom Data:
    phoneNumberToolTipMessage: string;
    // The PhoneRequiredMessage field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 2c106d5d-82bf-435a-90b2-3babdb847df3
    // Custom Data:
    phoneRequiredMessage: string;
    // The Phone Section Label Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 56886598-9698-4bfa-9fff-bd338555a106
    // Custom Data:
    phoneSectionLabelText: string;
    // The PhoneValidMessage field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: bada59f7-21f0-4e28-8c3f-2c0fd6837ced
    // Custom Data:
    phoneValidMessage: string;
    // The submit-button-text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 6c301623-81a6-4f48-9f3b-f1f4d5ec4ae6
    // Custom Data:
    submitButtonText: string;
  }
  // The Payment Email Main Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Payment Email Main Data
  // ID: 3a2a7965-7384-4412-a91f-2032b2200a87
  export interface PaymentEmailMainDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Component Header field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 76435e9e-6389-4d60-9417-4fe696368b8d
    // Custom Data:
    componentHeader: string;
  }
  // The Payment Method Adyen Dropin Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Payment Method Adyen Dropin Data
  // ID: a4cf769a-72a5-4024-b028-b1f3e0fe28fc
  export interface PaymentMethodAdyenDropinDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The AllowedPaymentMethodsMapping field.
    // Short description:
    // Field Type: Name Value List
    // Field ID: 4139a6eb-368f-4fc9-8569-bab309b2654f
    // Custom Data:
    allowedPaymentMethodsMapping: string;
    // The MembershipAndSubscriptionAutoRenewMessage field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: beab2724-4aa1-41b4-a27e-584aec2704b2
    // Custom Data:
    membershipAndSubscriptionAutoRenewMessage: string;
    // The MembershipAutoRenewMessage field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: 4f99e786-5ca0-41cb-862e-a8e9b75ea8a8
    // Custom Data:
    membershipAutoRenewMessage: string;
    // The Page Loader Body field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 639e7940-918e-4593-9c87-7772698b2c9b
    // Custom Data:
    pageLoaderBody: string;
    // The Page Loader Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: f177716d-c9c5-41c1-9cc1-ed76e2719c59
    // Custom Data:
    pageLoaderTitle: string;
    // The Settings field.
    // Short description:
    // Field Type: Droplink
    // Field ID: d2cd81a7-bdfd-46eb-a780-1bc03a00219a
    // Custom Data:
    settings: string;
    // The SubscriptionAutoRenewMessage field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: 68e47ac8-0a05-4ed3-97c6-c1484f93c691
    // Custom Data:
    subscriptionAutoRenewMessage: string;
    // The TranslationOverrides field.
    // Short description:
    // Field Type: Name Value List
    // Field ID: e6403750-bf79-4b9f-bfec-56b25569868e
    // Custom Data:
    translationOverrides: string;
  }
  // The Payment Method Adyen Pay by Secure Link Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Payment Method Adyen Pay by Secure Link Data
  // ID: 9e33af76-0e15-4f50-9917-b3c9eda84954
  export interface PaymentMethodAdyenPayBySecureLinkDataRenderingParams extends PaymentMethodDataRenderingParams {
  }
  // The Payment Method Alipay Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Payment Method Alipay Data
  // ID: cae07e06-aaab-4081-8bb4-9890091fa9d1
  export interface PaymentMethodAlipayDataRenderingParams extends PaymentMethodDataRenderingParams {
    // The RedirectInformationMessage field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 6aef76ce-873c-48b9-a176-a7ec4356e235
    // Custom Data:
    redirectInformationMessage: string;
  }
  // The PaymentMethodData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/PaymentMethodData
  // ID: 94f9771e-b024-4dd7-9d21-0c367c971e23
  export interface PaymentMethodDataRenderingParams extends AutoSaveUserDataRenderingParams {
    // The Cancel Button Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: f198938f-f938-4cbb-a56a-842a1b7c770b
    // Custom Data:
    cancelButtonText: string;
    // The Description field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 04210b7b-97c7-474a-b96e-36ca670d4dcf
    // Custom Data:
    description: string;
    // The Submit Button Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 2824a5b7-4bc2-40b4-a592-9d408e30143b
    // Custom Data:
    submitButtonText: string;
  }
  // The PaymentMethodPaypalData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/PaymentMethodPaypalData
  // ID: cbab6a60-78e5-47ce-b253-7109b9533a1a
  export interface PaymentMethodPaypalDataRenderingParams extends PaymentMethodDataRenderingParams {
    // The RedirectInformationMessage field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 782f47b8-108b-48d7-a75e-2188a18e2d3a
    // Custom Data:
    redirectInformationMessage: string;
    // The SavePaymentDescription field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 20214907-45b9-4abc-b9a7-c7d5517d0205
    // Custom Data:
    savePaymentDescription: string;
    // The SavePaymentSubTitle field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 4409e088-ef0c-4690-9767-cb18be2e38c5
    // Custom Data:
    savePaymentSubTitle: string;
  }
  // The Payment Method Razorpay Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Payment Method Razorpay Data
  // ID: 610e0589-93d8-4c8e-a2c6-07f557058746
  export interface PaymentMethodRazorpayDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The CreditCardLabel field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: c2b1437e-2371-4d2f-8bdd-6319d6aa20a2
    // Custom Data:
    creditCardLabel: string;
    // The CreditCardSubtext field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: d6c35601-1201-4828-ac43-c8c4ba3dc380
    // Custom Data:
    creditCardSubtext: string;
    // The MembershipAndSubscriptionAutoRenewMessage field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: 9f3f2185-13c7-4941-8a78-2067cfdd1720
    // Custom Data:
    membershipAndSubscriptionAutoRenewMessage: string;
    // The NetbankingLabel field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: c37d78ca-21f8-4bea-a74c-57cf8ed7a588
    // Custom Data:
    netbankingLabel: string;
    // The NetbankingSubtext field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: e7662200-52aa-4554-86da-3e15c6c78ee8
    // Custom Data:
    netbankingSubtext: string;
    // The UpiLabel field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: f59678a9-3a7c-4c27-b9e4-4d43e81f6d8a
    // Custom Data:
    upiLabel: string;
    // The UPILimitAmount field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 5560e6c3-6143-4bb7-be63-aa54250605f6
    // Custom Data:
    upiLimitAmount: string;
    // The UpiSubtext field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 4d3a8376-12de-4f63-9015-2af1536b5778
    // Custom Data:
    upiSubtext: string;
  }
  // The Payment Method Razorpay Pay by Secure Link Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Payment Method Razorpay Pay by Secure Link Data
  // ID: 2ea5a2a6-131e-43cd-9c7e-b307d421abaf
  export interface PaymentMethodRazorpayPayBySecureLinkDataRenderingParams extends PaymentMethodDataRenderingParams {
  }
  // The PaymentMethodWorldpayData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/PaymentMethodWorldpayData
  // ID: 58621709-4f02-4e32-9b0d-69cb289015bb
  export interface PaymentMethodWorldpayDataRenderingParams extends PaymentMethodDataRenderingParams {
    // The CardHolderNameEmpty field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: a3b57088-1ddf-404a-9542-7b4b7005d99d
    // Custom Data:
    cardHolderNameEmpty: string;
    // The CardHolderNameLabel field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: b29af014-647c-4297-8dc7-29c896a95087
    // Custom Data:
    cardHolderNameLabel: string;
    // The CardHolderNamePlaceholder field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: b191ae6e-51bd-4180-b049-f0688cacb36b
    // Custom Data:
    cardHolderNamePlaceholder: string;
    // The CardNumberInvalid field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 7decd6c3-0d29-4fe8-878b-ed50e71b74c0
    // Custom Data:
    cardNumberInvalid: string;
    // The CardNumberLabel field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 5395cc4a-54cd-4f02-acb1-8b87126facdc
    // Custom Data:
    cardNumberLabel: string;
    // The CardNumberPlaceholder field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 6f82954f-68c3-4763-8f72-c0260d9bce8a
    // Custom Data:
    cardNumberPlaceholder: string;
    // The ExpirationMonthEmpty field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: eb76077d-050e-49f3-a0d0-73729a485977
    // Custom Data:
    expirationMonthEmpty: string;
    // The ExpirationMonthLabel field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 1a2b6fe4-b9c9-47c3-b42a-52dfb45f07cb
    // Custom Data:
    expirationMonthLabel: string;
    // The ExpirationMonthPlaceholder field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 2e8b5b7b-5330-482c-9e8a-fb73b95d370a
    // Custom Data:
    expirationMonthPlaceholder: string;
    // The ExpirationMonthYearExpired field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 70f0c073-d728-4db7-958e-f0dfc1f5524f
    // Custom Data:
    expirationMonthYearExpired: string;
    // The ExpirationYearEmpty field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: ed863d43-2c99-4535-ab41-2fa5845789de
    // Custom Data:
    expirationYearEmpty: string;
    // The ExpirationYearLabel field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 2021ca8d-39e8-426f-843a-427d9e5dcdc1
    // Custom Data:
    expirationYearLabel: string;
    // The ExpirationYearPlaceholder field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 8176e2d9-e1fb-4eda-b18d-69ee1a4dc14d
    // Custom Data:
    expirationYearPlaceholder: string;
    // The SaveAsDefaultLabel field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 207ba64d-938c-4854-890b-cf8dd87fbe93
    // Custom Data:
    saveAsDefaultLabel: string;
    // The SaveAsDefaultSubText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: ebc7aaca-62ec-4006-b652-f7d12cbb9e59
    // Custom Data:
    saveAsDefaultSubText: string;
    // The SecurityCodeEmpty field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: debb3ef5-bca7-4282-a84e-8be2fba157fc
    // Custom Data:
    securityCodeEmpty: string;
    // The SecurityCodeLabel field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 969a8c3b-4ae6-4d9b-bf8c-841a2a37305f
    // Custom Data:
    securityCodeLabel: string;
    // The SecurityCodePlaceholder field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 20f75892-6ea3-4179-b2ac-eca90f57000f
    // Custom Data:
    securityCodePlaceholder: string;
    // The SecurityCodeTooltipText field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: d288099b-604c-48d0-a89b-c7d37b49e4ed
    // Custom Data:
    securityCodeTooltipText: string;
  }
  // The PaymentMethodWorldpayHostedPayment template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/PaymentMethodWorldpayHostedPayment
  // ID: bac98afe-a2a8-4cee-9e9d-f1bdd1c8b972
  export interface PaymentMethodWorldpayHostedPaymentRenderingParams extends PaymentMethodDataRenderingParams {
  }
  // The PayPal Pay Later Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/PayPal Pay Later Data
  // ID: 62a2a28a-1a63-4cef-b356-35d827d46ad6
  export interface PayPalPayLaterDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The AdyenSetting field.
    // Short description:
    // Field Type: Droplink
    // Field ID: f288ba3a-55a8-47d9-a658-3e55f013994c
    // Custom Data:
    adyenSetting: string;
  }
  // The PayPalPlaceOrderButton template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/PayPalPlaceOrderButton
  // ID: d5688d77-4ab0-4ce2-a027-114dc5fcd57a
  export interface PayPalPlaceOrderButtonRenderingParams extends SubmitOrderButtonDataRenderingParams {
    // The AuthorizationCancelledErrorMessage field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 3aafc5f0-bc68-45fb-834d-2ddfa9384321
    // Custom Data:
    authorizationCancelledErrorMessage: string;
    // The AuthorizationFailedErrorMessage field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 0b84211d-6b81-43a7-9454-1c7f85bce807
    // Custom Data:
    authorizationFailedErrorMessage: string;
    // The AuthorizationFallbackErrorMessage field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 02aca0a8-aa6d-437e-90f2-5e057b7159f4
    // Custom Data:
    authorizationFallbackErrorMessage: string;
    // The OrderProcessingStateBody field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 5afc11d7-87cc-4fd0-a443-b1bfcfdf3b89
    // Custom Data:
    orderProcessingStateBody: string;
    // The OrderProcessingStateTitle field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 004179d4-d4b2-4371-a204-88e9ff75a2b8
    // Custom Data:
    orderProcessingStateTitle: string;
    // The RedirectWaitingStateBody field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: edf8b99a-1d26-4577-abaa-33207e7f0e8f
    // Custom Data:
    redirectWaitingStateBody: string;
    // The RedirectWaitingStateTitle field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 552f73b2-f725-4b56-af3a-592d1103054d
    // Custom Data:
    redirectWaitingStateTitle: string;
  }
  // The PresumptiveChaptersData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/PresumptiveChaptersData
  // ID: 71925bb2-5e7c-4386-bf21-f97a1df48044
  export interface PresumptiveChaptersDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The AccordionButtonText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: ab35db1b-53a8-4179-90bd-99f90df7370e
    // Custom Data:
    accordionButtonText: string;
    // The AccordionInnerHeader field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 59b96202-371b-4561-93c3-fbca65192b30
    // Custom Data:
    accordionInnerHeader: string;
    // The AccordionSubtitle field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: 256c21d4-2ec5-4bed-830b-585367bd66f9
    // Custom Data:
    accordionSubtitle: string;
    // The CloseButtonText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: dc606e39-6c0c-42a3-ab2e-f6b5a4ab0673
    // Custom Data:
    closeButtonText: string;
    // The ExploreMoreChaptersButtonText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 35f05231-167e-4831-b559-b6f8c8928488
    // Custom Data:
    exploreMoreChaptersButtonText: string;
    // The ExploreMoreChaptersUrl field.
    // Short description:
    // Field Type: General Link
    // Field ID: 99eaec6f-4bad-46fe-8fc8-6f6529e33753
    // Custom Data:
    exploreMoreChaptersUrl: string;
    // The Header Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 559d9860-b159-41c7-908e-365f279897ee
    // Custom Data:
    headerTitle: string;
    // The Text field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: 2d26cce3-6151-4d86-92ac-d2331459b2da
    // Custom Data:
    text: string;
  }
  // The Primary Phone Number Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Primary Phone Number Data
  // ID: 94f87600-167d-41cd-89d8-46a4227c6681
  export interface PrimaryPhoneNumberDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Area Code field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 0f345a51-f3c7-4108-912f-2fa01fd01f48
    // Custom Data:
    areaCode: string;
    // The Country Code field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: b62bf734-869b-4d5a-8660-067473eb4b06
    // Custom Data:
    countryCode: string;
    // The Ext field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 539070fb-6919-4235-b901-6d42397faf2d
    // Custom Data:
    ext: string;
    // The Number field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: a712c2c6-20c6-4b3f-b26b-e986f4446bea
    // Custom Data:
    number: string;
    // The Phone Text field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: 529c6153-168e-47dc-9307-4c71d94c9228
    // Custom Data:
    phoneText: string;
    // The Phone Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 13de693c-3ebe-42db-b505-2e7ce7acf438
    // Custom Data:
    phoneTitle: string;
    // The Phone Tooltip Link field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 0162e61f-8b24-46b9-9cce-acd17faeebf3
    // Custom Data:
    phoneTooltipLink: string;
    // The Phone Tooltip Text field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: 89ca608c-dd03-4748-97a3-7f3fe75eecee
    // Custom Data:
    phoneTooltipText: string;
    // The Phone Tooltip Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 9f1ae551-c855-4fd3-ada4-cc508a162b65
    // Custom Data:
    phoneTooltipTitle: string;
  }
  // The Product Items Rendering Parameters template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Rendering Parameters/Product Items Rendering Parameters
  // ID: 97267763-dc8b-411a-9419-f068c4b24243
  export interface ProductItemsRenderingParametersRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The CartHeaderText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 20f58c76-264a-4f39-af47-7db39d29ae83
    // Custom Data:
    cartHeaderText: string;
    // The HideMemberPrice field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 896ea8e2-4851-4801-be58-ec371df0b459
    // Custom Data:
    hideMemberPrice: boolean;
    // The HidePriceInfoWhenMember field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 889aeafb-2f52-4f58-9ac2-1ba519fd2223
    // Custom Data:
    hidePriceInfoWhenMember: boolean;
    // The HideProductItemsQuantity field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 25045e8d-5629-4298-b956-5712000fa363
    // Custom Data:
    hideProductItemsQuantity: boolean;
    // The HideProductTypeIcon field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 7b0f93e9-680a-4b08-8f4e-d6887fe69242
    // Custom Data:
    hideProductTypeIcon: boolean;
    // The HideRegularPrice field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 005c43f8-1abe-4ffe-b708-5164b0906ab5
    // Custom Data:
    hideRegularPrice: boolean;
    // The isABTestEnabled field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: b93a99e2-2dab-4efc-bee6-97bfd685a9d8
    // Custom Data:
    isABTestEnabled: boolean;
    // The Product Items Data Source field.
    // Short description:
    // Field Type: Droplist
    // Field ID: 95999174-2ae6-4f33-bd6f-3b002f4770c8
    // Custom Data:
    productItemsDataSource: string;
    // The Read Only field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 98b350ac-aee8-442e-a53b-381bfedaa360
    // Custom Data:
    readOnly: boolean;
    // The Search Timeout After Key Press in Milliseconds field.
    // Short description:
    // Field Type: Integer
    // Field ID: af0ab8dd-77c6-407c-9162-a2b148e91881
    // Custom Data:
    searchTimeoutAfterKeyPressInMilliseconds: number;
    // The Show Cart Header field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 4dc6d222-ad7d-4893-a781-43726bf9f5fe
    // Custom Data:
    showCartHeader: boolean;
  }
  // The Promocode Component Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Promocode Component Data
  // ID: 4721390f-fdd6-464f-b36e-14374c72fc55
  export interface PromocodeComponentDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Apply Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 5ab9af36-37f6-4f2f-bd99-ef9670b33770
    // Custom Data:
    applyText: string;
    // The Promocode Placeholder field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 7f93d878-0c73-4309-b2d3-a1fdbcc56993
    // Custom Data:
    promocodePlaceholder: string;
  }
  // The Promocode Component template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Promocode Component
  // ID: 50ded6f7-ecc0-425e-887a-81ff66d73a2e
  export interface PromocodeComponentRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Button Label field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 47b3e476-7658-48bb-be41-a0a8188c559b
    // Custom Data:
    buttonLabel: string;
    // The Placeholder field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 6fd235be-7576-4329-91ee-c3b5bb0d2bfc
    // Custom Data:
    placeholder: string;
    // The Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 1ed708fd-5f16-4eea-ae8f-938db338df33
    // Custom Data:
    title: string;
    // The Tooltip Message field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 69855f0a-70a2-476e-8c6f-74735f011928
    // Custom Data:
    tooltipMessage: string;
  }
  // The Promo Modal Membership Item Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Promo Modal Membership Item Data
  // ID: 9edf6451-8e07-46f0-9be5-7d1e96e6ad82
  export interface PromoModalMembershipItemDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Membership Text field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: bc326365-14bd-4023-afcc-180fe8a7b83c
    // Custom Data:
    membershipText: string;
    // The ProductSku field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 329a816e-e846-4e58-8ff2-d57371df9d6c
    // Custom Data:
    productSku: string;
  }
  // The Quote Folder template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Quote Folder
  // ID: 438aa786-2e07-430b-8dcf-77fb56f39f51
  export interface QuoteFolderRenderingParams extends ReactJssModule.BaseRenderingParam {
  }
  // The Razorpay Submit Button Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Razorpay Submit Button Data
  // ID: 7650089d-abad-4e43-abae-a10fa4d93a40
  export interface RazorpaySubmitButtonDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The ButtonText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: bba61f04-e6d6-417c-bc7c-7920e2b6b01b
    // Custom Data:
    buttonText: string;
    // The OverlayBody field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: a201f942-68d3-4270-97d6-deb4fb2a7b74
    // Custom Data:
    overlayBody: string;
    // The OverlayTitle field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 7dbdb759-a0de-48fb-8297-b91b7b2c05d5
    // Custom Data:
    overlayTitle: string;
    // The Settings field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 8a083ef4-6a8c-4bbb-bc2f-481c37245f81
    // Custom Data:
    settings: string;
  }
  // The RequestQuoteLinkData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/RequestQuoteLinkData
  // ID: d59f8d58-8434-4e4f-8c52-b0a1421908d3
  export interface RequestQuoteLinkDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The GoToCheckoutLink field.
    // Short description:
    // Field Type: General Link
    // Field ID: 04b4ad17-0193-4622-8a1f-a2497ea3d7d4
    // Custom Data:
    goToCheckoutLink: string;
    // The LocalCurrency field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 57c14d32-dfc1-4b32-9541-aec67167baf1
    // Custom Data:
    localCurrency: string;
    // The TooltipMessage field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 472181c2-2a1e-4f8c-8c95-9156957fb4e4
    // Custom Data:
    tooltipMessage: string;
  }
  // The Return To PmiOrg Button template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Return To PmiOrg Button
  // ID: 4b9b60f0-4b8b-4da1-81e0-d109cba82416
  export interface ReturnToPmiOrgButtonRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Button Link field.
    // Short description:
    // Field Type: General Link
    // Field ID: b500daa3-51eb-41f9-bb76-b00a26796435
    // Custom Data:
    buttonLink: string;
    // The Button Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 02716444-8e97-4334-8f1a-da3c337d207f
    // Custom Data:
    buttonText: string;
  }
  // The Review Total template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Review Total
  // ID: b6480775-4af2-4203-9cf1-596b074bbeea
  export interface ReviewTotalRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Purchase field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 8298b455-5680-44f7-ac9c-92c7a33b2901
    // Custom Data:
    purchase: string;
    // The Total field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: d9447702-8e87-4a3d-a202-eb502e609d03
    // Custom Data:
    total: string;
    // The TotalSaving field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 17ee808c-4c6a-4a66-9a30-41e4aea2536f
    // Custom Data:
    totalSaving: string;
  }
  // The SavedPaymentCardsData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/SavedPaymentCardsData
  // ID: fd5a70f7-9b1e-4eb7-906d-36e3c5ef1aec
  export interface SavedPaymentCardsDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The AddNewButtonText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: d209b6a4-dc32-45ed-88d1-4b08313a9601
    // Custom Data:
    addNewButtonText: string;
    // The EditButtonText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: ecd5395d-fffb-4027-b816-2cb80f04b67d
    // Custom Data:
    editButtonText: string;
    // The HeadingText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: dd4c711c-56ab-441f-8f57-a9f0b08d4f48
    // Custom Data:
    headingText: string;
    // The SubmitButtonText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 6ea3ce6f-1415-4e78-9c34-906fd43b114a
    // Custom Data:
    submitButtonText: string;
  }
  // The ShoppingCartData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/ShoppingCartData
  // ID: c9cf08a0-3889-4e3d-872b-825d4c5865ee
  export interface ShoppingCartDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The EmptyCartBody field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 5e63b29d-bbfa-4aad-942f-aef0f922beed
    // Custom Data:
    emptyCartBody: string;
    // The EmptyCartHeading field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 5b1549b5-2e97-47c8-9b4c-5fd154f48177
    // Custom Data:
    emptyCartHeading: string;
    // The EmptyCartNavigateToLink field.
    // Short description:
    // Field Type: General Link
    // Field ID: 117b5cfd-1f53-40aa-a795-5c8e684fa0e6
    // Custom Data:
    emptyCartNavigateToLink: string;
  }
  // The Student Membership Confirmation Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Student Membership Confirmation Data
  // ID: ad640985-3d73-4f88-b101-114809ebb82c
  export interface StudentMembershipConfirmationDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Item Box Text field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 98279841-8fa6-4b74-b93a-193246812b40
    // Custom Data:
    itemBoxText: string;
    // The Item Box Title Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 0d789bcc-0b21-4373-8339-578d84d8031b
    // Custom Data:
    itemBoxTitleText: string;
    // The Note Text field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: f61fac9f-fad3-47b0-8187-bb91e171f457
    // Custom Data:
    noteText: string;
    // The Note Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 8170b282-0c05-4c87-8993-ce82d4487d12
    // Custom Data:
    noteTitle: string;
    // The Submit Button Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: c53ed6a5-df0e-4cd2-8153-d578313e7fcf
    // Custom Data:
    submitButtonText: string;
    // The Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 2e4a9761-d4d2-464f-a245-a17db594a547
    // Custom Data:
    title: string;
  }
  // The Submit Order Button Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Submit Order Button Data
  // ID: f2958f7d-bd23-4016-a727-35214bee6872
  export interface SubmitOrderButtonDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Adyen Pay by Secure Link Button Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: e56d408f-340f-4e91-af78-db68bc5b1818
    // Custom Data:
    adyenPayBySecureLinkButtonText: string;
    // The Alipay Button Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 78d0c51e-f011-44ae-929a-1327e8f26bfa
    // Custom Data:
    alipayButtonText: string;
    // The Alipay Order Processing State Body field.
    // Short description: [Deprecated]
    // Field Type: Single-Line Text
    // Field ID: ff1dac78-3599-4587-b410-2f708f608c59
    // Custom Data:
    alipayOrderProcessingStateBody: string;
    // The Alipay Order Processing State Title field.
    // Short description: [Deprecated]
    // Field Type: Single-Line Text
    // Field ID: 690fe3ae-5351-4226-a546-bfb8c0c44b0a
    // Custom Data:
    alipayOrderProcessingStateTitle: string;
    // The Alipay Redirect Waiting State Body field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 21eb6a42-e889-4d12-9241-234f41fa8cf0
    // Custom Data:
    alipayRedirectWaitingStateBody: string;
    // The Alipay Redirect Waiting State Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 84ae3cd7-a87f-4809-b100-d41855ab85c0
    // Custom Data:
    alipayRedirectWaitingStateTitle: string;
    // The ButtonText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 0acdeea8-2118-4977-8830-8da76eb6ac97
    // Custom Data:
    buttonText: string;
    // The Confirmation Page Url field.
    // Short description:
    // Field Type: General Link
    // Field ID: 08921a6a-9812-42c6-acba-a1bb40d327ce
    // Custom Data:
    confirmationPageUrl: string;
    // The OrderProcessingBody field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 1cb59b7a-8bcc-4e5c-b8fa-a56fb2664cde
    // Custom Data:
    orderProcessingBody: string;
    // The OrderProcessingTitle field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 212449d4-e2c0-433e-b165-5103b69be9bc
    // Custom Data:
    orderProcessingTitle: string;
    // The Razorpay Pay by Secure Link Button Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: ef0e5b60-0fcd-4e7d-93d9-129295750ab1
    // Custom Data:
    razorpayPayBySecureLinkButtonText: string;
    // The WorldpayHostedPaymentPlaceOrderText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 0c751bfb-eeb4-494a-997f-55c4d8486cf1
    // Custom Data:
    worldpayHostedPaymentPlaceOrderText: string;
  }
  // The Thank You Message template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Thank You Message
  // ID: c1e332ff-95ab-4613-96d9-89b13b612b41
  export interface ThankYouMessageRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Order Processing Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 67195295-6fd7-495f-a52d-a84e5c1048b4
    // Custom Data:
    orderProcessingText: string;
    // The Thank Inquiry Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: b86e3b7a-68e6-4718-916f-727bab832922
    // Custom Data:
    thankInquiryText: string;
    // The Thank Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 4bd62f9f-2c5a-4975-91a9-f9738e54f98c
    // Custom Data:
    thankText: string;
  }
  // The ViewInvoiceButtonData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/ViewInvoiceButtonData
  // ID: 5f740f0d-7d69-475d-bf40-52d105dd4728
  export interface ViewInvoiceButtonDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The CheckOrderStatusLink field.
    // Short description:
    // Field Type: General Link
    // Field ID: e5b2c3f0-029f-4f6f-88b3-836862eec64f
    // Custom Data:
    checkOrderStatusLink: string;
    // The ViewInvoiceButtonLink field.
    // Short description:
    // Field Type: General Link
    // Field ID: b23d817a-ceca-4314-a285-a9d9ecd5580b
    // Custom Data:
    viewInvoiceButtonLink: string;
  }
  // The ViewInvoicePendingButtonData template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/ViewInvoicePendingButtonData
  // ID: ba398f9b-4446-419b-b3e4-15c7ab63ac31
  export interface ViewInvoicePendingButtonDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The CheckOrderStatusLink field.
    // Short description:
    // Field Type: General Link
    // Field ID: f8683af4-6379-48fe-b134-83ab4ff44687
    // Custom Data:
    checkOrderStatusLink: string;
    // The ViewInvoiceButtonLink field.
    // Short description:
    // Field Type: General Link
    // Field ID: b2d820be-ff8a-4612-984d-eb93439ad995
    // Custom Data:
    viewInvoiceButtonLink: string;
    // The ViewOrderStatusText field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: fd292bfd-59c6-4e1e-9cdc-3742b350b6eb
    // Custom Data:
    viewOrderStatusText: string;
  }
  // The Vouchercode Component template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Vouchercode Component
  // ID: 122d2f80-0720-49b7-84e0-1e8324019091
  export interface VouchercodeComponentRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Button Label field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: de4f5815-5574-4563-bc96-31fbea9894c0
    // Custom Data:
    buttonLabel: string;
    // The Placeholder field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: caccba1a-b833-4458-8c5f-b5684cd47274
    // Custom Data:
    placeholder: string;
    // The Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: f6f7f6c2-61b9-4ac9-b92e-e12d061e90a4
    // Custom Data:
    title: string;
    // The Tooltip Message field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: dda9e5d5-7f87-41ae-afc6-c3bef3aea52b
    // Custom Data:
    tooltipMessage: string;
  }
  // The Voucher Component Data template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Checkout/Data Sources/Apply Voucher/Voucher Component Data
  // ID: 6695a9ee-e354-463b-baec-b92e810cebe7
  export interface VoucherComponentDataRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Agreement Text field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: eb419c1e-983d-48c5-933e-fabb79ee9c62
    // Custom Data:
    agreementText: string;
    // The Applied Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: bf0fd84b-2822-40c4-891c-100000abc65b
    // Custom Data:
    appliedText: string;
    // The Apply Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: be5fd7b5-f8ab-4278-9950-b9a0b373bfcb
    // Custom Data:
    applyText: string;
    // The Learn Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 5a47ed0c-a423-4600-b433-955d21cdd9cc
    // Custom Data:
    learnText: string;
    // The Placeholder Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: db4c1b84-aac3-43af-86da-35009f5c05d4
    // Custom Data:
    placeholderText: string;
    // The Remove Text field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 5e6f8770-e652-42dd-a1c0-d70d8a55ac2d
    // Custom Data:
    removeText: string;
    // The Tooltip Description field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: 0c6ec74e-18ac-4521-97ad-5a538bffe306
    // Custom Data:
    tooltipDescription: string;
    // The Tooltip Header field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 0ba798ab-ef89-431f-a5ff-520f545a1294
    // Custom Data:
    tooltipHeader: string;
    // The Tooltip Link field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 7e966440-890c-47eb-8983-7a5b1513d5b1
    // Custom Data:
    tooltipLink: string;
    // The Tooltip Linkname field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 70ecb8e1-6fc0-42ce-bfed-1fd8aff1338b
    // Custom Data:
    tooltipLinkname: string;
  }
  export namespace AddressChangeOptionsTemplate {
    export const templateId: string = 'f3d84f3f-ac51-430c-8b56-39623fda6801';
    export const templateName: string = 'AddressChangeOptions';
    export const addressUpdateLinkFieldId: string = 'e4dc03ec-101c-4141-8a1d-cc0fa7d64401';
    export const addressUpdateLinkFieldName: string = 'AddressUpdateLink';
    export const updateAddressSuggestionTextFieldId: string = '80ba908d-a978-40c2-8c86-738c30a2f09a';
    export const updateAddressSuggestionTextFieldName: string = 'UpdateAddressSuggestionText';
  }
  export namespace ApplyVoucherFolderTemplate {
    export const templateId: string = '25460bc1-0b9a-4b18-bc31-7f88b8deb89f';
    export const templateName: string = 'Apply Voucher Folder';
  }
  export namespace AutoSaveUserDataTemplate {
    export const templateId: string = '1f9ff9a3-ea4a-40f0-8a08-e50eb9b0c7e5';
    export const templateName: string = 'AutoSaveUserData';
    export const autoSaveTextFieldId: string = 'd4c5b41d-5842-4d3c-a6f1-26b6d94caa99';
    export const autoSaveTextFieldName: string = 'AutoSaveText';
  }
  export namespace BrazilCPFCodeDataTemplate {
    export const templateId: string = '60ebfc58-4381-4302-a9b9-6379eb5575bf';
    export const templateName: string = 'Brazil CPF Code Data';
    export const addCpfButtonTextFieldId: string = 'df269c6d-7c82-4614-9e7f-bd34372c3d3e';
    export const addCpfButtonTextFieldName: string = 'Add Cpf Button Text';
    export const allowCNPJFieldId: string = 'a38ce8d9-43fd-4708-92db-704e48f4a1f2';
    export const allowCNPJFieldName: string = 'Allow CNPJ';
    export const cpfFieldLabelFieldId: string = '43eb3754-bd05-4aa7-9237-81026762a304';
    export const cpfFieldLabelFieldName: string = 'Cpf Field Label';
    export const cpfFieldPlaceholderFieldId: string = 'aaa82e5b-64b0-434a-9aad-161af0216d1d';
    export const cpfFieldPlaceholderFieldName: string = 'Cpf Field Placeholder';
    export const expandLinkTextFieldId: string = '525d0e85-9a4c-48c5-82f3-dc335a98308a';
    export const expandLinkTextFieldName: string = 'Expand Link Text';
    export const headingTextFieldId: string = '8eb26979-aaa2-4d15-9e2f-8f5c1268ce31';
    export const headingTextFieldName: string = 'Heading Text';
    export const cpfInvalidMessageFieldId: string = '563cc6ec-81b1-42f2-9cba-6347baf9c171';
    export const cpfInvalidMessageFieldName: string = 'Cpf Invalid Message';
    export const cpfRequiredMessageFieldId: string = '21cf7574-1d3a-4ff7-8137-7ca9ac7c291c';
    export const cpfRequiredMessageFieldName: string = 'Cpf Required Message';
  }
  export namespace CartFolderTemplate {
    export const templateId: string = 'b5a8fe0a-c066-4b42-9a20-6584bc2441f7';
    export const templateName: string = 'Cart Folder';
  }
  export namespace CartMembershipDataTemplate {
    export const templateId: string = '2be48eb8-644d-4bc2-b3b1-5ba830f97a02';
    export const templateName: string = 'Cart Membership Data';
    export const membershipSectionFieldId: string = '0a6272b6-3163-49ee-b431-6619744709cc';
    export const membershipSectionFieldName: string = 'MembershipSection';
    export const registeredSectionFieldId: string = '57377190-db60-492a-b7a8-4fdc6ac86c32';
    export const registeredSectionFieldName: string = 'RegisteredSection';
    export const sheerIDParametersFieldId: string = 'feb268d7-1be2-496c-b447-32e7814b073e';
    export const sheerIDParametersFieldName: string = 'SheerIDParameters';
    export const studentMembershipDisclaimerTextFieldId: string = '13483cc2-71a8-46d4-a6a8-a8a9dfb8de4b';
    export const studentMembershipDisclaimerTextFieldName: string = 'StudentMembershipDisclaimerText';
    export const emailFieldId: string = 'b808958a-4292-41a6-be94-324e60efe676';
    export const emailFieldName: string = 'Email';
    export const sheerIdUrlFieldId: string = '7bea4473-24f0-4c98-8294-a0589cda17bd';
    export const sheerIdUrlFieldName: string = 'SheerIdUrl';
  }
  export namespace CartMembershipOptionsTemplate {
    export const templateId: string = 'e4cd7788-e383-49ee-a0b4-e1e7058ec7bb';
    export const templateName: string = 'CartMembershipOptions';
    export const applicationFeeTextFieldId: string = 'cb9f6fce-5035-47c6-99a5-97f378c4ee4a';
    export const applicationFeeTextFieldName: string = 'ApplicationFeeText';
    export const listItemsFieldId: string = '269a0fb9-6bfe-419c-afb9-034e0cbdfca7';
    export const listItemsFieldName: string = 'ListItems';
    export const optionsLinkFieldId: string = '5821391d-31c6-49f3-97bf-40f7042c47e5';
    export const optionsLinkFieldName: string = 'OptionsLink';
    export const standardTariffTextFieldId: string = '8c0979fa-17b7-4af5-a93d-3afb6aa3f3de';
    export const standardTariffTextFieldName: string = 'StandardTariffText';
    export const subTitleFieldId: string = '1a99fc2d-8f9b-45df-8320-d49da519ad95';
    export const subTitleFieldName: string = 'SubTitle';
    export const titleFieldId: string = 'b121896f-c2e4-45c8-afa9-9db3c900cdc4';
    export const titleFieldName: string = 'Title';
  }
  export namespace CartMembershipRenderingParametersTemplate {
    export const templateId: string = 'e76ad388-95e5-4536-8c4a-94fe003aa384';
    export const templateName: string = 'CartMembershipRenderingParameters';
    export const isABTestEnabledFieldId: string = '2440ff2a-be58-4d03-9a56-3a10bf8211f4';
    export const isABTestEnabledFieldName: string = 'isABTestEnabled';
  }
  export namespace CartTotalsRenderingParametersTemplate {
    export const templateId: string = '8a074b16-c179-4a9e-a684-eafab5ae4474';
    export const templateName: string = 'Cart Totals Rendering Parameters';
    export const cartTotalsDataSourceFieldId: string = '8d7cc943-dc3f-480b-8778-04cbfe046c82';
    export const cartTotalsDataSourceFieldName: string = 'Cart Totals Data Source';
    export const showTaxesFieldId: string = '4d2a45a5-2239-4c58-ba48-437f7ca69a5d';
    export const showTaxesFieldName: string = 'Show Taxes';
  }
  export namespace CartTotalsRowDataTemplate {
    export const templateId: string = 'a2d41f52-8c1f-48d2-8781-ecbea6b70501';
    export const templateName: string = 'Cart Totals Row Data';
  }
  export namespace CartTotalsRowRenderingParametersTemplate {
    export const templateId: string = '1525fca3-8f8f-41a9-b0a5-efaa00cb5f35';
    export const templateName: string = 'Cart Totals Row Rendering Parameters';
    export const fieldFieldId: string = 'c4721439-a7bc-4f10-9647-ba78c850f7eb';
    export const fieldFieldName: string = 'Field';
    export const isBoldFieldId: string = '7ac0d283-7d2d-4704-99b1-b8c5e3b149ff';
    export const isBoldFieldName: string = 'Is Bold';
    export const isRedFieldId: string = '064e998c-2880-42e8-884d-45d2fbcfd33e';
    export const isRedFieldName: string = 'Is Red';
    export const useLocalCurrencyTotalFieldId: string = '1ddf4958-3262-44b8-836c-1b54cef164e0';
    export const useLocalCurrencyTotalFieldName: string = 'UseLocalCurrencyTotal';
  }
  export namespace CartTotalsRowTemplate {
    export const templateId: string = 'eee924d7-b03e-4f69-af42-a13ff0804826';
    export const templateName: string = 'Cart Totals Row';
    export const subTextFieldId: string = '564d64be-ac0d-49de-9e13-baea011a215e';
    export const subTextFieldName: string = 'SubText';
    export const titleFieldId: string = '078b384a-dab1-4b10-b06d-db14167b292d';
    export const titleFieldName: string = 'Title';
  }
  export namespace CertificationRenewalAgreementOptionsTemplate {
    export const templateId: string = 'e3bef9d3-e703-4f73-b693-32c9114eddab';
    export const templateName: string = 'CertificationRenewalAgreementOptions';
    export const agreeIsCheckedFieldId: string = '12bf1b68-3d7b-49c2-8f4a-f5f9e5616460';
    export const agreeIsCheckedFieldName: string = 'AgreeIsChecked';
    export const agreementTextFieldId: string = '8adf48be-fa45-47b5-970b-fb1077af32c8';
    export const agreementTextFieldName: string = 'AgreementText';
  }
  export namespace ChangeSingleMembershipDataTemplate {
    export const templateId: string = '10911b27-4abf-4ee9-8e33-a9e2dbb19af4';
    export const templateName: string = 'Change Single Membership Data';
    export const accordionButtonTitleFieldId: string = '964dcce7-f808-4b0e-a802-9307c6554449';
    export const accordionButtonTitleFieldName: string = 'Accordion Button Title';
    export const headerDescriptionFieldId: string = '8b657aef-5407-4973-8a00-4be3494e01af';
    export const headerDescriptionFieldName: string = 'Header Description';
    export const headerTitleFieldId: string = 'd0070b11-1fac-498f-8f06-74d1f06ae985';
    export const headerTitleFieldName: string = 'Header Title';
    export const expandedDescriptionFieldId: string = 'dd026107-7891-4a67-94fb-7fe567fef875';
    export const expandedDescriptionFieldName: string = 'Expanded Description';
    export const expandedTitleFieldId: string = 'e62f6dc0-b1f9-443b-b987-089ae80ebe82';
    export const expandedTitleFieldName: string = 'Expanded Title';
  }
  export namespace CheckoutButtonsDataTemplate {
    export const templateId: string = 'a3388754-626a-4f5a-aeb7-1e0359134b91';
    export const templateName: string = 'Checkout Buttons Data';
    export const continueShoppingLinkFieldId: string = '76feda09-65d0-4ef0-9efd-a72ef14ae5b4';
    export const continueShoppingLinkFieldName: string = 'Continue Shopping Link';
    export const goToCheckoutTextFieldId: string = '7f00e387-fdf4-4c4d-9c11-2aef12f7e131';
    export const goToCheckoutTextFieldName: string = 'Go to Checkout Text';
    export const goToShoppingTextFieldId: string = '9ff0d674-ceba-4256-8855-fccd75b4348c';
    export const goToShoppingTextFieldName: string = 'Go to Shopping Text';
  }
  export namespace CheckoutContainerDataTemplate {
    export const templateId: string = '2a2002ea-d572-476f-b06c-d051eff55d86';
    export const templateName: string = 'CheckoutContainerData';
    export const backButtonTextFieldId: string = 'b05270e6-1011-4569-95d9-9f9b6c166a59';
    export const backButtonTextFieldName: string = 'BackButtonText';
    export const goToCartLinkFieldId: string = '35c8c0ab-8113-4b48-8dc6-dd8035708d6e';
    export const goToCartLinkFieldName: string = 'GoToCartLink';
    export const headingFieldId: string = 'a3b0ae12-c267-4603-8d3f-9c38200d8e0a';
    export const headingFieldName: string = 'Heading';
    export const showBackButtonFieldId: string = '8c779791-dcb3-489a-b5e7-29c6061d5058';
    export const showBackButtonFieldName: string = 'ShowBackButton';
  }
  export namespace CheckoutDonationDataTemplate {
    export const templateId: string = '9a1f7ffe-c342-4689-922e-61680a7cc18a';
    export const templateName: string = 'CheckoutDonationData';
    export const donationAddToCartTextFieldId: string = '438141d1-cf3f-491a-8446-652b7aed0ade';
    export const donationAddToCartTextFieldName: string = 'DonationAddToCartText';
    export const donationHeaderFieldId: string = '083b673b-4345-44a7-ac2e-988d1713d9fc';
    export const donationHeaderFieldName: string = 'DonationHeader';
    export const donationMadeSubTextFieldId: string = '2d1da220-c44a-4ab6-976e-c61dbd655597';
    export const donationMadeSubTextFieldName: string = 'DonationMadeSubText';
    export const donationMadeTextFieldId: string = '21353792-43f6-4cf8-b11e-707d8e7bb86d';
    export const donationMadeTextFieldName: string = 'DonationMadeText';
    export const donationOtherAmountPlaceholderTextFieldId: string = '9f82eb10-4338-4f9b-aaeb-eb2ca9fcab0b';
    export const donationOtherAmountPlaceholderTextFieldName: string = 'DonationOtherAmountPlaceholderText';
    export const donationSubHeaderFieldId: string = '919c0bef-5c86-4e5c-b749-ea160d8edc0e';
    export const donationSubHeaderFieldName: string = 'DonationSubHeader';
  }
  export namespace CheckoutFolderTemplate {
    export const templateId: string = '8cc4ca69-c783-4d05-8880-5a8dc5688a4f';
    export const templateName: string = 'Checkout Folder';
  }
  export namespace CheckoutNavigationRenderingParametersTemplate {
    export const templateId: string = 'd22423bc-190c-4767-9d87-ebebf5f1b18e';
    export const templateName: string = 'Checkout Navigation Rendering Parameters';
    export const hideNavigationFieldId: string = '0af848f4-3790-46f6-b769-f4417c8ca903';
    export const hideNavigationFieldName: string = 'Hide Navigation';
  }
  export namespace CheckoutNavigationStepsTemplate {
    export const templateId: string = 'f266a718-18e6-4978-b827-5c6c81e43350';
    export const templateName: string = 'Checkout Navigation Steps';
    export const mixedProductsFieldId: string = '1e74ff70-991e-42cb-ba2b-6045fc514461';
    export const mixedProductsFieldName: string = 'Mixed products';
    export const virtualProductsFieldId: string = 'e6cbc372-49b4-404d-a02a-af58af7f8003';
    export const virtualProductsFieldName: string = 'Virtual products';
  }
  export namespace CheckoutNavigationStepTemplate {
    export const templateId: string = '57cfa14b-718e-43c4-a1e0-5553ec8000b8';
    export const templateName: string = 'Checkout Navigation Step';
    export const stepIdFieldId: string = 'cb7502d8-cf47-4919-a528-ac7dcc1b636a';
    export const stepIdFieldName: string = 'Step Id';
    export const stepLinkFieldId: string = '89ad9809-f42a-444b-b484-e2c2be27b3ff';
    export const stepLinkFieldName: string = 'Step Link';
    export const stepTitleFieldId: string = '6a4ad5b5-01c6-483b-8d2f-bf4e777acdc9';
    export const stepTitleFieldName: string = 'Step Title';
  }
  export namespace CheckoutSettingsTemplate {
    export const templateId: string = '54c627b0-7790-40b3-978b-93d1de2857e4';
    export const templateName: string = 'Checkout Settings';
  }
  export namespace CheckoutStepDataTemplate {
    export const templateId: string = '76e62021-2e33-48bd-86ec-60d3f8bfad56';
    export const templateName: string = 'Checkout Step Data';
    export const buttonTextFieldId: string = '24aaf99c-1449-4344-b6f2-a37018414501';
    export const buttonTextFieldName: string = 'Button Text';
    export const titleFieldId: string = 'a294e893-34eb-452d-9a7c-cf8f7d229f49';
    export const titleFieldName: string = 'Title';
  }
  export namespace ConfirmationAccessItemsDataTemplate {
    export const templateId: string = 'e66b582c-235a-4240-b268-2f28310887c9';
    export const templateName: string = 'ConfirmationAccessItemsData';
    export const coursesButtonLinkFieldId: string = 'cded1ad3-4027-471b-9f4f-b508303c5752';
    export const coursesButtonLinkFieldName: string = 'CoursesButtonLink';
    export const coursesTextFieldId: string = 'e0926c3c-1cdb-407e-b92d-8d4ce0a5fcc7';
    export const coursesTextFieldName: string = 'CoursesText';
    export const dashboardButtonLinkFieldId: string = '1147b3f6-9b5f-4708-a908-42e14fbf3c24';
    export const dashboardButtonLinkFieldName: string = 'DashboardButtonLink';
    export const dashboardTextFieldId: string = '74064b98-5359-4a83-8c10-8d8e6cb51aba';
    export const dashboardTextFieldName: string = 'DashboardText';
    export const eligibleProductTypesToViewCoursesFieldId: string = 'd4550c38-7c5d-4e3b-966d-fb44a0fbd5f3';
    export const eligibleProductTypesToViewCoursesFieldName: string = 'EligibleProductTypesToViewCourses';
    export const headerFieldId: string = 'c8328c4c-26fd-4563-95f4-1d596e8d1145';
    export const headerFieldName: string = 'Header';
  }
  export namespace ConfirmationContainerDataTemplate {
    export const templateId: string = '933b6f7f-bfa4-456f-ae36-d87e5bf34eb0';
    export const templateName: string = 'ConfirmationContainerData';
    export const pendingOrderTitleFieldId: string = '4ab87d62-6354-48d2-ba2f-3747e0039801';
    export const pendingOrderTitleFieldName: string = 'PendingOrderTitle';
    export const quoteTitleFieldId: string = 'cf74a28d-c5e1-4e3d-8063-2f005139b131';
    export const quoteTitleFieldName: string = 'QuoteTitle';
    export const titleFieldId: string = 'f35841e1-5e81-4992-97ee-fdbe5a06b8ea';
    export const titleFieldName: string = 'Title';
  }
  export namespace ConfirmationDetailsDataTemplate {
    export const templateId: string = 'ec050c0a-da75-4903-988d-4247e51095ae';
    export const templateName: string = 'ConfirmationDetailsData';
    export const emailSentTextFieldId: string = 'b4ca8985-9b4f-4c76-a778-0ac4167ef6c5';
    export const emailSentTextFieldName: string = 'EmailSentText';
    export const generalBannerInfoForPendingOpenOrdersFieldId: string = '7da93986-697c-43e0-8c8d-d828ba426471';
    export const generalBannerInfoForPendingOpenOrdersFieldName: string = 'GeneralBannerInfoForPendingOpenOrders';
    export const notRecievedEmailTextFieldId: string = '5b26716f-4419-45a4-a5a2-d8340fb78756';
    export const notRecievedEmailTextFieldName: string = 'NotRecievedEmailText';
    export const quoteSentTextFieldId: string = 'c61f577c-51dd-4683-8a94-b2a83d73fbc4';
    export const quoteSentTextFieldName: string = 'QuoteSentText';
  }
  export namespace ConfirmationFolderTemplate {
    export const templateId: string = '9030f3a6-2642-48af-ac57-320b2188015d';
    export const templateName: string = 'ConfirmationFolder';
  }
  export namespace ConfirmationPageFooterDataTemplate {
    export const templateId: string = 'a4829610-0df2-472e-96e0-e9ae43732afa';
    export const templateName: string = 'Confirmation Page Footer Data';
  }
  export namespace ConfirmationPaymentMethodDataTemplate {
    export const templateId: string = '070bca51-8f8e-41ba-a025-62341d919606';
    export const templateName: string = 'Confirmation Payment Method Data';
    export const imageLookUpFieldId: string = '8238bfc2-a114-4746-be7c-65bdf1eafa3c';
    export const imageLookUpFieldName: string = 'imageLookUp';
  }
  export namespace CongatulationsGiftMembershipTemplate {
    export const templateId: string = 'c5135fca-cfd0-409e-9031-4f62ee03625e';
    export const templateName: string = 'Congatulations Gift Membership';
    export const headingFieldId: string = 'efc50d87-cac4-42fb-9f56-6550d3970bfc';
    export const headingFieldName: string = 'Heading';
    export const textFieldId: string = '159ac32f-fb4d-4817-9d0b-d5d1fff65ea3';
    export const textFieldName: string = 'Text';
  }
  export namespace CongratulationsCertificationTemplate {
    export const templateId: string = '186c9b38-0c8b-4b58-bb8c-9524cb51f7c4';
    export const templateName: string = 'Congratulations Certification';
    export const congratulationsTextFieldId: string = '45630122-5cfc-4309-a305-c2a8f31ffa37';
    export const congratulationsTextFieldName: string = 'Congratulations Text';
  }
  export namespace CongratulationsMembershipTemplate {
    export const templateId: string = '10eae8c9-0f23-4f28-8387-5966dbe8be6c';
    export const templateName: string = 'Congratulations Membership';
    export const descriptionFieldId: string = '496b5054-8d0e-4d66-9370-cb298cf38151';
    export const descriptionFieldName: string = 'Description';
    export const membershipDescriptionWithoutRenewalStatementFieldId: string = '4008fe49-a9a4-4cec-9e8d-bc11a49f1e12';
    export const membershipDescriptionWithoutRenewalStatementFieldName: string = 'MembershipDescriptionWithoutRenewalStatement';
    export const studentDescriptionFieldId: string = '3b573eee-6b6c-4587-9b56-c5b361e62089';
    export const studentDescriptionFieldName: string = 'Student Description';
    export const titleFieldId: string = 'feb57d55-f208-4d57-81e7-9dbd11503407';
    export const titleFieldName: string = 'Title';
  }
  export namespace ContactUsNowTemplate {
    export const templateId: string = 'c422339a-1f48-4dcd-99b8-2a2ee6be96a1';
    export const templateName: string = 'ContactUsNow';
    export const contactUsLinkFieldId: string = '59fb7856-6381-4b36-a5f1-0addacb97aa4';
    export const contactUsLinkFieldName: string = 'ContactUsLink';
    export const headerFieldId: string = 'b8c41268-3e4e-4e7c-b938-74d3003aeeb8';
    export const headerFieldName: string = 'Header';
    export const subTextFieldId: string = '8d72f01b-9396-4a6d-a5eb-6d6a0ee61b1f';
    export const subTextFieldName: string = 'SubText';
  }
  export namespace ContinueShoppingButtonTemplate {
    export const templateId: string = '46b4f640-2b0a-48ba-997d-77a19ea5b048';
    export const templateName: string = 'Continue Shopping Button';
    export const continueShoppingButtonTextFieldId: string = '8e461f6d-d8ae-4668-a1d6-ed70802cc72c';
    export const continueShoppingButtonTextFieldName: string = 'continue shopping button text';
    export const continueShoppingLinkFieldId: string = 'ff6a4db0-017c-4eb8-bcf5-efa97b7a84c7';
    export const continueShoppingLinkFieldName: string = 'continue shopping link';
  }
  export namespace ConvertquoteTemplate {
    export const templateId: string = 'dd5bc92a-0233-4f4a-a9d2-6f473990ede5';
    export const templateName: string = 'convertquote';
    export const componentImageFieldId: string = 'fda37110-7889-460e-9838-03d4f0a7b7f7';
    export const componentImageFieldName: string = 'componentImage';
    export const problemDescriptionTextFieldId: string = '6d3bb524-f4fd-4308-bcf7-4841987300c1';
    export const problemDescriptionTextFieldName: string = 'problemDescriptionText';
  }
  export namespace CourseLibraryLinkTemplate {
    export const templateId: string = 'd47f4f92-96e8-458b-8e3e-7736ffaf968d';
    export const templateName: string = 'Course Library Link';
    export const linkTextFieldId: string = '44d60258-3b50-4d27-9a45-9cc03f3cff17';
    export const linkTextFieldName: string = 'Link Text';
    export const eligibleProductTypesToShowLinkFieldId: string = '1a03e9f7-581c-41ba-8533-4b0c6829e4d4';
    export const eligibleProductTypesToShowLinkFieldName: string = 'Eligible Product Types to Show Link';
  }
  export namespace CreateQuoteButtonDataTemplate {
    export const templateId: string = 'c05b1cb0-c6b1-474a-9ea3-a8d2aa2d6a84';
    export const templateName: string = 'Create Quote Button Data';
    export const buttonTextFieldId: string = '7d39ca00-1f81-49b5-9f09-471d0d930528';
    export const buttonTextFieldName: string = 'ButtonText';
    export const confirmationPageUrlFieldId: string = '2865a7e7-4fbb-41b7-9348-ffd855173cf2';
    export const confirmationPageUrlFieldName: string = 'Confirmation Page Url';
  }
  export namespace CurrencyConfirmationTemplate {
    export const templateId: string = '8538a146-6ff1-4686-989d-5da0f4f1f746';
    export const templateName: string = 'Currency Confirmation';
    export const checkoutButtonTextFieldId: string = '2d2128af-5975-4556-92d2-1101a88635e4';
    export const checkoutButtonTextFieldName: string = 'Checkout Button Text';
    export const pilotTextFieldId: string = '47d46a0e-860e-4131-b2cb-5457d0826b02';
    export const pilotTextFieldName: string = 'Pilot Text';
    export const preferenceTextFieldId: string = '34882baa-fbf4-4212-80fc-0e1dc3c533a0';
    export const preferenceTextFieldName: string = 'Preference Text';
    export const preferenceTextsFieldId: string = 'ef40d425-b6ae-4d16-8bf9-098eb93dc28a';
    export const preferenceTextsFieldName: string = 'Preference Texts';
    export const titleFieldId: string = 'e52f20be-6fc4-4001-b3b5-eb2efa2066e7';
    export const titleFieldName: string = 'Title';
  }
  export namespace DiscountComponentDataTemplate {
    export const templateId: string = '355ac908-29ed-497a-91a0-ea30c3b38111';
    export const templateName: string = 'Discount Component Data';
    export const promoTitleFieldId: string = '9da7dae4-0948-40a9-bd11-1615353fb1dc';
    export const promoTitleFieldName: string = 'Promo Title';
    export const voucherTitleFieldId: string = '2ef6b614-3b5b-45e5-b323-5f690058623e';
    export const voucherTitleFieldName: string = 'Voucher Title';
  }
  export namespace DonationComponentDataTemplate {
    export const templateId: string = '329c4051-88fc-4979-a8cd-7ca63a507172';
    export const templateName: string = 'Donation Component Data';
    export const addToCartTextFieldId: string = '00011827-cb1e-48b3-80b6-4fdd924d98eb';
    export const addToCartTextFieldName: string = 'Add To Cart Text';
    export const donateToTextFieldId: string = 'cc6d3be1-bebc-4f7f-ad84-b59cea837e95';
    export const donateToTextFieldName: string = 'Donate To Text';
    export const donationMadeTextFieldId: string = 'f79ae4b5-8323-4d0e-9632-cdb12b1a16f2';
    export const donationMadeTextFieldName: string = 'Donation Made Text';
    export const learnMoreTextFieldId: string = 'c6628cb3-d810-4d2f-9bff-aa8649587024';
    export const learnMoreTextFieldName: string = 'Learn More Text';
    export const placeholderTextFieldId: string = '058c3891-7c4d-464d-a161-8b2553b7a913';
    export const placeholderTextFieldName: string = 'Placeholder Text';
  }
  export namespace DonationRenderingParametersTemplate {
    export const templateId: string = '81b82cde-de46-4a85-a743-a7bea528f353';
    export const templateName: string = 'Donation Rendering Parameters';
    export const donationDataSourceFieldId: string = 'f1563f56-1de4-4973-a7c6-f1349263090f';
    export const donationDataSourceFieldName: string = 'Donation Data Source';
    export const fixedAmountsFieldId: string = '276e2726-1de3-48f3-a1f2-df879ff2fe32';
    export const fixedAmountsFieldName: string = 'Fixed Amounts';
    export const readonlyFieldId: string = 'e662b13b-7913-474b-8a12-863e8de8218a';
    export const readonlyFieldName: string = 'Readonly';
  }
  export namespace FeatureSwitchTemplate {
    export const templateId: string = 'cf066ab5-d371-4a4f-8d01-548e1f349bb8';
    export const templateName: string = 'Feature Switch';
    export const shouldUpdateGetCartCacheFieldId: string = '6e460d08-f2af-4c85-a827-a212a97e07c1';
    export const shouldUpdateGetCartCacheFieldName: string = 'ShouldUpdateGetCartCache';
  }
  export namespace GeneralOrderInfoTemplate {
    export const templateId: string = '65b26cdb-4f6f-4b3a-8a88-36769a4ac001';
    export const templateName: string = 'General Order Info';
    export const bannerInfoForPendingOpenOrdersFieldId: string = '054d809b-3996-43fe-bfca-7c66469c01fc';
    export const bannerInfoForPendingOpenOrdersFieldName: string = 'Banner Info For Pending Open Orders';
    export const emailTextFieldId: string = '45c0aec5-526f-426a-a72c-65b4f94cd2ed';
    export const emailTextFieldName: string = 'Email Text';
    export const generalInfoForPendingOpenOrdersFieldId: string = '3fc7ab76-821b-4b0c-a34f-f21c50d69575';
    export const generalInfoForPendingOpenOrdersFieldName: string = 'General Info For Pending Open Orders';
    export const openQuoteButtonTextFieldId: string = '4ac190d1-caaa-4d3b-b523-eaa4ed8815e9';
    export const openQuoteButtonTextFieldName: string = 'Open Quote Button Text';
    export const printInvoiceButtonLinkFieldId: string = 'bcaecf0a-04d9-45d4-b58d-271e23b37e19';
    export const printInvoiceButtonLinkFieldName: string = 'Print Invoice Button Link';
    export const printTextFieldId: string = '5fe5cc29-723a-46d8-b15f-4d6e22837867';
    export const printTextFieldName: string = 'Print Text';
    export const reviewOrderStatusFieldId: string = '49614c24-95b3-493b-b200-7f7db0c972a5';
    export const reviewOrderStatusFieldName: string = 'Review Order Status';
    export const standardBannerInfoFieldId: string = '9e210be7-1121-48db-b54f-23b97f207860';
    export const standardBannerInfoFieldName: string = 'Standard Banner Info';
  }
  export namespace GetOrderFailedDataTemplate {
    export const templateId: string = '4630305a-4872-4a94-9e04-ee7df6731088';
    export const templateName: string = 'GetOrder Failed Data';
    export const headerFieldId: string = '387ff6b7-863b-42b0-b367-db368068b7b6';
    export const headerFieldName: string = 'Header';
    export const messageBodyFieldId: string = 'fe46fa4d-6383-4d2c-9cd4-7dba3b6447d9';
    export const messageBodyFieldName: string = 'Message Body';
  }
  export namespace GlobalFooterDataTemplate {
    export const templateId: string = 'a00f3ed0-34fe-418e-864a-6c818799688f';
    export const templateName: string = 'Global Footer Data';
    export const advertisingTextFieldId: string = 'e31cc922-35d8-4772-9fe7-07c3cafd88e4';
    export const advertisingTextFieldName: string = 'Advertising Text';
    export const copyrightTextFieldId: string = '0363a5ba-43bf-4691-a5fa-f63b389ffe1b';
    export const copyrightTextFieldName: string = 'Copyright Text';
    export const privacyTextFieldId: string = '************************************';
    export const privacyTextFieldName: string = 'Privacy Text';
    export const processingCountryFieldId: string = '3a3e15d4-4480-4548-8660-2a401f684e9f';
    export const processingCountryFieldName: string = 'Processing Country';
    export const sitemapTextFieldId: string = '24d45f18-f40c-46da-8e62-852dd930e1c5';
    export const sitemapTextFieldName: string = 'Sitemap Text';
    export const termsAndConditionsTextFieldId: string = 'eeef88e0-6bb0-4a8d-ad84-d850314aa57f';
    export const termsAndConditionsTextFieldName: string = 'Terms and Conditions Text';
    export const termsOfUseTextFieldId: string = 'c001c0ad-5fce-431f-9f43-4ae101d313c5';
    export const termsOfUseTextFieldName: string = 'Terms of Use Text';
  }
  export namespace GoogleAnalyticsEventRenderingParametersTemplate {
    export const templateId: string = 'bbb59dd1-70e4-492f-a1f9-69e79c67a8bb';
    export const templateName: string = 'Google Analytics Event Rendering Parameters';
    export const eventBodyFieldId: string = '777818fb-0c51-4c70-ad27-aa0b535095a4';
    export const eventBodyFieldName: string = 'Event Body';
    export const eventTypeFieldId: string = '767ad006-3b8f-48a3-86a2-4804f926f34e';
    export const eventTypeFieldName: string = 'Event type';
  }
  export namespace GoToCheckoutButtonDataTemplate {
    export const templateId: string = '044a26bf-d809-45de-81b7-7a12dc38f704';
    export const templateName: string = 'Go To Checkout Button Data';
    export const buttonTextFieldId: string = '95f0adda-ff8e-4423-a7fd-be0b96b2c9f9';
    export const buttonTextFieldName: string = 'ButtonText';
    export const checkoutPageUrlFieldId: string = '1eb46e9d-e9ad-492a-be15-60bd2cbb966c';
    export const checkoutPageUrlFieldName: string = 'Checkout Page Url';
    export const localCurrencyFieldId: string = 'c91c4565-4228-48f1-85e8-e45881546755';
    export const localCurrencyFieldName: string = 'LocalCurrency';
  }
  export namespace LivePersonChatRenderingParametersTemplate {
    export const templateId: string = 'f0a59d6a-7756-4b26-9f10-ab04f249fe64';
    export const templateName: string = 'Live Person Chat Rendering Parameters';
    export const chatScriptBodyFieldId: string = '87521e4e-1558-49ee-b586-7a31c77a094a';
    export const chatScriptBodyFieldName: string = 'Chat Script Body';
    export const livePersonIdFieldId: string = 'ce39f825-8603-4b2f-a3a8-a169271899ed';
    export const livePersonIdFieldName: string = 'livePersonId';
  }
  export namespace LocalChaptersSearchParametersTemplateTemplate {
    export const templateId: string = '7964d8c4-68c1-4a5f-939c-88f9bec7a2fe';
    export const templateName: string = 'Local Chapters Search Parameters Template';
    export const distanceFieldId: string = 'd292e9f1-9f0d-47ac-ae64-d5fc56c03f84';
    export const distanceFieldName: string = 'distance';
    export const maxResultFieldId: string = 'cf0dee42-013b-4ed7-96ac-b410087e5601';
    export const maxResultFieldName: string = 'maxResult';
  }
  export namespace MembershipAutomaticRenewalDataTemplate {
    export const templateId: string = '9a9f20c4-fbe4-49b2-be4d-dbe6d5cc488e';
    export const templateName: string = 'Membership Automatic Renewal Data';
    export const headerTextFieldId: string = '1972ef95-6f4e-4f04-9b59-90bad6146a04';
    export const headerTextFieldName: string = 'Header Text';
    export const textFieldId: string = 'eae2ea93-e07b-44a1-9da1-0115853e64ec';
    export const textFieldName: string = 'Text';
  }
  export namespace MembershipBenefitsCardDataFolderTemplate {
    export const templateId: string = '245408cf-86ec-4da7-b650-6b9891f57af8';
    export const templateName: string = 'MembershipBenefitsCardDataFolder';
  }
  export namespace MembershipBenefitsCardDataTemplate {
    export const templateId: string = '00ec4c3e-d5e6-40af-8694-ff54bf79497f';
    export const templateName: string = 'MembershipBenefitsCardData';
    export const headingTextFieldId: string = '0c49a53d-a6d9-4328-a248-7ddf06e9196c';
    export const headingTextFieldName: string = 'HeadingText';
    export const linkFieldId: string = 'bf3885d9-6fa5-446a-b448-7737bb87f7d0';
    export const linkFieldName: string = 'Link';
    export const textFieldId: string = '8f7e882b-46a7-4679-bd75-c5a2a360d541';
    export const textFieldName: string = 'Text';
  }
  export namespace MembershipBenefitsCarouselDataTemplate {
    export const templateId: string = '2e147a6d-4c76-4ccb-beea-3678613a39ad';
    export const templateName: string = 'MembershipBenefitsCarouselData';
    export const cardsFieldId: string = '7aefd21f-244e-4309-af4d-ee7a723b554a';
    export const cardsFieldName: string = 'Cards';
  }
  export namespace MembershipCongratulationsDataTemplate {
    export const templateId: string = 'be8bd67a-67ad-46f4-a15f-f7a54e483b02';
    export const templateName: string = 'MembershipCongratulationsData';
    export const giftMembershipDescriptionFieldId: string = '23d32d26-c14f-4ce6-8d8c-646d586c643f';
    export const giftMembershipDescriptionFieldName: string = 'GiftMembershipDescription';
    export const giftMembershipTitleFieldId: string = 'ca906d6a-304e-4cc8-ab35-5d40ecdd6e65';
    export const giftMembershipTitleFieldName: string = 'GiftMembershipTitle';
    export const membershipDescriptionFieldId: string = '94eb25c6-3b48-48a9-8f56-3210c0641f53';
    export const membershipDescriptionFieldName: string = 'MembershipDescription';
    export const purchaseTermsWithAutoRenewalStatementFieldId: string = '8f90f0de-a2c9-44dc-9f7f-03c7e9f08747';
    export const purchaseTermsWithAutoRenewalStatementFieldName: string = 'PurchaseTermsWithAutoRenewalStatement';
    export const purchaseTermsWithoutAutoRenewStatementFieldId: string = 'd1818cf7-ca50-4939-a1a5-c0fa99f63738';
    export const purchaseTermsWithoutAutoRenewStatementFieldName: string = 'PurchaseTermsWithoutAutoRenewStatement';
    export const studentMembershipDescriptionFieldId: string = 'f157ccff-51ce-4b0b-abae-81e4f3808489';
    export const studentMembershipDescriptionFieldName: string = 'StudentMembershipDescription';
    export const titleFieldId: string = 'dd56682d-b4cb-45ac-8f4d-f4788fb46caa';
    export const titleFieldName: string = 'Title';
  }
  export namespace MembershipPromoModalDataTemplate {
    export const templateId: string = '1179143f-e6b1-41d0-9fda-88876e4e9772';
    export const templateName: string = 'Membership Promo Modal Data';
    export const addToCartButtonTextFieldId: string = '290afceb-a251-4ede-a265-d2e8b8cc3ce6';
    export const addToCartButtonTextFieldName: string = 'Add to Cart Button Text';
    export const cancelButtonTextFieldId: string = '6f8b11e1-7139-4474-b32d-82a031695313';
    export const cancelButtonTextFieldName: string = 'Cancel Button Text';
    export const listItemsFieldId: string = '77eb4005-57ef-4c83-9b06-f099e4f4d33f';
    export const listItemsFieldName: string = 'List Items';
    export const membershipItemsFieldId: string = '1ced9246-fffe-41b9-a6a9-199728b650ae';
    export const membershipItemsFieldName: string = 'Membership Items';
    export const optionsLinkFieldId: string = '1b02c3ed-047e-4730-8358-e9267bd7b7ad';
    export const optionsLinkFieldName: string = 'Options Link';
    export const studentMembershipValidationTextFieldId: string = 'ca6a915f-f89d-4ab6-abde-0782087a6c95';
    export const studentMembershipValidationTextFieldName: string = 'Student Membership Validation Text';
    export const titleFieldId: string = '19a6235e-6b03-4d65-826a-64c914515654';
    export const titleFieldName: string = 'Title';
    export const totalSavingsTextFieldId: string = 'cc7ea172-aac6-4129-aeda-2a655c5e16b7';
    export const totalSavingsTextFieldName: string = 'Total Savings Text';
    export const sheerIDParametersFieldId: string = '456e7905-9148-411e-8c0a-6e2c9668f0be';
    export const sheerIDParametersFieldName: string = 'SheerIDParameters';
  }
  export namespace MembershipTermsAndConditionsDataTemplate {
    export const templateId: string = 'cd5f92a9-4830-452a-b41c-54b6a41874c0';
    export const templateName: string = 'Membership Terms And Conditions Data';
    export const agreeTextFieldId: string = 'eba2367c-55e9-4c52-9d00-8805afa48081';
    export const agreeTextFieldName: string = 'Agree Text';
    export const optionalAgreeTextFieldId: string = 'd2e43e1b-1c7a-420e-b0d9-3acd4fef758e';
    export const optionalAgreeTextFieldName: string = 'Optional Agree Text';
    export const retireeTextFieldId: string = '4674e971-12f9-46a5-9d88-702638fa817f';
    export const retireeTextFieldName: string = 'Retiree Text';
    export const titleFieldId: string = 'be820b1b-148d-490c-a078-8823a81e2ea7';
    export const titleFieldName: string = 'Title';
  }
  export namespace MembershipUpsellDataTemplate {
    export const templateId: string = '92d970b3-cb54-4608-b7d8-08c865e175e4';
    export const templateName: string = 'MembershipUpsellData';
    export const actionTextFieldId: string = '5916c534-d5ee-4e41-8b53-840f6ed923c2';
    export const actionTextFieldName: string = 'ActionText';
    export const bodyFieldId: string = 'ec9ec963-7183-487d-9d3f-654b8079a7e8';
    export const bodyFieldName: string = 'Body';
    export const bodyNoDiscountFieldId: string = '037dd51f-1177-47fd-a1bf-a052f44c35c5';
    export const bodyNoDiscountFieldName: string = 'BodyNoDiscount';
    export const titleFieldId: string = 'd6bd48a1-9037-42b2-b605-77850e80c449';
    export const titleFieldName: string = 'Title';
    export const titleNoDiscountFieldId: string = 'cf9e7883-25f7-4b16-b5bc-609917064e15';
    export const titleNoDiscountFieldName: string = 'TitleNoDiscount';
  }
  export namespace MembershipUpsellNewDataTemplate {
    export const templateId: string = '49e657d1-4651-4bb9-80ac-21b9a875dc5c';
    export const templateName: string = 'MembershipUpsellNewData';
    export const actionTextFieldId: string = '028e82a8-ed4c-491b-be92-c3926d3e290d';
    export const actionTextFieldName: string = 'ActionText';
    export const bodyFieldId: string = '913cbb9b-2bae-49cd-bd25-b03ef8aaf485';
    export const bodyFieldName: string = 'Body';
    export const bodyNoDiscountFieldId: string = '27874113-5714-4d5b-b4c7-78938273845f';
    export const bodyNoDiscountFieldName: string = 'BodyNoDiscount';
  }
  export namespace OrderConfirmationSummaryDataTemplate {
    export const templateId: string = '2441d74e-3f66-412b-b0be-89e7e24ff7ba';
    export const templateName: string = 'Order Confirmation Summary Data';
    export const headerFieldId: string = 'c8344e1f-4414-4372-b9ca-6af6b4ae693e';
    export const headerFieldName: string = 'Header';
    export const orderDiscountFieldId: string = 'acf10980-1bba-4bfb-9b91-add3a79fbfd4';
    export const orderDiscountFieldName: string = 'Order discount';
    export const orderTotalFieldId: string = '54d0c72b-29c0-4fff-8f1c-bcd1a6f1a2f8';
    export const orderTotalFieldName: string = 'Order total';
    export const quoteHeaderFieldId: string = '54c7d12b-ff1a-4d8f-bcc4-0aa76ddda3f1';
    export const quoteHeaderFieldName: string = 'Quote Header';
    export const subtotalFieldId: string = '79a7c00a-4d7b-4a7b-8efd-266e279f6a12';
    export const subtotalFieldName: string = 'Subtotal';
    export const taxesFieldId: string = '499c31bc-afd4-498e-b344-fef9a2c9adfd';
    export const taxesFieldName: string = 'Taxes';
    export const voucherDiscountFieldId: string = '1c36ce4c-c4c6-4abc-bfa2-69e21458005c';
    export const voucherDiscountFieldName: string = 'Voucher discount';
  }
  export namespace OrderReviewPaymentFolderTemplate {
    export const templateId: string = '12ef9d96-959a-4210-b79d-7417fdf8a974';
    export const templateName: string = 'Order Review Payment Folder';
  }
  export namespace OrderReviewPaymentTemplate {
    export const templateId: string = 'e353f941-9075-4120-9d15-8449c6e3312f';
    export const templateName: string = 'Order Review Payment';
    export const billingAddressFieldId: string = 'b48b0aba-0b7e-4749-8036-b0a64506a881';
    export const billingAddressFieldName: string = 'BillingAddress';
    export const paymentMethodsFieldId: string = '3194781b-bddc-498a-a453-fc8deb7485f4';
    export const paymentMethodsFieldName: string = 'PaymentMethods';
    export const purchaseOrderFieldId: string = '74d82428-4d66-4fdc-949b-13f5e7a34086';
    export const purchaseOrderFieldName: string = 'PurchaseOrder';
    export const voucherAsPaymentFieldId: string = '41179a35-3ee4-43f8-bd74-862bbf080d12';
    export const voucherAsPaymentFieldName: string = 'VoucherAsPayment';
  }
  export namespace OrderSummaryDataTemplate {
    export const templateId: string = '85d5d7a4-84d7-485e-a0f1-bc8eb8ee84fe';
    export const templateName: string = 'Order Summary Data';
    export const checkoutButtonTextFieldId: string = 'd71cbad5-2157-42a3-a64c-f48825fccce2';
    export const checkoutButtonTextFieldName: string = 'Checkout Button Text';
    export const estimatedTaxFieldId: string = '017d8701-3629-4604-8dfd-9413561546ea';
    export const estimatedTaxFieldName: string = 'Estimated Tax';
    export const estimatedTotalFieldId: string = 'd96e6f00-c16f-465f-b41a-cae94fd161c6';
    export const estimatedTotalFieldName: string = 'Estimated Total';
    export const goToCheckoutUrlFieldId: string = 'bcf8be2a-89c3-481b-95ae-84855f29b936';
    export const goToCheckoutUrlFieldName: string = 'Go To Checkout Url';
    export const goToCartLinkFieldId: string = 'bc15aaca-d136-4d97-a1e8-86dccc4b72fc';
    export const goToCartLinkFieldName: string = 'GoToCartLink';
    export const orderDiscountFieldId: string = 'e6520a5c-23c8-41be-ad6d-69a6efca5b62';
    export const orderDiscountFieldName: string = 'Order Discount';
    export const subtotalFieldId: string = '81ab94dc-acde-43d5-891a-b456ece203cc';
    export const subtotalFieldName: string = 'Subtotal';
    export const titleFieldId: string = '0355c5c6-8ee6-4326-abae-68b3d1c1c93c';
    export const titleFieldName: string = 'Title';
    export const totalCartDiscountFieldId: string = 'fc5cab7b-61bf-4590-9793-074e3a155e86';
    export const totalCartDiscountFieldName: string = 'Total Cart Discount';
    export const voucherOrderDiscountFieldId: string = '80f752fb-6e85-4fae-8907-eaa9b92412b5';
    export const voucherOrderDiscountFieldName: string = 'Voucher Order Discount';
  }
  export namespace OrderSummaryMobileAccordionDataTemplate {
    export const templateId: string = '1d42e4e8-ae13-4ab3-bfdc-886b60a5c7bf';
    export const templateName: string = 'Order Summary Mobile Accordion Data';
    export const hideButtonTitleFieldId: string = 'd6f85730-e614-4197-bf5e-51618d35eb13';
    export const hideButtonTitleFieldName: string = 'Hide Button Title';
    export const showButtonTitleFieldId: string = 'a323f0da-5f46-4df4-99b6-606af70ceaa2';
    export const showButtonTitleFieldName: string = 'Show Button Title';
  }
  export namespace OrderSummaryMobileRenderingParametersTemplate {
    export const templateId: string = 'ff9ee7cc-f2d8-4542-b012-faa347be9e13';
    export const templateName: string = 'Order Summary Mobile Rendering Parameters';
    export const headerTextFieldId: string = 'c3e40bf0-28bb-4139-96a7-c3c3277cc810';
    export const headerTextFieldName: string = 'Header Text';
    export const showButtonFieldId: string = '65ddbc3a-f5b0-42c1-a521-8de3d1f66d44';
    export const showButtonFieldName: string = 'Show Button';
  }
  export namespace OrderSummaryRenderingParametersTemplate {
    export const templateId: string = 'fec63381-1ec9-43a3-8e41-7379331ad16d';
    export const templateName: string = 'OrderSummaryRenderingParameters';
    export const displayCartLinesFieldId: string = '5b124922-8c2e-4cd5-b484-62301ffee276';
    export const displayCartLinesFieldName: string = 'DisplayCartLines';
    export const hideGoToCheckoutButtonFieldId: string = '29894129-cf60-4c37-af5f-7513e519a311';
    export const hideGoToCheckoutButtonFieldName: string = 'HideGoToCheckoutButton';
  }
  export namespace PageLevelLoadingModalDataTemplate {
    export const templateId: string = '99c2fe4a-e06e-4387-94d4-a7cd9184fadb';
    export const templateName: string = 'PageLevelLoadingModalData';
    export const messageFieldId: string = '5edd740d-2ecb-4a0b-849a-916305b11548';
    export const messageFieldName: string = 'Message';
    export const titleFieldId: string = '2c912f1c-be1b-4eef-8fdb-8419c7022a1a';
    export const titleFieldName: string = 'Title';
  }
  export namespace PaymentAccordionDataTemplate {
    export const templateId: string = '5770faed-ef53-4c93-961a-b4e2c599031d';
    export const templateName: string = 'PaymentAccordionData';
    export const billingExpandLinkTextFieldId: string = 'c4bf2f82-39cb-49e1-be18-ebfd5690ef23';
    export const billingExpandLinkTextFieldName: string = 'BillingExpandLinkText';
    export const billingHeadingTextFieldId: string = 'e6bdb463-1bf1-4b7a-83f4-5f012db01011';
    export const billingHeadingTextFieldName: string = 'BillingHeadingText';
    export const contactEmailTooltipTextFieldId: string = '1002bea8-0920-4c8b-9e32-a2326754b3e8';
    export const contactEmailTooltipTextFieldName: string = 'ContactEmailTooltipText';
    export const contactExpandLinkTextFieldId: string = 'b66a6ea3-43c3-413d-a6de-2af427073489';
    export const contactExpandLinkTextFieldName: string = 'ContactExpandLinkText';
    export const contactHeadingTextFieldId: string = 'c7c3d82d-866a-4f6d-81e4-f3be1e3054c3';
    export const contactHeadingTextFieldName: string = 'ContactHeadingText';
    export const contactPhoneToolTipTextFieldId: string = '8218bc0c-e875-4bfa-9847-f06f1e2f1c58';
    export const contactPhoneToolTipTextFieldName: string = 'ContactPhoneToolTipText';
    export const paymentMethodExpandLinkTextFieldId: string = '90eb0cfb-4dc9-454c-9db9-2a08b5082209';
    export const paymentMethodExpandLinkTextFieldName: string = 'PaymentMethodExpandLinkText';
    export const paymentMethodHeadingTextFieldId: string = 'cd5f5642-9100-451e-8c7d-79cd531d0d63';
    export const paymentMethodHeadingTextFieldName: string = 'PaymentMethodHeadingText';
    export const paymentSecureInformationTextFieldId: string = '309dd2d3-602d-4eda-9624-d80b6b805509';
    export const paymentSecureInformationTextFieldName: string = 'PaymentSecureInformationText';
  }
  export namespace PaymentAccordionRenderingParametersTemplate {
    export const templateId: string = '44454d74-556d-4f94-87a9-143a175292b6';
    export const templateName: string = 'PaymentAccordionRenderingParameters';
    export const hidePaymentSectionFieldId: string = 'f2321705-637a-44f7-b962-ab8f2356d0c3';
    export const hidePaymentSectionFieldName: string = 'HidePaymentSection';
    export const isQuotePageFieldId: string = '5eeefd54-5c7a-4dee-a383-65f02a0fb110';
    export const isQuotePageFieldName: string = 'IsQuotePage';
  }
  export namespace PaymentAddressFolderTemplate {
    export const templateId: string = '934b7ee8-441d-406d-89bd-00a423eada7c';
    export const templateName: string = 'PaymentAddressFolder';
  }
  export namespace PaymentAddressTemplate {
    export const templateId: string = '9d969a4a-e75d-4237-aade-3372d3288ec7';
    export const templateName: string = 'PaymentAddress';
    export const addressLabelFieldId: string = 'c7723a57-de99-4aec-b8ae-53c74f09e967';
    export const addressLabelFieldName: string = 'Address Label';
  }
  export namespace PaymentBillingDataTemplate {
    export const templateId: string = '04b1e545-2d1a-480a-9e44-8f3dafb657e7';
    export const templateName: string = 'PaymentBillingData';
    export const addAddressButtonTextFieldId: string = '501749fb-a955-45a6-a18b-9480b5de397e';
    export const addAddressButtonTextFieldName: string = 'AddAddressButtonText';
    export const editButtonTextFieldId: string = '875ccc00-e88a-4004-ac01-78e04b3c2f89';
    export const editButtonTextFieldName: string = 'EditButtonText';
    export const headingTextFieldId: string = '09c4c50d-f38a-4a54-bd68-c8cde60934ee';
    export const headingTextFieldName: string = 'HeadingText';
    export const useAddressButtonTextFieldId: string = 'd5c104a3-a9e0-4323-a913-41af0be987e9';
    export const useAddressButtonTextFieldName: string = 'UseAddressButtonText';
    export const addressLine1labelFieldId: string = '4866b101-d404-45cd-a9bf-a6a5726b0d1a';
    export const addressLine1labelFieldName: string = 'AddressLine1Label';
    export const addressLine1placeholderFieldId: string = '41655a0e-1b74-41ad-b83a-e3adec1e3020';
    export const addressLine1placeholderFieldName: string = 'AddressLine1Placeholder';
    export const addressLine2labelFieldId: string = 'd1e3eaca-6ee5-4767-b5f5-71c82084559c';
    export const addressLine2labelFieldName: string = 'AddressLine2Label';
    export const addressLine2placeholderFieldId: string = 'eededc02-5e37-43c7-8a3f-de9e01850fb9';
    export const addressLine2placeholderFieldName: string = 'AddressLine2Placeholder';
    export const cancelButtonTextFieldId: string = '23dd9bb5-4766-4cf5-9345-f6b32fbda163';
    export const cancelButtonTextFieldName: string = 'CancelButtonText';
    export const cityLabelFieldId: string = '5a6c302f-3ae6-4c20-9d10-eb8b09ad0c3c';
    export const cityLabelFieldName: string = 'CityLabel';
    export const cityPlaceholderFieldId: string = '4849055a-3090-4d8a-abf4-0c02c39d0468';
    export const cityPlaceholderFieldName: string = 'CityPlaceholder';
    export const countryLabelFieldId: string = 'ad0da3ad-3305-4c24-b55e-d41bd0d6e408';
    export const countryLabelFieldName: string = 'CountryLabel';
    export const countryPlaceholderFieldId: string = '23e8d7d4-2fdb-45ea-805c-c384f31c762c';
    export const countryPlaceholderFieldName: string = 'CountryPlaceholder';
    export const loadingTextFieldId: string = 'd3216b2a-bed6-49d2-81ff-f58887d064c5';
    export const loadingTextFieldName: string = 'LoadingText';
    export const postalCodeLabelFieldId: string = '7d1b469a-f65d-46cc-b5f3-b956bd7ea82b';
    export const postalCodeLabelFieldName: string = 'PostalCodeLabel';
    export const postalCodePlaceholderFieldId: string = 'fa3e47e7-bebc-49f9-b54e-4a90c26db27b';
    export const postalCodePlaceholderFieldName: string = 'PostalCodePlaceholder';
    export const saveAsPrimaryLabelFieldId: string = '766391f2-b7a4-4435-ace0-aa33f640d87c';
    export const saveAsPrimaryLabelFieldName: string = 'SaveAsPrimaryLabel';
    export const stateProvinceLabelFieldId: string = '531dc32c-37be-4bf9-bfa0-589e0ea41f10';
    export const stateProvinceLabelFieldName: string = 'StateProvinceLabel';
    export const stateProvincePlaceholderFieldId: string = '9e9305fe-9f58-48b9-a5a5-5ab47ebb5b57';
    export const stateProvincePlaceholderFieldName: string = 'StateProvincePlaceholder';
    export const submitButtonTextFieldId: string = '86d5965c-1e16-4d37-ba4f-57c3bb990830';
    export const submitButtonTextFieldName: string = 'SubmitButtonText';
    export const addressLine1emptyFieldId: string = '75a03b59-6a8c-4b50-8aec-fc8671a77e04';
    export const addressLine1emptyFieldName: string = 'AddressLine1Empty';
    export const cityEmptyFieldId: string = '7a952589-6785-48d7-bc0d-14ab04593ec4';
    export const cityEmptyFieldName: string = 'CityEmpty';
    export const countryEmptyFieldId: string = '486e4247-0f57-4ebe-a6a2-f52c9ef3ccce';
    export const countryEmptyFieldName: string = 'CountryEmpty';
    export const postalCodeEmptyFieldId: string = '30f97923-9953-48a7-bbdf-481386cabd9a';
    export const postalCodeEmptyFieldName: string = 'PostalCodeEmpty';
    export const requiredFieldsMessageFieldId: string = '9e8be739-b6af-45dc-ae9d-fd9673d26ca3';
    export const requiredFieldsMessageFieldName: string = 'RequiredFieldsMessage';
    export const stateProvinceEmptyFieldId: string = 'bdaf1b2a-e492-4d0e-84a2-ebdf7f9d211b';
    export const stateProvinceEmptyFieldName: string = 'StateProvinceEmpty';
    export const autoSaveTextFieldId: string = 'd4c5b41d-5842-4d3c-a6f1-26b6d94caa99';
    export const autoSaveTextFieldName: string = 'AutoSaveText';
  }
  export namespace PaymentContactDataTemplate {
    export const templateId: string = '34c0f7db-6a35-4e1d-8f99-aa99cd0bb93d';
    export const templateName: string = 'Payment Contact Data';
    export const dialingCodeLabelFieldId: string = '25c18c93-e965-4f7b-b249-4e1dce93c813';
    export const dialingCodeLabelFieldName: string = 'Dialing Code Label';
    export const emailFieldLabelFieldId: string = '56593a34-7faa-48e0-bd54-93bbd4f83ddd';
    export const emailFieldLabelFieldName: string = 'email-field-label';
    export const emailFieldPlaceholderTextFieldId: string = '434e70cf-3d5a-4b70-964a-e00c03fe1f0a';
    export const emailFieldPlaceholderTextFieldName: string = 'EmailFieldPlaceholderText';
    export const nameChangeDisclaimerFieldId: string = 'd4376d7f-14f3-4a21-a15d-4d98e3cce9b8';
    export const nameChangeDisclaimerFieldName: string = 'name-change-disclaimer';
    export const phoneCharacterLimitMessageFieldId: string = '3cb45669-be04-4669-a538-0a7642ad1a83';
    export const phoneCharacterLimitMessageFieldName: string = 'Phone Character Limit Message';
    export const phoneNumberLabelFieldId: string = 'e45344ea-fe19-4df8-8b0c-6736823af875';
    export const phoneNumberLabelFieldName: string = 'Phone Number Label';
    export const phoneNumberPlaceholderTextFieldId: string = 'a66fe0c5-9962-49cd-adc9-909969ae0024';
    export const phoneNumberPlaceholderTextFieldName: string = 'Phone Number Placeholder Text';
    export const phoneSectionLabelTextFieldId: string = '56886598-9698-4bfa-9fff-bd338555a106';
    export const phoneSectionLabelTextFieldName: string = 'Phone Section Label Text';
    export const submitButtonTextFieldId: string = '6c301623-81a6-4f48-9f3b-f1f4d5ec4ae6';
    export const submitButtonTextFieldName: string = 'submit-button-text';
    export const emailAddressToolTipMessageFieldId: string = '9e3bb5c3-24fe-4686-8ca7-622a55d15d4b';
    export const emailAddressToolTipMessageFieldName: string = 'EmailAddressToolTipMessage';
    export const phoneNumberToolTipMessageFieldId: string = '5830762b-e31f-4cc9-a83c-4ecd90dedb69';
    export const phoneNumberToolTipMessageFieldName: string = 'PhoneNumberToolTipMessage';
    export const dialingCountryCodeRequiredMessageFieldId: string = '6dad62e1-7f4b-421b-a633-9811f1b5831f';
    export const dialingCountryCodeRequiredMessageFieldName: string = 'DialingCountryCodeRequiredMessage';
    export const emailInvalidMessageFieldId: string = '7ba732ec-e6ac-4fa3-b5e5-cfb65ec22da3';
    export const emailInvalidMessageFieldName: string = 'EmailInvalidMessage';
    export const emailRequiredMessageFieldId: string = '8712ca1c-09b1-47a7-8474-f678cc8a679f';
    export const emailRequiredMessageFieldName: string = 'EmailRequiredMessage';
    export const phoneRequiredMessageFieldId: string = '2c106d5d-82bf-435a-90b2-3babdb847df3';
    export const phoneRequiredMessageFieldName: string = 'PhoneRequiredMessage';
    export const phoneValidMessageFieldId: string = 'bada59f7-21f0-4e28-8c3f-2c0fd6837ced';
    export const phoneValidMessageFieldName: string = 'PhoneValidMessage';
  }
  export namespace PaymentEmailMainDataTemplate {
    export const templateId: string = '3a2a7965-7384-4412-a91f-2032b2200a87';
    export const templateName: string = 'Payment Email Main Data';
    export const componentHeaderFieldId: string = '76435e9e-6389-4d60-9417-4fe696368b8d';
    export const componentHeaderFieldName: string = 'Component Header';
  }
  export namespace PaymentMethodAdyenDropinDataTemplate {
    export const templateId: string = 'a4cf769a-72a5-4024-b028-b1f3e0fe28fc';
    export const templateName: string = 'Payment Method Adyen Dropin Data';
    export const membershipAndSubscriptionAutoRenewMessageFieldId: string = 'beab2724-4aa1-41b4-a27e-584aec2704b2';
    export const membershipAndSubscriptionAutoRenewMessageFieldName: string = 'MembershipAndSubscriptionAutoRenewMessage';
    export const membershipAutoRenewMessageFieldId: string = '4f99e786-5ca0-41cb-862e-a8e9b75ea8a8';
    export const membershipAutoRenewMessageFieldName: string = 'MembershipAutoRenewMessage';
    export const subscriptionAutoRenewMessageFieldId: string = '68e47ac8-0a05-4ed3-97c6-c1484f93c691';
    export const subscriptionAutoRenewMessageFieldName: string = 'SubscriptionAutoRenewMessage';
    export const pageLoaderBodyFieldId: string = '639e7940-918e-4593-9c87-7772698b2c9b';
    export const pageLoaderBodyFieldName: string = 'Page Loader Body';
    export const pageLoaderTitleFieldId: string = 'f177716d-c9c5-41c1-9cc1-ed76e2719c59';
    export const pageLoaderTitleFieldName: string = 'Page Loader Title';
    export const allowedPaymentMethodsMappingFieldId: string = '4139a6eb-368f-4fc9-8569-bab309b2654f';
    export const allowedPaymentMethodsMappingFieldName: string = 'AllowedPaymentMethodsMapping';
    export const settingsFieldId: string = 'd2cd81a7-bdfd-46eb-a780-1bc03a00219a';
    export const settingsFieldName: string = 'Settings';
    export const translationOverridesFieldId: string = 'e6403750-bf79-4b9f-bfec-56b25569868e';
    export const translationOverridesFieldName: string = 'TranslationOverrides';
  }
  export namespace PaymentMethodAdyenPayBySecureLinkDataTemplate {
    export const templateId: string = '9e33af76-0e15-4f50-9917-b3c9eda84954';
    export const templateName: string = 'Payment Method Adyen Pay by Secure Link Data';
    export const descriptionFieldId: string = '04210b7b-97c7-474a-b96e-36ca670d4dcf';
    export const descriptionFieldName: string = 'Description';
    export const cancelButtonTextFieldId: string = 'f198938f-f938-4cbb-a56a-842a1b7c770b';
    export const cancelButtonTextFieldName: string = 'Cancel Button Text';
    export const submitButtonTextFieldId: string = '2824a5b7-4bc2-40b4-a592-9d408e30143b';
    export const submitButtonTextFieldName: string = 'Submit Button Text';
    export const autoSaveTextFieldId: string = 'd4c5b41d-5842-4d3c-a6f1-26b6d94caa99';
    export const autoSaveTextFieldName: string = 'AutoSaveText';
  }
  export namespace PaymentMethodAlipayDataTemplate {
    export const templateId: string = 'cae07e06-aaab-4081-8bb4-9890091fa9d1';
    export const templateName: string = 'Payment Method Alipay Data';
    export const redirectInformationMessageFieldId: string = '6aef76ce-873c-48b9-a176-a7ec4356e235';
    export const redirectInformationMessageFieldName: string = 'RedirectInformationMessage';
    export const descriptionFieldId: string = '04210b7b-97c7-474a-b96e-36ca670d4dcf';
    export const descriptionFieldName: string = 'Description';
    export const cancelButtonTextFieldId: string = 'f198938f-f938-4cbb-a56a-842a1b7c770b';
    export const cancelButtonTextFieldName: string = 'Cancel Button Text';
    export const submitButtonTextFieldId: string = '2824a5b7-4bc2-40b4-a592-9d408e30143b';
    export const submitButtonTextFieldName: string = 'Submit Button Text';
    export const autoSaveTextFieldId: string = 'd4c5b41d-5842-4d3c-a6f1-26b6d94caa99';
    export const autoSaveTextFieldName: string = 'AutoSaveText';
  }
  export namespace PaymentMethodDataTemplate {
    export const templateId: string = '94f9771e-b024-4dd7-9d21-0c367c971e23';
    export const templateName: string = 'PaymentMethodData';
    export const descriptionFieldId: string = '04210b7b-97c7-474a-b96e-36ca670d4dcf';
    export const descriptionFieldName: string = 'Description';
    export const cancelButtonTextFieldId: string = 'f198938f-f938-4cbb-a56a-842a1b7c770b';
    export const cancelButtonTextFieldName: string = 'Cancel Button Text';
    export const submitButtonTextFieldId: string = '2824a5b7-4bc2-40b4-a592-9d408e30143b';
    export const submitButtonTextFieldName: string = 'Submit Button Text';
    export const autoSaveTextFieldId: string = 'd4c5b41d-5842-4d3c-a6f1-26b6d94caa99';
    export const autoSaveTextFieldName: string = 'AutoSaveText';
  }
  export namespace PaymentMethodPaypalDataTemplate {
    export const templateId: string = 'cbab6a60-78e5-47ce-b253-7109b9533a1a';
    export const templateName: string = 'PaymentMethodPaypalData';
    export const redirectInformationMessageFieldId: string = '782f47b8-108b-48d7-a75e-2188a18e2d3a';
    export const redirectInformationMessageFieldName: string = 'RedirectInformationMessage';
    export const savePaymentDescriptionFieldId: string = '20214907-45b9-4abc-b9a7-c7d5517d0205';
    export const savePaymentDescriptionFieldName: string = 'SavePaymentDescription';
    export const savePaymentSubTitleFieldId: string = '4409e088-ef0c-4690-9767-cb18be2e38c5';
    export const savePaymentSubTitleFieldName: string = 'SavePaymentSubTitle';
    export const descriptionFieldId: string = '04210b7b-97c7-474a-b96e-36ca670d4dcf';
    export const descriptionFieldName: string = 'Description';
    export const cancelButtonTextFieldId: string = 'f198938f-f938-4cbb-a56a-842a1b7c770b';
    export const cancelButtonTextFieldName: string = 'Cancel Button Text';
    export const submitButtonTextFieldId: string = '2824a5b7-4bc2-40b4-a592-9d408e30143b';
    export const submitButtonTextFieldName: string = 'Submit Button Text';
    export const autoSaveTextFieldId: string = 'd4c5b41d-5842-4d3c-a6f1-26b6d94caa99';
    export const autoSaveTextFieldName: string = 'AutoSaveText';
  }
  export namespace PaymentMethodRazorpayDataTemplate {
    export const templateId: string = '610e0589-93d8-4c8e-a2c6-07f557058746';
    export const templateName: string = 'Payment Method Razorpay Data';
    export const creditCardLabelFieldId: string = 'c2b1437e-2371-4d2f-8bdd-6319d6aa20a2';
    export const creditCardLabelFieldName: string = 'CreditCardLabel';
    export const creditCardSubtextFieldId: string = 'd6c35601-1201-4828-ac43-c8c4ba3dc380';
    export const creditCardSubtextFieldName: string = 'CreditCardSubtext';
    export const membershipAndSubscriptionAutoRenewMessageFieldId: string = '9f3f2185-13c7-4941-8a78-2067cfdd1720';
    export const membershipAndSubscriptionAutoRenewMessageFieldName: string = 'MembershipAndSubscriptionAutoRenewMessage';
    export const netbankingLabelFieldId: string = 'c37d78ca-21f8-4bea-a74c-57cf8ed7a588';
    export const netbankingLabelFieldName: string = 'NetbankingLabel';
    export const netbankingSubtextFieldId: string = 'e7662200-52aa-4554-86da-3e15c6c78ee8';
    export const netbankingSubtextFieldName: string = 'NetbankingSubtext';
    export const upiLabelFieldId: string = 'f59678a9-3a7c-4c27-b9e4-4d43e81f6d8a';
    export const upiLabelFieldName: string = 'UpiLabel';
    export const upiLimitAmountFieldId: string = '5560e6c3-6143-4bb7-be63-aa54250605f6';
    export const upiLimitAmountFieldName: string = 'UPILimitAmount';
    export const upiSubtextFieldId: string = '4d3a8376-12de-4f63-9015-2af1536b5778';
    export const upiSubtextFieldName: string = 'UpiSubtext';
  }
  export namespace PaymentMethodRazorpayPayBySecureLinkDataTemplate {
    export const templateId: string = '2ea5a2a6-131e-43cd-9c7e-b307d421abaf';
    export const templateName: string = 'Payment Method Razorpay Pay by Secure Link Data';
    export const descriptionFieldId: string = '04210b7b-97c7-474a-b96e-36ca670d4dcf';
    export const descriptionFieldName: string = 'Description';
    export const cancelButtonTextFieldId: string = 'f198938f-f938-4cbb-a56a-842a1b7c770b';
    export const cancelButtonTextFieldName: string = 'Cancel Button Text';
    export const submitButtonTextFieldId: string = '2824a5b7-4bc2-40b4-a592-9d408e30143b';
    export const submitButtonTextFieldName: string = 'Submit Button Text';
    export const autoSaveTextFieldId: string = 'd4c5b41d-5842-4d3c-a6f1-26b6d94caa99';
    export const autoSaveTextFieldName: string = 'AutoSaveText';
  }
  export namespace PaymentMethodWorldpayDataTemplate {
    export const templateId: string = '58621709-4f02-4e32-9b0d-69cb289015bb';
    export const templateName: string = 'PaymentMethodWorldpayData';
    export const cardHolderNameLabelFieldId: string = 'b29af014-647c-4297-8dc7-29c896a95087';
    export const cardHolderNameLabelFieldName: string = 'CardHolderNameLabel';
    export const cardHolderNamePlaceholderFieldId: string = 'b191ae6e-51bd-4180-b049-f0688cacb36b';
    export const cardHolderNamePlaceholderFieldName: string = 'CardHolderNamePlaceholder';
    export const cardNumberLabelFieldId: string = '5395cc4a-54cd-4f02-acb1-8b87126facdc';
    export const cardNumberLabelFieldName: string = 'CardNumberLabel';
    export const cardNumberPlaceholderFieldId: string = '6f82954f-68c3-4763-8f72-c0260d9bce8a';
    export const cardNumberPlaceholderFieldName: string = 'CardNumberPlaceholder';
    export const expirationMonthLabelFieldId: string = '1a2b6fe4-b9c9-47c3-b42a-52dfb45f07cb';
    export const expirationMonthLabelFieldName: string = 'ExpirationMonthLabel';
    export const expirationMonthPlaceholderFieldId: string = '2e8b5b7b-5330-482c-9e8a-fb73b95d370a';
    export const expirationMonthPlaceholderFieldName: string = 'ExpirationMonthPlaceholder';
    export const expirationYearLabelFieldId: string = '2021ca8d-39e8-426f-843a-427d9e5dcdc1';
    export const expirationYearLabelFieldName: string = 'ExpirationYearLabel';
    export const expirationYearPlaceholderFieldId: string = '8176e2d9-e1fb-4eda-b18d-69ee1a4dc14d';
    export const expirationYearPlaceholderFieldName: string = 'ExpirationYearPlaceholder';
    export const saveAsDefaultLabelFieldId: string = '207ba64d-938c-4854-890b-cf8dd87fbe93';
    export const saveAsDefaultLabelFieldName: string = 'SaveAsDefaultLabel';
    export const saveAsDefaultSubTextFieldId: string = 'ebc7aaca-62ec-4006-b652-f7d12cbb9e59';
    export const saveAsDefaultSubTextFieldName: string = 'SaveAsDefaultSubText';
    export const securityCodeLabelFieldId: string = '969a8c3b-4ae6-4d9b-bf8c-841a2a37305f';
    export const securityCodeLabelFieldName: string = 'SecurityCodeLabel';
    export const securityCodePlaceholderFieldId: string = '20f75892-6ea3-4179-b2ac-eca90f57000f';
    export const securityCodePlaceholderFieldName: string = 'SecurityCodePlaceholder';
    export const securityCodeTooltipTextFieldId: string = 'd288099b-604c-48d0-a89b-c7d37b49e4ed';
    export const securityCodeTooltipTextFieldName: string = 'SecurityCodeTooltipText';
    export const cardHolderNameEmptyFieldId: string = 'a3b57088-1ddf-404a-9542-7b4b7005d99d';
    export const cardHolderNameEmptyFieldName: string = 'CardHolderNameEmpty';
    export const cardNumberInvalidFieldId: string = '7decd6c3-0d29-4fe8-878b-ed50e71b74c0';
    export const cardNumberInvalidFieldName: string = 'CardNumberInvalid';
    export const expirationMonthEmptyFieldId: string = 'eb76077d-050e-49f3-a0d0-73729a485977';
    export const expirationMonthEmptyFieldName: string = 'ExpirationMonthEmpty';
    export const expirationMonthYearExpiredFieldId: string = '70f0c073-d728-4db7-958e-f0dfc1f5524f';
    export const expirationMonthYearExpiredFieldName: string = 'ExpirationMonthYearExpired';
    export const expirationYearEmptyFieldId: string = 'ed863d43-2c99-4535-ab41-2fa5845789de';
    export const expirationYearEmptyFieldName: string = 'ExpirationYearEmpty';
    export const securityCodeEmptyFieldId: string = 'debb3ef5-bca7-4282-a84e-8be2fba157fc';
    export const securityCodeEmptyFieldName: string = 'SecurityCodeEmpty';
    export const descriptionFieldId: string = '04210b7b-97c7-474a-b96e-36ca670d4dcf';
    export const descriptionFieldName: string = 'Description';
    export const cancelButtonTextFieldId: string = 'f198938f-f938-4cbb-a56a-842a1b7c770b';
    export const cancelButtonTextFieldName: string = 'Cancel Button Text';
    export const submitButtonTextFieldId: string = '2824a5b7-4bc2-40b4-a592-9d408e30143b';
    export const submitButtonTextFieldName: string = 'Submit Button Text';
    export const autoSaveTextFieldId: string = 'd4c5b41d-5842-4d3c-a6f1-26b6d94caa99';
    export const autoSaveTextFieldName: string = 'AutoSaveText';
  }
  export namespace PaymentMethodWorldpayHostedPaymentTemplate {
    export const templateId: string = 'bac98afe-a2a8-4cee-9e9d-f1bdd1c8b972';
    export const templateName: string = 'PaymentMethodWorldpayHostedPayment';
    export const descriptionFieldId: string = '04210b7b-97c7-474a-b96e-36ca670d4dcf';
    export const descriptionFieldName: string = 'Description';
    export const cancelButtonTextFieldId: string = 'f198938f-f938-4cbb-a56a-842a1b7c770b';
    export const cancelButtonTextFieldName: string = 'Cancel Button Text';
    export const submitButtonTextFieldId: string = '2824a5b7-4bc2-40b4-a592-9d408e30143b';
    export const submitButtonTextFieldName: string = 'Submit Button Text';
    export const autoSaveTextFieldId: string = 'd4c5b41d-5842-4d3c-a6f1-26b6d94caa99';
    export const autoSaveTextFieldName: string = 'AutoSaveText';
  }
  export namespace PayPalPayLaterDataTemplate {
    export const templateId: string = '62a2a28a-1a63-4cef-b356-35d827d46ad6';
    export const templateName: string = 'PayPal Pay Later Data';
    export const adyenSettingFieldId: string = 'f288ba3a-55a8-47d9-a658-3e55f013994c';
    export const adyenSettingFieldName: string = 'AdyenSetting';
  }
  export namespace PayPalPlaceOrderButtonTemplate {
    export const templateId: string = 'd5688d77-4ab0-4ce2-a027-114dc5fcd57a';
    export const templateName: string = 'PayPalPlaceOrderButton';
    export const orderProcessingStateBodyFieldId: string = '5afc11d7-87cc-4fd0-a443-b1bfcfdf3b89';
    export const orderProcessingStateBodyFieldName: string = 'OrderProcessingStateBody';
    export const orderProcessingStateTitleFieldId: string = '004179d4-d4b2-4371-a204-88e9ff75a2b8';
    export const orderProcessingStateTitleFieldName: string = 'OrderProcessingStateTitle';
    export const authorizationCancelledErrorMessageFieldId: string = '3aafc5f0-bc68-45fb-834d-2ddfa9384321';
    export const authorizationCancelledErrorMessageFieldName: string = 'AuthorizationCancelledErrorMessage';
    export const authorizationFailedErrorMessageFieldId: string = '0b84211d-6b81-43a7-9454-1c7f85bce807';
    export const authorizationFailedErrorMessageFieldName: string = 'AuthorizationFailedErrorMessage';
    export const authorizationFallbackErrorMessageFieldId: string = '02aca0a8-aa6d-437e-90f2-5e057b7159f4';
    export const authorizationFallbackErrorMessageFieldName: string = 'AuthorizationFallbackErrorMessage';
    export const redirectWaitingStateBodyFieldId: string = 'edf8b99a-1d26-4577-abaa-33207e7f0e8f';
    export const redirectWaitingStateBodyFieldName: string = 'RedirectWaitingStateBody';
    export const redirectWaitingStateTitleFieldId: string = '552f73b2-f725-4b56-af3a-592d1103054d';
    export const redirectWaitingStateTitleFieldName: string = 'RedirectWaitingStateTitle';
    export const adyenPayBySecureLinkButtonTextFieldId: string = 'e56d408f-340f-4e91-af78-db68bc5b1818';
    export const adyenPayBySecureLinkButtonTextFieldName: string = 'Adyen Pay by Secure Link Button Text';
    export const alipayButtonTextFieldId: string = '78d0c51e-f011-44ae-929a-1327e8f26bfa';
    export const alipayButtonTextFieldName: string = 'Alipay Button Text';
    export const alipayOrderProcessingStateBodyFieldId: string = 'ff1dac78-3599-4587-b410-2f708f608c59';
    export const alipayOrderProcessingStateBodyFieldName: string = 'Alipay Order Processing State Body';
    export const alipayOrderProcessingStateTitleFieldId: string = '690fe3ae-5351-4226-a546-bfb8c0c44b0a';
    export const alipayOrderProcessingStateTitleFieldName: string = 'Alipay Order Processing State Title';
    export const alipayRedirectWaitingStateBodyFieldId: string = '21eb6a42-e889-4d12-9241-234f41fa8cf0';
    export const alipayRedirectWaitingStateBodyFieldName: string = 'Alipay Redirect Waiting State Body';
    export const alipayRedirectWaitingStateTitleFieldId: string = '84ae3cd7-a87f-4809-b100-d41855ab85c0';
    export const alipayRedirectWaitingStateTitleFieldName: string = 'Alipay Redirect Waiting State Title';
    export const buttonTextFieldId: string = '0acdeea8-2118-4977-8830-8da76eb6ac97';
    export const buttonTextFieldName: string = 'ButtonText';
    export const confirmationPageUrlFieldId: string = '08921a6a-9812-42c6-acba-a1bb40d327ce';
    export const confirmationPageUrlFieldName: string = 'Confirmation Page Url';
    export const orderProcessingBodyFieldId: string = '1cb59b7a-8bcc-4e5c-b8fa-a56fb2664cde';
    export const orderProcessingBodyFieldName: string = 'OrderProcessingBody';
    export const orderProcessingTitleFieldId: string = '212449d4-e2c0-433e-b165-5103b69be9bc';
    export const orderProcessingTitleFieldName: string = 'OrderProcessingTitle';
    export const razorpayPayBySecureLinkButtonTextFieldId: string = 'ef0e5b60-0fcd-4e7d-93d9-129295750ab1';
    export const razorpayPayBySecureLinkButtonTextFieldName: string = 'Razorpay Pay by Secure Link Button Text';
    export const worldpayHostedPaymentPlaceOrderTextFieldId: string = '0c751bfb-eeb4-494a-997f-55c4d8486cf1';
    export const worldpayHostedPaymentPlaceOrderTextFieldName: string = 'WorldpayHostedPaymentPlaceOrderText';
  }
  export namespace PresumptiveChaptersDataTemplate {
    export const templateId: string = '71925bb2-5e7c-4386-bf21-f97a1df48044';
    export const templateName: string = 'PresumptiveChaptersData';
    export const accordionButtonTextFieldId: string = 'ab35db1b-53a8-4179-90bd-99f90df7370e';
    export const accordionButtonTextFieldName: string = 'AccordionButtonText';
    export const accordionInnerHeaderFieldId: string = '59b96202-371b-4561-93c3-fbca65192b30';
    export const accordionInnerHeaderFieldName: string = 'AccordionInnerHeader';
    export const accordionSubtitleFieldId: string = '256c21d4-2ec5-4bed-830b-585367bd66f9';
    export const accordionSubtitleFieldName: string = 'AccordionSubtitle';
    export const closeButtonTextFieldId: string = 'dc606e39-6c0c-42a3-ab2e-f6b5a4ab0673';
    export const closeButtonTextFieldName: string = 'CloseButtonText';
    export const exploreMoreChaptersButtonTextFieldId: string = '35f05231-167e-4831-b559-b6f8c8928488';
    export const exploreMoreChaptersButtonTextFieldName: string = 'ExploreMoreChaptersButtonText';
    export const exploreMoreChaptersUrlFieldId: string = '99eaec6f-4bad-46fe-8fc8-6f6529e33753';
    export const exploreMoreChaptersUrlFieldName: string = 'ExploreMoreChaptersUrl';
    export const headerTitleFieldId: string = '559d9860-b159-41c7-908e-365f279897ee';
    export const headerTitleFieldName: string = 'Header Title';
    export const textFieldId: string = '2d26cce3-6151-4d86-92ac-d2331459b2da';
    export const textFieldName: string = 'Text';
  }
  export namespace PrimaryPhoneNumberDataTemplate {
    export const templateId: string = '94f87600-167d-41cd-89d8-46a4227c6681';
    export const templateName: string = 'Primary Phone Number Data';
    export const areaCodeFieldId: string = '0f345a51-f3c7-4108-912f-2fa01fd01f48';
    export const areaCodeFieldName: string = 'Area Code';
    export const countryCodeFieldId: string = 'b62bf734-869b-4d5a-8660-067473eb4b06';
    export const countryCodeFieldName: string = 'Country Code';
    export const extFieldId: string = '539070fb-6919-4235-b901-6d42397faf2d';
    export const extFieldName: string = 'Ext';
    export const numberFieldId: string = 'a712c2c6-20c6-4b3f-b26b-e986f4446bea';
    export const numberFieldName: string = 'Number';
    export const phoneTextFieldId: string = '529c6153-168e-47dc-9307-4c71d94c9228';
    export const phoneTextFieldName: string = 'Phone Text';
    export const phoneTitleFieldId: string = '13de693c-3ebe-42db-b505-2e7ce7acf438';
    export const phoneTitleFieldName: string = 'Phone Title';
    export const phoneTooltipLinkFieldId: string = '0162e61f-8b24-46b9-9cce-acd17faeebf3';
    export const phoneTooltipLinkFieldName: string = 'Phone Tooltip Link';
    export const phoneTooltipTextFieldId: string = '89ca608c-dd03-4748-97a3-7f3fe75eecee';
    export const phoneTooltipTextFieldName: string = 'Phone Tooltip Text';
    export const phoneTooltipTitleFieldId: string = '9f1ae551-c855-4fd3-ada4-cc508a162b65';
    export const phoneTooltipTitleFieldName: string = 'Phone Tooltip Title';
  }
  export namespace ProductItemsRenderingParametersTemplate {
    export const templateId: string = '97267763-dc8b-411a-9419-f068c4b24243';
    export const templateName: string = 'Product Items Rendering Parameters';
    export const cartHeaderTextFieldId: string = '20f58c76-264a-4f39-af47-7db39d29ae83';
    export const cartHeaderTextFieldName: string = 'CartHeaderText';
    export const hideMemberPriceFieldId: string = '896ea8e2-4851-4801-be58-ec371df0b459';
    export const hideMemberPriceFieldName: string = 'HideMemberPrice';
    export const hidePriceInfoWhenMemberFieldId: string = '889aeafb-2f52-4f58-9ac2-1ba519fd2223';
    export const hidePriceInfoWhenMemberFieldName: string = 'HidePriceInfoWhenMember';
    export const hideProductItemsQuantityFieldId: string = '25045e8d-5629-4298-b956-5712000fa363';
    export const hideProductItemsQuantityFieldName: string = 'HideProductItemsQuantity';
    export const hideProductTypeIconFieldId: string = '7b0f93e9-680a-4b08-8f4e-d6887fe69242';
    export const hideProductTypeIconFieldName: string = 'HideProductTypeIcon';
    export const hideRegularPriceFieldId: string = '005c43f8-1abe-4ffe-b708-5164b0906ab5';
    export const hideRegularPriceFieldName: string = 'HideRegularPrice';
    export const productItemsDataSourceFieldId: string = '95999174-2ae6-4f33-bd6f-3b002f4770c8';
    export const productItemsDataSourceFieldName: string = 'Product Items Data Source';
    export const readOnlyFieldId: string = '98b350ac-aee8-442e-a53b-381bfedaa360';
    export const readOnlyFieldName: string = 'Read Only';
    export const showCartHeaderFieldId: string = '4dc6d222-ad7d-4893-a781-43726bf9f5fe';
    export const showCartHeaderFieldName: string = 'Show Cart Header';
    export const isABTestEnabledFieldId: string = 'b93a99e2-2dab-4efc-bee6-97bfd685a9d8';
    export const isABTestEnabledFieldName: string = 'isABTestEnabled';
    export const searchTimeoutAfterKeyPressInMillisecondsFieldId: string = 'af0ab8dd-77c6-407c-9162-a2b148e91881';
    export const searchTimeoutAfterKeyPressInMillisecondsFieldName: string = 'Search Timeout After Key Press in Milliseconds';
  }
  export namespace PromocodeComponentDataTemplate {
    export const templateId: string = '4721390f-fdd6-464f-b36e-14374c72fc55';
    export const templateName: string = 'Promocode Component Data';
    export const applyTextFieldId: string = '5ab9af36-37f6-4f2f-bd99-ef9670b33770';
    export const applyTextFieldName: string = 'Apply Text';
    export const promocodePlaceholderFieldId: string = '7f93d878-0c73-4309-b2d3-a1fdbcc56993';
    export const promocodePlaceholderFieldName: string = 'Promocode Placeholder';
  }
  export namespace PromocodeComponentTemplate {
    export const templateId: string = '50ded6f7-ecc0-425e-887a-81ff66d73a2e';
    export const templateName: string = 'Promocode Component';
    export const buttonLabelFieldId: string = '47b3e476-7658-48bb-be41-a0a8188c559b';
    export const buttonLabelFieldName: string = 'Button Label';
    export const placeholderFieldId: string = '6fd235be-7576-4329-91ee-c3b5bb0d2bfc';
    export const placeholderFieldName: string = 'Placeholder';
    export const titleFieldId: string = '1ed708fd-5f16-4eea-ae8f-938db338df33';
    export const titleFieldName: string = 'Title';
    export const tooltipMessageFieldId: string = '69855f0a-70a2-476e-8c6f-74735f011928';
    export const tooltipMessageFieldName: string = 'Tooltip Message';
  }
  export namespace PromoModalMembershipItemDataTemplate {
    export const templateId: string = '9edf6451-8e07-46f0-9be5-7d1e96e6ad82';
    export const templateName: string = 'Promo Modal Membership Item Data';
    export const membershipTextFieldId: string = 'bc326365-14bd-4023-afcc-180fe8a7b83c';
    export const membershipTextFieldName: string = 'Membership Text';
    export const productSkuFieldId: string = '329a816e-e846-4e58-8ff2-d57371df9d6c';
    export const productSkuFieldName: string = 'ProductSku';
  }
  export namespace QuoteFolderTemplate {
    export const templateId: string = '438aa786-2e07-430b-8dcf-77fb56f39f51';
    export const templateName: string = 'Quote Folder';
  }
  export namespace RazorpaySubmitButtonDataTemplate {
    export const templateId: string = '7650089d-abad-4e43-abae-a10fa4d93a40';
    export const templateName: string = 'Razorpay Submit Button Data';
    export const buttonTextFieldId: string = 'bba61f04-e6d6-417c-bc7c-7920e2b6b01b';
    export const buttonTextFieldName: string = 'ButtonText';
    export const settingsFieldId: string = '8a083ef4-6a8c-4bbb-bc2f-481c37245f81';
    export const settingsFieldName: string = 'Settings';
    export const overlayBodyFieldId: string = 'a201f942-68d3-4270-97d6-deb4fb2a7b74';
    export const overlayBodyFieldName: string = 'OverlayBody';
    export const overlayTitleFieldId: string = '7dbdb759-a0de-48fb-8297-b91b7b2c05d5';
    export const overlayTitleFieldName: string = 'OverlayTitle';
  }
  export namespace RequestQuoteLinkDataTemplate {
    export const templateId: string = 'd59f8d58-8434-4e4f-8c52-b0a1421908d3';
    export const templateName: string = 'RequestQuoteLinkData';
    export const goToCheckoutLinkFieldId: string = '04b4ad17-0193-4622-8a1f-a2497ea3d7d4';
    export const goToCheckoutLinkFieldName: string = 'GoToCheckoutLink';
    export const localCurrencyFieldId: string = '57c14d32-dfc1-4b32-9541-aec67167baf1';
    export const localCurrencyFieldName: string = 'LocalCurrency';
    export const tooltipMessageFieldId: string = '472181c2-2a1e-4f8c-8c95-9156957fb4e4';
    export const tooltipMessageFieldName: string = 'TooltipMessage';
  }
  export namespace ReturnToPmiOrgButtonTemplate {
    export const templateId: string = '4b9b60f0-4b8b-4da1-81e0-d109cba82416';
    export const templateName: string = 'Return To PmiOrg Button';
    export const buttonLinkFieldId: string = 'b500daa3-51eb-41f9-bb76-b00a26796435';
    export const buttonLinkFieldName: string = 'Button Link';
    export const buttonTextFieldId: string = '02716444-8e97-4334-8f1a-da3c337d207f';
    export const buttonTextFieldName: string = 'Button Text';
  }
  export namespace ReviewTotalTemplate {
    export const templateId: string = 'b6480775-4af2-4203-9cf1-596b074bbeea';
    export const templateName: string = 'Review Total';
    export const purchaseFieldId: string = '8298b455-5680-44f7-ac9c-92c7a33b2901';
    export const purchaseFieldName: string = 'Purchase';
    export const totalFieldId: string = 'd9447702-8e87-4a3d-a202-eb502e609d03';
    export const totalFieldName: string = 'Total';
    export const totalSavingFieldId: string = '17ee808c-4c6a-4a66-9a30-41e4aea2536f';
    export const totalSavingFieldName: string = 'TotalSaving';
  }
  export namespace SavedPaymentCardsDataTemplate {
    export const templateId: string = 'fd5a70f7-9b1e-4eb7-906d-36e3c5ef1aec';
    export const templateName: string = 'SavedPaymentCardsData';
    export const headingTextFieldId: string = 'dd4c711c-56ab-441f-8f57-a9f0b08d4f48';
    export const headingTextFieldName: string = 'HeadingText';
    export const addNewButtonTextFieldId: string = 'd209b6a4-dc32-45ed-88d1-4b08313a9601';
    export const addNewButtonTextFieldName: string = 'AddNewButtonText';
    export const editButtonTextFieldId: string = 'ecd5395d-fffb-4027-b816-2cb80f04b67d';
    export const editButtonTextFieldName: string = 'EditButtonText';
    export const submitButtonTextFieldId: string = '6ea3ce6f-1415-4e78-9c34-906fd43b114a';
    export const submitButtonTextFieldName: string = 'SubmitButtonText';
  }
  export namespace ShoppingCartDataTemplate {
    export const templateId: string = 'c9cf08a0-3889-4e3d-872b-825d4c5865ee';
    export const templateName: string = 'ShoppingCartData';
    export const emptyCartBodyFieldId: string = '5e63b29d-bbfa-4aad-942f-aef0f922beed';
    export const emptyCartBodyFieldName: string = 'EmptyCartBody';
    export const emptyCartHeadingFieldId: string = '5b1549b5-2e97-47c8-9b4c-5fd154f48177';
    export const emptyCartHeadingFieldName: string = 'EmptyCartHeading';
    export const emptyCartNavigateToLinkFieldId: string = '117b5cfd-1f53-40aa-a795-5c8e684fa0e6';
    export const emptyCartNavigateToLinkFieldName: string = 'EmptyCartNavigateToLink';
  }
  export namespace StudentMembershipConfirmationDataTemplate {
    export const templateId: string = 'ad640985-3d73-4f88-b101-114809ebb82c';
    export const templateName: string = 'Student Membership Confirmation Data';
    export const noteTextFieldId: string = 'f61fac9f-fad3-47b0-8187-bb91e171f457';
    export const noteTextFieldName: string = 'Note Text';
    export const noteTitleFieldId: string = '8170b282-0c05-4c87-8993-ce82d4487d12';
    export const noteTitleFieldName: string = 'Note Title';
    export const itemBoxTextFieldId: string = '98279841-8fa6-4b74-b93a-193246812b40';
    export const itemBoxTextFieldName: string = 'Item Box Text';
    export const itemBoxTitleTextFieldId: string = '0d789bcc-0b21-4373-8339-578d84d8031b';
    export const itemBoxTitleTextFieldName: string = 'Item Box Title Text';
    export const submitButtonTextFieldId: string = 'c53ed6a5-df0e-4cd2-8153-d578313e7fcf';
    export const submitButtonTextFieldName: string = 'Submit Button Text';
    export const titleFieldId: string = '2e4a9761-d4d2-464f-a245-a17db594a547';
    export const titleFieldName: string = 'Title';
  }
  export namespace SubmitOrderButtonDataTemplate {
    export const templateId: string = 'f2958f7d-bd23-4016-a727-35214bee6872';
    export const templateName: string = 'Submit Order Button Data';
    export const adyenPayBySecureLinkButtonTextFieldId: string = 'e56d408f-340f-4e91-af78-db68bc5b1818';
    export const adyenPayBySecureLinkButtonTextFieldName: string = 'Adyen Pay by Secure Link Button Text';
    export const alipayButtonTextFieldId: string = '78d0c51e-f011-44ae-929a-1327e8f26bfa';
    export const alipayButtonTextFieldName: string = 'Alipay Button Text';
    export const alipayOrderProcessingStateBodyFieldId: string = 'ff1dac78-3599-4587-b410-2f708f608c59';
    export const alipayOrderProcessingStateBodyFieldName: string = 'Alipay Order Processing State Body';
    export const alipayOrderProcessingStateTitleFieldId: string = '690fe3ae-5351-4226-a546-bfb8c0c44b0a';
    export const alipayOrderProcessingStateTitleFieldName: string = 'Alipay Order Processing State Title';
    export const alipayRedirectWaitingStateBodyFieldId: string = '21eb6a42-e889-4d12-9241-234f41fa8cf0';
    export const alipayRedirectWaitingStateBodyFieldName: string = 'Alipay Redirect Waiting State Body';
    export const alipayRedirectWaitingStateTitleFieldId: string = '84ae3cd7-a87f-4809-b100-d41855ab85c0';
    export const alipayRedirectWaitingStateTitleFieldName: string = 'Alipay Redirect Waiting State Title';
    export const buttonTextFieldId: string = '0acdeea8-2118-4977-8830-8da76eb6ac97';
    export const buttonTextFieldName: string = 'ButtonText';
    export const confirmationPageUrlFieldId: string = '08921a6a-9812-42c6-acba-a1bb40d327ce';
    export const confirmationPageUrlFieldName: string = 'Confirmation Page Url';
    export const orderProcessingBodyFieldId: string = '1cb59b7a-8bcc-4e5c-b8fa-a56fb2664cde';
    export const orderProcessingBodyFieldName: string = 'OrderProcessingBody';
    export const orderProcessingTitleFieldId: string = '212449d4-e2c0-433e-b165-5103b69be9bc';
    export const orderProcessingTitleFieldName: string = 'OrderProcessingTitle';
    export const razorpayPayBySecureLinkButtonTextFieldId: string = 'ef0e5b60-0fcd-4e7d-93d9-129295750ab1';
    export const razorpayPayBySecureLinkButtonTextFieldName: string = 'Razorpay Pay by Secure Link Button Text';
    export const worldpayHostedPaymentPlaceOrderTextFieldId: string = '0c751bfb-eeb4-494a-997f-55c4d8486cf1';
    export const worldpayHostedPaymentPlaceOrderTextFieldName: string = 'WorldpayHostedPaymentPlaceOrderText';
  }
  export namespace ThankYouMessageTemplate {
    export const templateId: string = 'c1e332ff-95ab-4613-96d9-89b13b612b41';
    export const templateName: string = 'Thank You Message';
    export const orderProcessingTextFieldId: string = '67195295-6fd7-495f-a52d-a84e5c1048b4';
    export const orderProcessingTextFieldName: string = 'Order Processing Text';
    export const thankInquiryTextFieldId: string = 'b86e3b7a-68e6-4718-916f-727bab832922';
    export const thankInquiryTextFieldName: string = 'Thank Inquiry Text';
    export const thankTextFieldId: string = '4bd62f9f-2c5a-4975-91a9-f9738e54f98c';
    export const thankTextFieldName: string = 'Thank Text';
  }
  export namespace ViewInvoiceButtonDataTemplate {
    export const templateId: string = '5f740f0d-7d69-475d-bf40-52d105dd4728';
    export const templateName: string = 'ViewInvoiceButtonData';
    export const checkOrderStatusLinkFieldId: string = 'e5b2c3f0-029f-4f6f-88b3-836862eec64f';
    export const checkOrderStatusLinkFieldName: string = 'CheckOrderStatusLink';
    export const viewInvoiceButtonLinkFieldId: string = 'b23d817a-ceca-4314-a285-a9d9ecd5580b';
    export const viewInvoiceButtonLinkFieldName: string = 'ViewInvoiceButtonLink';
  }
  export namespace ViewInvoicePendingButtonDataTemplate {
    export const templateId: string = 'ba398f9b-4446-419b-b3e4-15c7ab63ac31';
    export const templateName: string = 'ViewInvoicePendingButtonData';
    export const checkOrderStatusLinkFieldId: string = 'f8683af4-6379-48fe-b134-83ab4ff44687';
    export const checkOrderStatusLinkFieldName: string = 'CheckOrderStatusLink';
    export const viewInvoiceButtonLinkFieldId: string = 'b2d820be-ff8a-4612-984d-eb93439ad995';
    export const viewInvoiceButtonLinkFieldName: string = 'ViewInvoiceButtonLink';
    export const viewOrderStatusTextFieldId: string = 'fd292bfd-59c6-4e1e-9cdc-3742b350b6eb';
    export const viewOrderStatusTextFieldName: string = 'ViewOrderStatusText';
  }
  export namespace VouchercodeComponentTemplate {
    export const templateId: string = '122d2f80-0720-49b7-84e0-1e8324019091';
    export const templateName: string = 'Vouchercode Component';
    export const buttonLabelFieldId: string = 'de4f5815-5574-4563-bc96-31fbea9894c0';
    export const buttonLabelFieldName: string = 'Button Label';
    export const placeholderFieldId: string = 'caccba1a-b833-4458-8c5f-b5684cd47274';
    export const placeholderFieldName: string = 'Placeholder';
    export const titleFieldId: string = 'f6f7f6c2-61b9-4ac9-b92e-e12d061e90a4';
    export const titleFieldName: string = 'Title';
    export const tooltipMessageFieldId: string = 'dda9e5d5-7f87-41ae-afc6-c3bef3aea52b';
    export const tooltipMessageFieldName: string = 'Tooltip Message';
  }
  export namespace VoucherComponentDataTemplate {
    export const templateId: string = '6695a9ee-e354-463b-baec-b92e810cebe7';
    export const templateName: string = 'Voucher Component Data';
    export const agreementTextFieldId: string = 'eb419c1e-983d-48c5-933e-fabb79ee9c62';
    export const agreementTextFieldName: string = 'Agreement Text';
    export const appliedTextFieldId: string = 'bf0fd84b-2822-40c4-891c-100000abc65b';
    export const appliedTextFieldName: string = 'Applied Text';
    export const applyTextFieldId: string = 'be5fd7b5-f8ab-4278-9950-b9a0b373bfcb';
    export const applyTextFieldName: string = 'Apply Text';
    export const learnTextFieldId: string = '5a47ed0c-a423-4600-b433-955d21cdd9cc';
    export const learnTextFieldName: string = 'Learn Text';
    export const placeholderTextFieldId: string = 'db4c1b84-aac3-43af-86da-35009f5c05d4';
    export const placeholderTextFieldName: string = 'Placeholder Text';
    export const removeTextFieldId: string = '5e6f8770-e652-42dd-a1c0-d70d8a55ac2d';
    export const removeTextFieldName: string = 'Remove Text';
    export const tooltipDescriptionFieldId: string = '0c6ec74e-18ac-4521-97ad-5a538bffe306';
    export const tooltipDescriptionFieldName: string = 'Tooltip Description';
    export const tooltipHeaderFieldId: string = '0ba798ab-ef89-431f-a5ff-520f545a1294';
    export const tooltipHeaderFieldName: string = 'Tooltip Header';
    export const tooltipLinkFieldId: string = '7e966440-890c-47eb-8983-7a5b1513d5b1';
    export const tooltipLinkFieldName: string = 'Tooltip Link';
    export const tooltipLinknameFieldId: string = '70ecb8e1-6fc0-42ce-bfed-1fd8aff1338b';
    export const tooltipLinknameFieldName: string = 'Tooltip Linkname';
  }
