import { RadioButton } from '@pmi/dsm-react/dist/components/radiobutton';
import { razorpayPayBySecureLinkPaymentData } from 'Feature/NewCheckoutFow/atoms';
import { useAvailablePaymentMethods, useCurrentUser } from 'Feature/NewCheckoutFow/hooks';
import { PaymentMethodCodes } from 'Foundation/Payment/client/BackendConstants.Generated';
import { Text } from 'Foundation/ReactJss/client/Components';
import { useSetAtom } from 'jotai';
import React from 'react';
import { PaymentMethodRazorpayBySecureLinkProps } from './models';

const Component: React.FC<PaymentMethodRazorpayBySecureLinkProps> = ({
  fields,
  selectedPaymentMethodCode,
  paymentMethodChanged,
}) => {
  const { getPaymentMethod } = useAvailablePaymentMethods();
  const paymentMethod = getPaymentMethod(PaymentMethodCodes.RazorpayHosted);
  const setRazorpayPayBySecureLinkPaymentData = useSetAtom(razorpayPayBySecureLinkPaymentData);
  const { isUserImpersonated } = useCurrentUser();

  if (!paymentMethod || !isUserImpersonated) return null;

  const onSelectingPaymentMethod = () => {
    paymentMethodChanged(PaymentMethodCodes.RazorpayHosted);
    setRazorpayPayBySecureLinkPaymentData({ payWithRazorpaySecureLinkPayment: true });
  };

  return (
    <li className="payment-paymentmethods__item razorpay__item" key={paymentMethod.code}>
      <RadioButton
        id={paymentMethod.code}
        name={paymentMethod.code}
        value={paymentMethod.code}
        checked={selectedPaymentMethodCode === paymentMethod.code}
        onChange={onSelectingPaymentMethod}
        label={
          <Text tag="div" className="razorpay__item-label" field={fields?.description} fallback="Pay By Secure Link" />
        }
      />
    </li>
  );
};

export default Component;
