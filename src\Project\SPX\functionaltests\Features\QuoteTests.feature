﻿@local @browser:Chrome-Headless @desktop
Feature: QuoteTests
Ensure user is able to generate a quote and verify the order summary
Background: 
	Given an empty cart

@Region2Store @Qa @Can @Adyen
Scenario: Generate quote from region 2 store with Worldpay

	Given a newly registered user from data scaffolding is logged in
	When Add digital product to the cart via SKU's
	 And Add membership to the cart via SKU's
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Click on request a quote link
	 And Fill out billing address on quote page and save for future use
	 And Place quote order
	Then Verify order confirmation page for quote order
	 
	When Navigate to order review page by clicking purchase quote button
	Then Verify the order summary on the checkout page
	 
	When Update creditcard payment details and save for future use
	 And Place worldpay creditcard order
	Then Verify order confirmation page

@IndiaStore @Qa @Can @Adyen
Scenario: Generate quote from India store

	Given a newly registered user from data scaffolding is logged in
	When Go to the cart page
	 And Add Single membership to the cart and change the chapter
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Click on request a quote link
	 And Fill out contact information on quote page
	 And Fill out billing address on quote page and save for future use
	 And Place quote order
	Then Verify order confirmation page for quote order
	
	When Navigate to order review page by clicking purchase quote button
	 And Fill out contact information
	 And Select existing billing address on checkout page
	Then Verify the order summary on the checkout page
	
	When Place RazorPay UPI for Subscription product
    Then Verify order confirmation page

@BrazilStore @Qa @Can
Scenario: Generate quote from Brazil store

	Given a newly registered user from data scaffolding is logged in
	When Add digital product to the cart via SKU's
	 And Add membership to the cart via SKU's
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Click on request a quote link
	 And Fill out billing address on quote page and save for future use
     And Place quote order
	Then Verify order confirmation page for quote order
	
	When Navigate to order review page by clicking purchase quote button
	Then Verify the order summary on the checkout page
	 
	When Update creditcard payment details and save for future use
	 And Place worldpay creditcard order
	Then Verify order confirmation page

@ChinaStore @Qa @Can
Scenario: Generate quote from China store

    Given a newly registered user from data scaffolding is logged in
    When Add digital product to the cart via SKU's
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Click on request a quote link
	 And Add billing address on quote page and not save for future use
	Then Verify the order summary on the Quote page

	When Place quote order
	Then Verify order confirmation page for quote order
	
	When Navigate to order review page by clicking purchase quote button
	 And Click edit cart button on checkout page
	 And Proceed to checkout from cart
	Then Verify the order summary on the checkout page
	 And Place Adyen AliPay order
	Then Verify order confirmation page
	

@USAStore @Qa @Can @Adyen
Scenario: Generate quote from Usa store with Adyen

	Given a newly registered user from data scaffolding is logged in
	When Add digital product to the cart via SKU's
	 And Add membership to the cart via SKU's
	 And Add donation to the cart
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Click on request a quote link
	 And Fill out billing address on quote page and save for future use
	 And Place quote order	
	Then Verify order confirmation page for quote order
	
	When Navigate to order review page by clicking purchase quote button
	Then Verify the order summary on the checkout page
	
	When Place Adyen Credit card order and save for future use
	Then Verify order confirmation page

@MainStore @Qa @Can
Scenario: Verify user is able to apply Voucher during quote purchase
	
	Given a newly registered user from data scaffolding is logged in
	When Add digital product to the cart via SKU's
	Then Verify products on the cart page
	Then Verify the order summary on the cart page
	
	When Click on request a quote link
	 And Fill out billing address on quote page and save for future use
	 And Place quote order
	Then Verify order confirmation page for quote order
	 
	When Navigate to order review page by clicking purchase quote button
	 And Apply voucher on the cart page
	Then Verify the order summary on the checkout page