// tslint:disable:indent array-type
// tslint:disable:no-use-before-declare
// tslint:disable:no-namespace
// @ts-ignore
  import * as ReactJssModule from 'Foundation/ReactJss/client';
// @ts-ignore

  // The Adyen Settings template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Payment Adyen/Adyen Settings
  // ID: ef099c9a-8259-4fb7-ad8b-7de71bad2ea9
  export interface AdyenSettingsDataSource extends ReactJssModule.BaseDataSourceItem {
    // The AnalyticsEnabled field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 47675771-2c3d-41a0-8b22-2aa6d946419b
    // Custom Data:
    analyticsEnabled: ReactJssModule.Field<boolean>;
    // The CardHideCVC field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: fa0cddcf-d7d3-44c6-846b-13acaf2eac13
    // Custom Data:
    cardHideCVC: ReactJssModule.Field<boolean>;
    // The Card_Name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 394ed4be-e4c5-4f23-96d7-a7f888c7249d
    // Custom Data:
    cardName: ReactJssModule.TextField;
    // The ClientKey field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 6197794d-45e5-4c3b-b958-37ff1fda5d24
    // Custom Data:
    clientKey: ReactJssModule.TextField;
    // The CssURL field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: d3da0d22-4042-4717-adfc-f09ffb7a7363
    // Custom Data:
    cssURL: ReactJssModule.TextField;
    // The Environment field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: f59857e5-5628-436a-8d79-eda5f94b5829
    // Custom Data:
    environment: ReactJssModule.TextField;
    // The PayPalClientId field.
    // Short description: [Deprecated] It is not being used anymore
    // Field Type: Single-Line Text
    // Field ID: feb15724-c2cc-44cc-8729-b97757b145ab
    // Custom Data:
    payPalClientId: ReactJssModule.TextField;
    // The ShowStoredPaymentMethods field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 908666ba-53df-4493-b514-fa10b42341f0
    // Custom Data:
    showStoredPaymentMethods: ReactJssModule.Field<boolean>;
    // The ThreeDsChallengeWindowSize field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: fe788ba5-7076-42b9-9557-b92a368aa1bc
    // Custom Data:
    threeDsChallengeWindowSize: ReactJssModule.TextField;
  }
  // The Global Payment Adyen Settings template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Payment Adyen/Global Payment Adyen Settings
  // ID: 94f6226e-e57f-4463-a622-024b12e4f957
  export interface GlobalPaymentAdyenSettingsDataSource extends ReactJssModule.BaseDataSourceItem {
  }

  // The Adyen Settings template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Payment Adyen/Adyen Settings
  // ID: ef099c9a-8259-4fb7-ad8b-7de71bad2ea9
  export interface AdyenSettingsRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The AnalyticsEnabled field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 47675771-2c3d-41a0-8b22-2aa6d946419b
    // Custom Data:
    analyticsEnabled: boolean;
    // The CardHideCVC field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: fa0cddcf-d7d3-44c6-846b-13acaf2eac13
    // Custom Data:
    cardHideCVC: boolean;
    // The Card_Name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 394ed4be-e4c5-4f23-96d7-a7f888c7249d
    // Custom Data:
    cardName: string;
    // The ClientKey field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 6197794d-45e5-4c3b-b958-37ff1fda5d24
    // Custom Data:
    clientKey: string;
    // The CssURL field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: d3da0d22-4042-4717-adfc-f09ffb7a7363
    // Custom Data:
    cssURL: string;
    // The Environment field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: f59857e5-5628-436a-8d79-eda5f94b5829
    // Custom Data:
    environment: string;
    // The PayPalClientId field.
    // Short description: [Deprecated] It is not being used anymore
    // Field Type: Single-Line Text
    // Field ID: feb15724-c2cc-44cc-8729-b97757b145ab
    // Custom Data:
    payPalClientId: string;
    // The ShowStoredPaymentMethods field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 908666ba-53df-4493-b514-fa10b42341f0
    // Custom Data:
    showStoredPaymentMethods: boolean;
    // The ThreeDsChallengeWindowSize field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: fe788ba5-7076-42b9-9557-b92a368aa1bc
    // Custom Data:
    threeDsChallengeWindowSize: string;
  }
  // The Global Payment Adyen Settings template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Payment Adyen/Global Payment Adyen Settings
  // ID: 94f6226e-e57f-4463-a622-024b12e4f957
  export interface GlobalPaymentAdyenSettingsRenderingParams extends ReactJssModule.BaseRenderingParam {
  }
  export namespace AdyenSettingsTemplate {
    export const templateId: string = 'ef099c9a-8259-4fb7-ad8b-7de71bad2ea9';
    export const templateName: string = 'Adyen Settings';
    export const analyticsEnabledFieldId: string = '47675771-2c3d-41a0-8b22-2aa6d946419b';
    export const analyticsEnabledFieldName: string = 'AnalyticsEnabled';
    export const cardHideCVCFieldId: string = 'fa0cddcf-d7d3-44c6-846b-13acaf2eac13';
    export const cardHideCVCFieldName: string = 'CardHideCVC';
    export const cardNameFieldId: string = '394ed4be-e4c5-4f23-96d7-a7f888c7249d';
    export const cardNameFieldName: string = 'Card_Name';
    export const clientKeyFieldId: string = '6197794d-45e5-4c3b-b958-37ff1fda5d24';
    export const clientKeyFieldName: string = 'ClientKey';
    export const cssURLFieldId: string = 'd3da0d22-4042-4717-adfc-f09ffb7a7363';
    export const cssURLFieldName: string = 'CssURL';
    export const environmentFieldId: string = 'f59857e5-5628-436a-8d79-eda5f94b5829';
    export const environmentFieldName: string = 'Environment';
    export const payPalClientIdFieldId: string = 'feb15724-c2cc-44cc-8729-b97757b145ab';
    export const payPalClientIdFieldName: string = 'PayPalClientId';
    export const showStoredPaymentMethodsFieldId: string = '908666ba-53df-4493-b514-fa10b42341f0';
    export const showStoredPaymentMethodsFieldName: string = 'ShowStoredPaymentMethods';
    export const threeDsChallengeWindowSizeFieldId: string = 'fe788ba5-7076-42b9-9557-b92a368aa1bc';
    export const threeDsChallengeWindowSizeFieldName: string = 'ThreeDsChallengeWindowSize';
  }
  export namespace GlobalPaymentAdyenSettingsTemplate {
    export const templateId: string = '94f6226e-e57f-4463-a622-024b12e4f957';
    export const templateName: string = 'Global Payment Adyen Settings';
  }
