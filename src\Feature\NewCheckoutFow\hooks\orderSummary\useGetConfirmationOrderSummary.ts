import { useQuery } from '@tanstack/react-query';
import { use404ResponseErrorHandler, useAddError } from 'Feature/NewCheckoutFow';
import { getOrder } from 'Feature/NewCheckoutFow/api';
import { orderTrackingNumberAtom } from 'Feature/NewCheckoutFow/atoms';
import { PaymentMethodCodes, RazorpayCodes } from 'Feature/NewCheckoutFow/constants';
import { getOrderQueryKey } from 'Feature/NewCheckoutFow/utils';
import { getFormatedPrice } from 'Foundation/Extensions/client';
import { useAtomValue } from 'jotai';

export const useGetConfirmationOrderSummary = () => {
  const { addApiError } = useAddError();
  const { is404Error } = use404ResponseErrorHandler();

  const trackingNumber = useAtomValue(orderTrackingNumberAtom);
  const result = useQuery(getOrderQueryKey(trackingNumber), () => getOrder(trackingNumber), {
    enabled: !!trackingNumber,
    // TODO: onError callback will be removed in next major verion. It needs to be refactored during Global Handler implementation.
    onError: async (error: any) => {
      if (!is404Error(error)) {
        addApiError(error, true);
      }
    },
  });

  const orderBillingAddress = result?.data?.shippingAddress; // using shipping address b/c partial voucher issue

  const getAuthorizedTotalCurrency = () => {
    const currentOrderData = result?.data;
    return currentOrderData?.paidWithLocalCurrency
      ? currentOrderData.localCurrencySymbol
      : currentOrderData?.currencySymbol;
  };

  const orderHasGiftedMembership = result?.data?.giftRecipientEmail;
  const orderHasSingleMembership = result?.data?.membershipCartLine?.selectedBundleOptions?.some(
    (x) => x?.selectedProductType?.toLowerCase() === 'chapter membership',
  );
  const orderHasMembership = result?.data?.cartLines?.some((x) => x?.product?.productType.includes('Membership'));

  const orderHasStudentMembership = result?.data?.membershipCartLine?.displayName
    .toLowerCase()
    .includes('student membership');

  const orderHasRetireeMembership = result?.data?.membershipCartLine?.displayName
    .toLowerCase()
    .includes('retiree membership');

  const isIndiaOrder = result?.data?.currencyCode === 'INR';

  const isIndiaOrderNonRenew = isIndiaOrder && !RazorpayCodes.includes(result?.data?.paymentMethodCode);

  return {
    error: result.error,
    isError: result.isError,
    isFetching: result.isFetching,
    isLoading: result.isLoading,
    isSuccess: result.isSuccess,
    orderData: result.data,
    orderBillingAddress,
    orderHasGiftedMembership,
    orderHasSingleMembership,
    orderHasStudentMembership,
    orderHasMembership,
    orderHasRetireeMembership,
    isQuote: result.data?.paymentMethodCode === PaymentMethodCodes.Quote,
    isFree: result.data?.paymentMethodCode === PaymentMethodCodes.Free,
    paidOrderTotal: !!result?.data?.paidWithLocalCurrency
      ? getFormatedPrice(Math.abs(result?.data?.price?.totalGrossLocalCurrencyAmount))
      : getFormatedPrice(Math.abs(result?.data?.price?.grandTotal)),
    paidOrderTotalCurrency: getAuthorizedTotalCurrency(),
    itemsQuantity: result.data?.cartLines?.length,
    isIndiaOrder,
    isIndiaOrderNonRenew,
  };
};
