// tslint:disable:indent array-type
// tslint:disable:no-use-before-declare
// tslint:disable:no-namespace
  export namespace ExperienceTestConstants {
    export const OnePmiMembershipTestKey: string = 'OnePmiMembershipTest';
    export const ExperienceTestResultKey: string = 'ExperienceTestResult';
    export const PresumptiveSkuKey: string = 'presumptivesku';
    export const DeleteAdditionalCartLineItemKey: string = 'deleteadditionalcartlineitemid';
    export const DeleteAdditionalCartLineSkuKey: string = 'deleteadditionalcartlinesku';
    export const ExperienceBResultKey: string = 'ExperienceB';
  }
  export namespace FulfillmentProviderTypes {
    export const PMIEventProvider: string = 'PMIEVENT';
  }
  export namespace InformationCodes {
    export const ExtraMembershipInformation: string = 'ExtraMembershipInformation';
  }
  export namespace InitStoreReduxActions {
    export const InitAvailablePaymentMethodCodes: string = 'd59dab9a-19fc-4b58-bdb8-03d84eec0c7c_initAvailablePaymentMethodCodes';
  }
  export namespace WarningCodes {
    export const IndiaCardNotification: string = 'IndiaCardNotification';
  }
