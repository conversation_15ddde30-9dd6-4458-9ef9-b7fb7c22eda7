import {
  AddressModel,
  ChapterModel,
  EnrollAbandonedCartModel,
  PaymentModel,
  PaymentResultModel,
  ShippingMethodModel,
  ValidatePONumberInitModel,
} from 'Feature/Checkout/client/BackendModels.Generated';
import { PaymentCardTypes } from 'Foundation/Profile/client/BackendModels.Generated';
import { StoreModel } from 'Foundation/Commerce/client';
import { LocationModel } from 'Foundation/GeoIP/client/BackendModels.Generated';
import { PaymentMethodModel } from 'Feature/NewCheckoutFow/models';

export interface ShippingAddressVariables {
  shippingAddress: AddressModel;
  shippingMethod: ShippingMethodModel;
  billingAddress: AddressModel;
}

export interface PaymentVariables {
  paymentData: PaymentModel;
}

export interface ValidatePONumberVariables {
  validatePONumberInitModel: ValidatePONumberInitModel;
}

export interface ShippingVariables {
  shippingAddress: AddressModel;
}

export interface SetShippingAddressResponse {
  setShippingAddresses: boolean;
}

export interface GetAvailablePaymentMethodCodesResponse {
  availablePaymentMethodCodes: string[];
}

export interface GetPaymentMethodsResponse {
  paymentMethods: PaymentMethodModel[];
}

export interface SubmitPaymentResponse {
  setPaymentData: PaymentResultModel;
}

export interface GetGeoIpDataResponse {
  getGeoIpData: LocationModel;
}

export interface GetShippingMethodsResponse {
  shippingMethods: ShippingMethodModel[];
}

export interface GetChapterListResponse {
  chapterList: ChapterModel[];
}

export interface ValidatePONumberResponse {
  validatePONumber: boolean;
}

export interface GetCurrentStoreResponse {
  currentStore: StoreModel;
}
export interface BillingAddressModel {
  id: number;
  firstName: string;
  lastName: string;
  address1: string;
  address2: string;
  city: string;
  countryCode: string;
  countryCode3: string;
  state: string;
  zipPostalCode: string;
}

export interface SetBillingAddressVariables {
  address: BillingAddressModel;
}

export interface PaymentCardInfo {
  id: number;
  firstFourDigits: string;
  lastFourDigits: string;
  expirationDate: string;
  expirationYear: number;
  expirationMonth: number;
  cardType: PaymentCardTypes;
  cardTypeDisplay: string;
  cardHolderName?: string;
  isDefault: boolean;
}

export interface EnrollAbandonedCartCampaignVariables extends EnrollAbandonedCartModel {}

export interface EnrollAbandonedCartCampaignResponse {}
