----item----
version: 1
id: {8610DDA6-3FED-465B-B496-129DA75FA0CB}
database: master
path: /sitecore/templates/SPX/Feature/Checkout/New Checkout/Data Sources/Payment Method Razorpay Data/__Standard Values
parent: {610E0589-93D8-4C8E-A2C6-07F557058746}
name: __Standard Values
master: {00000000-0000-0000-0000-000000000000}
template: {610E0589-93D8-4C8E-A2C6-07F557058746}
templatekey: Payment Method Razorpay Data

----field----
field: {FD4E2050-186C-4375-8B99-E8A85DD7436E}
name: __Enable item fallback
key: __enable item fallback
content-length: 1

1
----version----
language: en
version: 1
revision: d777eb28-031f-4cc6-a8a1-599a1f47ca7e

----field----
field: {C2B1437E-2371-4D2F-8BDD-6319D6AA20A2}
name: CreditCardLabel
key: creditcardlabel
content-length: 20

Credit or Debit Card
----field----
field: {D6C35601-1201-4828-AC43-C8C4BA3DC380}
name: CreditCardSubtext
key: creditcardsubtext
content-length: 67

A secure payment window will open for you to complete your payment.
----field----
field: {F59678A9-3A7C-4C27-B9E4-4D43E81F6D8A}
name: UpiLabel
key: upilabel
content-length: 3

UPI
----field----
field: {5560E6C3-6143-4BB7-BE63-AA54250605F6}
name: UPILimitAmount
key: upilimitamount
content-length: 5

24999
----field----
field: {C37D78CA-21F8-4BEA-A74C-57CF8ED7A588}
name: NetbankingLabel
key: netbankinglabel
content-length: 10

Netbanking
----field----
field: {9F3F2185-13C7-4941-8A78-2067CFDD1720}
name: MembershipAndSubscriptionAutoRenewMessage
key: membershipandsubscriptionautorenewmessage
content-length: 351

By completing your payment, you consent to automatic renewal of your membership and subscriptions. Membership fees, including chapters, and applicable taxes, are billed annually, while subscription frequency varies based on your chosen plan. You may turn off this feature or edit your account through your myPMI profile or by contacting Customer Care.
----field----
field: {********-0F8F-4B20-8D2A-CB71D28C6103}
name: __Owner
key: __owner
content-length: 14

sitecore\admin
----field----
field: {25BED78C-4957-4165-998A-CA1B52F67497}
name: __Created
key: __created
content-length: 16

20250303T162342Z
----field----
field: {5DD74568-4D4B-44C1-B513-0AF5F4CDA34F}
name: __Created by
key: __created by
content-length: 14

sitecore\admin
----field----
field: {8CDC337E-A112-42FB-BBB4-4143751E123F}
name: __Revision
key: __revision
content-length: 36

d777eb28-031f-4cc6-a8a1-599a1f47ca7e
----field----
field: {D9CF14B1-FA16-4BA6-9288-E8A174D4D522}
name: __Updated
key: __updated
content-length: 16

20250613T143751Z
----field----
field: {BADD9CF9-53E0-4D0C-BCC0-2D784C282F6A}
name: __Updated by
key: __updated by
content-length: 14

sitecore\Admin
