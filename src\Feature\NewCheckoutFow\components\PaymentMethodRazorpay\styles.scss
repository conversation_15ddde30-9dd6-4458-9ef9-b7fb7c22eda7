.razorpay {
  &__item {
    &:first-of-type,
    .razorpay__notification + & {
      border-top-left-radius: var(--pmi-dsm-border-radius-lg);
      border-top-right-radius: var(--pmi-dsm-border-radius-lg);
    }
    &:last-of-type {
      border-bottom-left-radius: var(--pmi-dsm-border-radius-lg);
      border-bottom-right-radius: var(--pmi-dsm-border-radius-lg);
    }
    &-selected {
      background: var(--fill-accent-softest);
    }

    &-disabled {
      opacity: 0.5;
      cursor: not-allowed;

      .MuiFormControlLabel-label {
        color: var(--text-disabled) !important;
      }

      .razorpay__item-desc {
        color: var(--text-disabled) !important;
      }
    }

    @at-root .dsm & {
      .MuiFormControlLabel-label {
        color: var(--text-primary);
        font-size: calc(var(--pmi-dsm-font-size-md));
        line-height: var(--pmi-dsm-font-line-height-3);
        font-weight: var(--pmi-dsm-font-weight-normal);
        text-overflow: ellipsis;
      }
      &-label {
        display: flex;
        align-items: center;
        gap: var(--scale-8);
        margin-top: 2px;
      }
      &-card {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 12px;
        flex: 1 0 0;
        &-img {
          width: 34px;
          height: auto;
          border-radius: var(--pmi-dsm-border-radius-md);
        }
      }
      &-desc {
        padding: 0 var(--pmi-dsm-layout-xs);
        color: var(--text-secondary);
        font-size: calc(var(--pmi-dsm-font-size-sm));
        font-style: normal;
        font-weight: var(--pmi-dsm-font-weight-normal);
        line-height: calc(var(--pmi-dsm-font-line-height-1) * 1.125);
      }
    }
  }

  &__notification {
    @at-root .dsm & {
      .alert.alert-neutral {
        border-radius: var(--pmi-dsm-border-radius-lg);
        box-shadow: none;
        border: var(--border-xs) solid var(--border-neutral);

        &::before {
          display: none;
        }
      }

      .alert-content {
        .alert-text {
          color: var(--text-neutral);
          font-size: var(--pmi-dsm-font-size-sm);
          line-height: calc(var(--pmi-dsm-font-line-height-1) * 1.125);
        }
      }
    }
  }
}
