/* Tailwind CSS directives */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 
 * Custom component classes can be added here
 * These will be processed by Tailwind and available as utilities
 */
@layer components {
  /* Add custom component styles here if needed */
  /* Example:
  .btn-primary {
    @apply bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded;
  }
  */
}

@layer utilities {
  /* Add custom utility classes here if needed */
  /* Example:
  .text-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  }
  */
}
