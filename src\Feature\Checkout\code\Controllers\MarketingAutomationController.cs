﻿namespace Pmi.Spx.Feature.Checkout.Controllers
{
    using Pmi.Spx.Feature.Checkout.JsModels.MarketingAutomation;
    using Pmi.Spx.Feature.Checkout.Services;
    using Pmi.Spx.Foundation.Extensions.Extensions;
    using Pmi.Spx.Foundation.Framework.Services.Logging;
    using Sitecore.Commerce;
    using Sitecore.Mvc.Controllers;
    using System;
    using System.Net;
    using System.Web.Http;
    using System.Web.Mvc;

    public class MarketingAutomationController : SitecoreController
    {
        private readonly IMarketingAutomationService _marketingAutomationService;
        private readonly ILogger _logger;

        public MarketingAutomationController(IMarketingAutomationService marketingAutomationService, ILoggerFactory loggerFactory)
        {
            Assert.ArgumentNotNull(marketingAutomationService, nameof(marketingAutomationService));
            Assert.ArgumentNotNull(loggerFactory, nameof(loggerFactory));

            _marketingAutomationService = marketingAutomationService;
            _logger = loggerFactory.GetLogger(this);
        }

        [System.Web.Http.HttpPost]
        public ActionResult EnrollAbandonedCartCampaign([FromBody] EnrollAbandonedCartModel enrollAbandonedCartModel)
        {
            try
            { 
                _marketingAutomationService.EnrollAbandonedCartCampaign(enrollAbandonedCartModel);
                return this.JsonOk("Success");
            }
            catch(Exception ex)
            {
                _logger.Error($"Error in {nameof(MarketingAutomationController)}.{nameof(this.EnrollAbandonedCartCampaign)}(). Sku: {enrollAbandonedCartModel.Sku}", ex);
                return this.JsonError("An unexpected error occurred", HttpStatusCode.InternalServerError, ex);
            }
        }
    }
}