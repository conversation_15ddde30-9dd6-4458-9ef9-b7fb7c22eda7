﻿namespace Pmi.Spx.Feature.Checkout.Pipelines
{
    using Sitecore.Mvc.Pipelines.Loader;
    using Sitecore.Pipelines;
    using System.Web.Http;
    using System.Web.Mvc;
    using System.Web.Routing;

    public class RegisterRoutes : InitializeRoutes
    {
        public override void Process(PipelineArgs args)
        {
            GlobalConfiguration.Configuration.Routes.MapHttpRoute(
                "ChapterResultsSearch",
                "api/bundleoptions/chapterproducts/search", new { controller = "ChapterProductsSearch", action = "Results" }
            );
            GlobalConfiguration.Configuration.Routes.MapHttpRoute(
                "FreeProductActivation",
                "api/spx/activatefreeproduct",
                new { controller = "FreeProductActivation", action = "ActivateFreeProduct" });
            GlobalConfiguration.Configuration.Routes.MapHttpRoute(
                "NegotiateFreeProductActivation",
                "api/spx/freeproductactivation/negotiate",
                new { controller = "FreeProductActivation", action = "NegotiateFreeProductActivation" });

            RouteTable.Routes.MapRoute(
                "EnrollAbandonedCartCampaign",
                "api/spx/enrollAbandonedCart",
                new { controller = "MarketingAutomation", action = "EnrollAbandonedCartCampaign" });
        }
    }
}