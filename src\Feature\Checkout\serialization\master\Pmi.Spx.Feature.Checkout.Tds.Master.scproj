﻿<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.21022</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>85b13f8f-7225-4b0a-8e82-23b207f3d30e</ProjectGuid>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <CompactSitecoreItemsInProjectFile>True</CompactSitecoreItemsInProjectFile>
    <AssemblyName>Pmi.Spx.Feature.Checkout.Tds.Master</AssemblyName>
    <Name>Pmi.Spx.Feature.Checkout.Tds.Master</Name>
    <RootNamespace>Pmi.Spx.Feature.Checkout.Tds.Master</RootNamespace>
    <SourceWebVirtualPath>/Pmi.Spx.Feature.Checkout.csproj</SourceWebVirtualPath>
    <SourceWebProject>{ec1db1be-b3a9-4ac2-8700-34186e20444a}|Feature\Checkout\code\Pmi.Spx.Feature.Checkout.csproj</SourceWebProject>
    <SourceWebPhysicalPath>..\..\code</SourceWebPhysicalPath>
    <ManageRoles>False</ManageRoles>
    <SitecoreDatabase>master</SitecoreDatabase>
    <AssemblyStatus>Exclude</AssemblyStatus>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <BaseTransformFile>GlassV5Item.tt</BaseTransformFile>
    <FieldsForCodeGen>Title,Blob,Shared,Unversioned,Default value,Validation,ValidationText,__Long description,__Short description,__Display name,__Hidden,__Read Only,__Sortorder</FieldsForCodeGen>
    <HeaderTransformFile>GlassV5Header.tt</HeaderTransformFile>
    <CodeGenTargetProject>Pmi.Spx.Feature.Checkout</CodeGenTargetProject>
    <CodeGenFile>SitecoreModels.Generated.cs</CodeGenFile>
    <BaseNamespace>Models</BaseNamespace>
    <EnableCodeGeneration>True</EnableCodeGeneration>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>.\bin\Debug\</OutputPath>
    <RecursiveDeployAction>SitecoreRecycle</RecursiveDeployAction>
    <InstallSitecoreConnector>True</InstallSitecoreConnector>
    <DisableFileDeployment>False</DisableFileDeployment>
    <LightningDeployMode>True</LightningDeployMode>
    <ConnectorTimeoutSeconds>120</ConnectorTimeoutSeconds>
    <EnableValidations>False</EnableValidations>
    <ValidationSettingsFilePath>.\Pmi.Spx.Feature.Checkout.Tds.Master_Debug.xml</ValidationSettingsFilePath>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
    <DebugSymbols>false</DebugSymbols>
    <OutputPath>.\bin\Release\</OutputPath>
    <RecursiveDeployAction>Ignore</RecursiveDeployAction>
  </PropertyGroup>
  <ItemGroup>
    <AssemblyAttributes Include="AssemblyFileVersion">
      <Value>$(AssemblyFileVersion)</Value>
    </AssemblyAttributes>
    <AssemblyAttributes Include="AssemblyVersion">
      <Value>$(AssemblyVersion)</Value>
    </AssemblyAttributes>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\code\Pmi.Spx.Feature.Checkout.csproj">
      <Project>{ec1db1be-b3a9-4ac2-8700-34186e20444a}</Project>
      <Name>Pmi.Spx.Feature.Checkout</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <SitecoreItem Include="sitecore\content.item"><ItemDeployment>NeverDeploy</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\AddressUpdateOptions.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\B2B2C.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\B2B2C\Analytics.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\B2B2C\Analytics\Inspectlet Settings.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\CertificationRenewalAgreementOptions.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\CertificationRenewalAgreementOptions\CertificationRenewalAgreementOptions.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\Checkout.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\Checkout\Cart.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\Checkout\Cart\Checkout Buttons Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\Checkout\Cart\China Store Donation Component Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\Checkout\Cart\Currency Confirmation Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\Checkout\Cart\Donation Component Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\Checkout\Cart\Promocode Component Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\Checkout\Common.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\Checkout\Common\Global Footer Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\Checkout\Common\Inspectlet Settings.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\Checkout\Confirmation.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\Checkout\Confirmation\B2B2C Congratulations Membership.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\Checkout\Confirmation\Congatulations Gift Membership.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\Checkout\Confirmation\Congratulations Certification.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\Checkout\Confirmation\Congratulations Membership.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\Checkout\Confirmation\Course Library Link Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\Checkout\Confirmation\General Order Info.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\Checkout\Confirmation\Go To MyPmi Button Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\Checkout\Confirmation\Return To PmiOrg Button Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\Checkout\Confirmation\Student Membership Confirmation Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\Checkout\Confirmation\Thank You Message.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\Checkout\Convert Quote.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\Checkout\Convert Quote\Please contact Customer Care.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\Checkout\Membership.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\Checkout\Membership\B2B2C Membership Automatic Renewal Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\Checkout\Membership\Local Chapters Search Parameters.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\Checkout\Membership\Membership Automatic Renewal Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\Checkout\Membership\Membership Terms And Conditions Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\Checkout\Membership\Primary Phone Number Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\Checkout\Payment.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\Checkout\Payment\Payment Email Main Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\Checkout\Payment\Voucher Component Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\Checkout\Review.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\Checkout\Review\Discount Component Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\Checkout\Review\Order Review Payment Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\Checkout\Review\Review Total.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\Checkout\Review\ThreeDS Challenge Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\MembershipOverlayOptions.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\MembershipOverlayOptions\Options.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Cart.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Cart\Change Single Membership Data.item"><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization><ExcludeItemFrom></ExcludeItemFrom><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Cart\Checkout Container Cart Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Cart\Checkout Container Payment Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Cart\CheckoutDonationData.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Cart\Continue Shopping Button.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Cart\Currency Confirmation Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Cart\Go to Checkout Button Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Cart\Membership Promo Modal Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Cart\Membership Promo Modal Data\Individual Membership Item.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Cart\Membership Promo Modal Data\Student Membership Item.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Cart\Membership Upsell Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Cart\Membership Upsell New Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Cart\MembershipOptionsData.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Cart\MembershipOptionsData\BrazilStoreData.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Cart\MembershipOptionsData\DefaultStoreData.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Cart\MembershipOptionsData\IndiaStoreData.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Cart\MembershipOptionsData\Options.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Cart\MembershipOptionsData\Options\BrazilMembershipOptions.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Cart\MembershipOptionsData\Options\DefaultMembershipOptions.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Cart\MembershipOptionsData\Options\IndiaMembershipOptions.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Cart\MembershipOptionsData\Options\RegisteredUserOptions.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Cart\Order Summary Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Cart\Order Summary Mobile Accordion Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Cart\Payment Accordion Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Cart\Payment Billing Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Cart\Payment Method PayPal.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Cart\Payment Method Worldpay Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Cart\PresumptiveChaptersData.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Cart\Promocode Component.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Cart\Recommended Products Carousel Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Cart\Request Quote Link Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Cart\Shopping Cart Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Cart\Vouchercode Component.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Checkout.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Checkout\Brazil CPF Code Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Checkout\Checkout Contact Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Checkout\Checkout Order Summary Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Checkout\CNY Payment Disclaimer Short.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Checkout\Order Summary Mobile Accordion Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Checkout\Page Level Loading Modal Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Checkout\Payment Disclaimer Short.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Checkout\Payment Method Adyen Dropin Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Checkout\Payment Method Adyen Pay by Secure Link Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Checkout\Payment Method Alipay Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Checkout\Payment Method Razorpay Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Checkout\Payment Method Razorpay Pay by Secure Link Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Checkout\Payment Terms And Conditions.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Checkout\PaymentMethodWorldpayHostedPaymentData.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Checkout\PayPal Pay Later Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Checkout\PaypalOrderButtonData.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Checkout\Razorpay Submit Button Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Checkout\Saved Payment Cards Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Checkout\Submit Order Button Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Confirmation.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Confirmation\Confirmation Access Items Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Confirmation\Confirmation ContactUs.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Confirmation\Confirmation Container Data.item"><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Confirmation\Confirmation Details Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Confirmation\GetOrder Failed Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Confirmation\Membership Benefits Cards.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Confirmation\Membership Benefits Cards\Access Premium Webinars.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Confirmation\Membership Benefits Cards\Download PMBOK Guide.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Confirmation\Membership Benefits Cards\View the Benefits of Membership.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Confirmation\Membership Benefits Carousel.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Confirmation\Membership Congratulations Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Confirmation\Order Confirmation Summary Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Confirmation\Payment Method Confirmation.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Confirmation\View Invoice Button Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Confirmation\View Invoice Pending Button Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Membership.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Membership\PresumptiveChaptersSearchParameters.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Quote.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Quote\Checkout Container Quote Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Quote\Create Quote Button Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Quote\Order Summary Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Quote\Payment Accordion Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Quote\Payment Billing Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Data\New Checkout\Quote\Promocode Component Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\layout.item"><ItemDeployment>NeverDeploy</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Layouts.item"><ItemDeployment>NeverDeploy</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Layouts\SPX.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Layouts\SPX\Feature.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Layouts\SPX\Feature\Checkout.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Models.item"><ItemDeployment>NeverDeploy</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Models\SPX.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Models\SPX\Feature.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Models\SPX\Feature\Checkout.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings.item"><ItemDeployment>NeverDeploy</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\after-cart-products.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\apply-donation.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\cards-notification-block.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\cart-totals.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\confirmation-membership-benefits.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\congratulations-certification.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\congratulations-gift-membership.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\congratulations-membership.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\course-library-link.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\general-order-info.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\in-between-cart-header-and-cart-products.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\in-between-place-order-button-and-payment-methods.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\membership-automatic-renewal.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\membership-terms-and-conditions.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\New Checkout.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\New Checkout\order-confirmation-get-order-failed.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\New Checkout\order-confirmation-invoice-button.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\New Checkout\order-summary-mobile-accordion-content.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\New Checkout\order-summary-submit-button.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\New Checkout\payment-method-confirmation.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\New Checkout\spx-membership-upsell.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\order-actions.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\order-summary-terms.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\payment-methods-error-block.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\primary-phone-number.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\spx-after-single-membership-cart-item.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\spx-content-col-full.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\spx-content-col-left.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\spx-content-col-right.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\spx-content-col-right-bottom.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\spx-content-col-right-discount.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\spx-content-row-bottom.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\spx-content-row-top.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\spx-payment-billing.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\spx-payment-billing-errors.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\spx-payment-contact.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\spx-payment-methods.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\spx-payment-methods-summary.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\spx-payment-paymentmethods.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\spx-payment-paymentmethods-edit.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\spx-payment-paymentmethods-errors.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\spx-payment-paymentmethods-items.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\spx-payment-paymentmethods-saved.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\spx-place-order-buttons.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\student-membership.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\student-membership-button.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Feature\Checkout\thank-you-message.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings.item"><ItemDeployment>NeverDeploy</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Acoustic Email Tracking.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Add To Cart.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\AddressUpdateSuggestion.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Apply Voucher.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Billing.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Cart Membership.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Cart Totals Row.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Cart Totals.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\CartEnsureAlipayOrderSubmitted.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\CertificationRenewalAgreement.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Checkout Container Two Column.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Checkout Navigation.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Confirmation Container One Column.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Confirmation.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Confirmation\Congratulations Certification.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Confirmation\Congratulations Membership.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Confirmation\CongratulationsGiftMembership.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Confirmation\Course Library Link.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Confirmation\General Order Info.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Confirmation\Google Analytics Event.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Confirmation\Inspectlet.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Confirmation\Order Confirmation Summary.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Confirmation\Order Confirmation.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Confirmation\Student Membership Button.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Confirmation\Student Membership.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Confirmation\Thank You Message.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Convert Quote To Order.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Currency Confirmation.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Discount.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Donation.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Go To Checkout.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Live Person Chat.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Membership Automatic Renewal.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Membership Chapter Errors.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Membership Removal.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Membership Terms And Conditions.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Membership.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\Access Items.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\Add to Cart from URL.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\Apply Donation Amount.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\Brazil CPF Code.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\Cart Lock Error Banner.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\Change Single Membership Chapter.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\Confirmation ContactUs.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\Confirmation Details.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\Confirmation GetOrder Failed.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\Congratulations Membership Component.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\Continue Shopping Button.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\Create Quote Button.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\Go To Checkout Button.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\Membership Benefits Carousel.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\Membership Promo Modal.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\Membership Upsell Modal.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\Membership Upsell New.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\Membership Upsell.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\New Currency Confirmation.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\Order Receipt.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\Order Summary Mobile Accordion.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\Order Summary.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\OrderLinesAdditionalInformation.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\PageLevelLoadingModal.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\Payment Accordion.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\Payment Billing Form.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\Payment Contact Form.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\Payment Method Adyen Pay by Secure Link.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\Payment Method Adyen.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\Payment Method Alipay.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\Payment Method Razorpay Pay by Secure Link.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\Payment Method Razorpay.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\Payment Method Worldpay.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\Payment Methods Edit.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\PaymentMethodConfirmation.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\PaymentMethodPayPal.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\PaymentMethodWorldpayHostedPaymentPages.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\PayPal Pay Later.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\PaypalOrderButton.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\Promocode.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\Razorpay Submit Button.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\RedirectFromAlipayPaymentHandler.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\RedirectFromPayPalHandler.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\RequestQuoteLink.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\Saved Payment Cards.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\Submit Order Button.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\View Invoice Button.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\ViewInvoiceButtonWrapper.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\ViewPendingStatusButtonWrapper.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\New Checkout\Vouchercode.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Order Information.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Order Review Billing Address.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Order Review Payment.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Order Review Total.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Order Review Totals Row.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Order Summary Mobile.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Payment Email.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Payment Main Header.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Payment.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Place Order Button.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Place Order Buttons Placeholder.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\PresumptiveChapters.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Primary Phone Number.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Product Items.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Promocode.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Return To PmiOrg Btn.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Shipping.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Shopping Cart.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Feature\Checkout\Subscription Automatic Renewal.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\system.item"><ItemDeployment>NeverDeploy</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\B2B2C.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\B2B2C\Feature.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\B2B2C\Feature\Checkout.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\B2B2C\Feature\Checkout\Messages.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\B2B2C\Feature\Checkout\Messages\InvalidPONumber.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX\Feature.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX\Feature\Catalog.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX\Feature\Catalog\ApplicationSuspended.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX\Feature\Checkout.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX\Feature\Checkout\CartExperienceTest.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX\Feature\Checkout\CartExperienceTest\ExperienceA.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX\Feature\Checkout\CartExperienceTest\ExperienceB.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX\Feature\Checkout\CartMembership.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX\Feature\Checkout\CartMembership\Chapter Search Placeholder India Store Label.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX\Feature\Checkout\CartMembership\Chapter Search Placeholder Label.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX\Feature\Checkout\CartMembership\SingleMembershipNoChapters.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX\Feature\Checkout\CartMembership\Suggested For You.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX\Feature\Checkout\Currency Preferences.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX\Feature\Checkout\Currency Preferences\Cannot Process.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX\Feature\Checkout\Subscription Frequency.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX\Feature\Checkout\Subscription Frequency\Monthly.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX\Feature\NewCheckoutFlow.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX\Feature\NewCheckoutFlow\ShoppingCart.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX\Feature\NewCheckoutFlow\ShoppingCart\Errors.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX\Feature\NewCheckoutFlow\ShoppingCart\Errors\InvalidPromoCode.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX\Feature\NewCheckoutFlow\ShoppingCart\ItemSavings.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX\Feature\NewCheckoutFlow\ShoppingCart\MultiItemsInCart.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX\Feature\NewCheckoutFlow\ShoppingCart\NoItemsInCart.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX\Feature\NewCheckoutFlow\ShoppingCart\OneItemInCart.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX\Feature\NewCheckoutFlow\ShoppingCart\PromoCodeApplied.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX\Feature\NewCheckoutFlow\ShoppingCart\RemoveLink.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX\Feature\NewCheckoutFlow\ShoppingCart\SingleMembership.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX\Feature\NewCheckoutFlow\ShoppingCart\SingleMembership\SelectChapterButtonLabel.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX\Feature\NewCheckoutFlow\ShoppingCart\SingleMembership\SelectedButtonLabel.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX\Feature\NewCheckoutFlow\ShoppingCart\SingleMembership\SelectingButtonLabel.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX\Feature\NewCheckoutFlow\ShoppingCart\Subtotal.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX\Feature\NewCheckoutFlow\ShoppingCart\VoucherCodeApplied.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX\Foundation.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX\Foundation\Checkout.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX\Foundation\Checkout\Messages.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX\Foundation\Checkout\Messages\PaypalUrlRequestErrorMessage.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Settings.item"><ItemDeployment>NeverDeploy</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Settings\SPX.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Settings\SPX\Feature.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Settings\SPX\Feature\Checkout.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates.item"><ItemDeployment>NeverDeploy</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\AddressChangeOptions.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\AddressChangeOptions\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\AddressChangeOptions\Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\AddressChangeOptions\Data\AddressUpdateLink.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\AddressChangeOptions\Data\UpdateAddressSuggestionText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Apply Voucher.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Apply Voucher\Apply Voucher Folder.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Apply Voucher\Apply Voucher Folder\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Apply Voucher\Voucher Component Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Apply Voucher\Voucher Component Data\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Apply Voucher\Voucher Component Data\Voucher Text Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Apply Voucher\Voucher Component Data\Voucher Text Data\Agreement Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Apply Voucher\Voucher Component Data\Voucher Text Data\Applied Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Apply Voucher\Voucher Component Data\Voucher Text Data\Apply Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Apply Voucher\Voucher Component Data\Voucher Text Data\Learn Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Apply Voucher\Voucher Component Data\Voucher Text Data\Placeholder Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Apply Voucher\Voucher Component Data\Voucher Text Data\Remove Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Apply Voucher\Voucher Component Data\Voucher Text Data\Tooltip Description.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Apply Voucher\Voucher Component Data\Voucher Text Data\Tooltip Header.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Apply Voucher\Voucher Component Data\Voucher Text Data\Tooltip Link.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Apply Voucher\Voucher Component Data\Voucher Text Data\Tooltip Linkname.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Cart Membership Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Cart Membership Data\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Cart Membership Data\Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Cart Membership Data\Data\MembershipSection.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Cart Membership Data\Data\RegisteredSection.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Cart Membership Data\Data\SheerIDParameters.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Cart Membership Data\Data\StudentMembershipDisclaimerText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Cart Membership Data\SheerIDData.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Cart Membership Data\SheerIDData\Email.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Cart Membership Data\SheerIDData\SheerIdUrl.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Cart Totals Row Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Cart Totals Row Data\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Cart Totals Row.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Cart Totals Row\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Cart Totals Row\Main.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Cart Totals Row\Main\SubText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Cart Totals Row\Main\Title.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\CartMembershipOptions.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\CartMembershipOptions\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\CartMembershipOptions\Option Details.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\CartMembershipOptions\Option Details\ApplicationFeeText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\CartMembershipOptions\Option Details\ListItems.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\CartMembershipOptions\Option Details\OptionsLink.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\CartMembershipOptions\Option Details\StandardTariffText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\CartMembershipOptions\Option Details\SubTitle.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\CartMembershipOptions\Option Details\Title.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\CertificationRenewalAgreementOptions.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\CertificationRenewalAgreementOptions\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\CertificationRenewalAgreementOptions\Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\CertificationRenewalAgreementOptions\Data\AgreeIsChecked.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\CertificationRenewalAgreementOptions\Data\AgreementText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Checkout Buttons Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Checkout Buttons Data\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Checkout Buttons Data\Button Texts.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Checkout Buttons Data\Button Texts\Continue Shopping Link.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Checkout Buttons Data\Button Texts\Go to Checkout Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Checkout Buttons Data\Button Texts\Go to Shopping Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Checkout Navigation Step.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Checkout Navigation Step\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Checkout Navigation Step\Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Checkout Navigation Step\Data\Step Id.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Checkout Navigation Step\Data\Step Link.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Checkout Navigation Step\Data\Step Title.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Checkout Navigation Steps.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Checkout Navigation Steps\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Checkout Navigation Steps\Checkout Flows.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Checkout Navigation Steps\Checkout Flows\Mixed products.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Checkout Navigation Steps\Checkout Flows\Virtual products.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Checkout Step Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Checkout Step Data\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Checkout Step Data\Checkout Texts.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Checkout Step Data\Checkout Texts\Button Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Checkout Step Data\Checkout Texts\Title.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Congatulations Gift Membership.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Congatulations Gift Membership\General.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Congatulations Gift Membership\General\Heading.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Congatulations Gift Membership\General\Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Congratulations Certification.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Congratulations Certification\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Congratulations Certification\Congratulations Certification.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Congratulations Certification\Congratulations Certification\Congratulations Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Congratulations Membership.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Congratulations Membership\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Congratulations Membership\Congratulations Membership.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Congratulations Membership\Congratulations Membership\Description.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Congratulations Membership\Congratulations Membership\MembershipDescriptionWithoutRenewalStatement.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Congratulations Membership\Congratulations Membership\Student Description.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Congratulations Membership\Congratulations Membership\Title.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\convertquote.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\convertquote\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\convertquote\Common.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\convertquote\Common\componentImage.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\convertquote\Problem Description Block.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\convertquote\Problem Description Block\problemDescriptionText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Course Library Link.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Course Library Link\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Course Library Link\Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Course Library Link\Data\Link Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Course Library Link\Settings.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Course Library Link\Settings\Eligible Product Types to Show Link.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment><CodeGenData>name=EligibleProductTypesToShowLink</CodeGenData></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Currency Confirmation.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Currency Confirmation\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Currency Confirmation\Currency Confirmation Modal.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Currency Confirmation\Currency Confirmation Modal\Checkout Button Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Currency Confirmation\Currency Confirmation Modal\Pilot Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Currency Confirmation\Currency Confirmation Modal\Preference Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Currency Confirmation\Currency Confirmation Modal\Preference Texts.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Currency Confirmation\Currency Confirmation Modal\Title.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Discount Component Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Discount Component Data\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Discount Component Data\Discount Titles.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Discount Component Data\Discount Titles\Promo Title.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Discount Component Data\Discount Titles\Voucher Title.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Donation Component Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Donation Component Data\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Donation Component Data\Donation Component Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Donation Component Data\Donation Component Data\Add To Cart Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Donation Component Data\Donation Component Data\Donate To Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Donation Component Data\Donation Component Data\Donation Made Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Donation Component Data\Donation Component Data\Learn More Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Donation Component Data\Donation Component Data\Placeholder Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\General Order Info.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\General Order Info\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\General Order Info\General Order Info.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\General Order Info\General Order Info\Banner Info For Pending Open Orders.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\General Order Info\General Order Info\Email Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\General Order Info\General Order Info\General Info For Pending Open Orders.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\General Order Info\General Order Info\Open Quote Button Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\General Order Info\General Order Info\Print Invoice Button Link.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\General Order Info\General Order Info\Print Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\General Order Info\General Order Info\Review Order Status.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\General Order Info\General Order Info\Standard Banner Info.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Global Footer Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Global Footer Data\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Global Footer Data\Global Footer Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Global Footer Data\Global Footer Data\Advertising Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Global Footer Data\Global Footer Data\Copyright Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Global Footer Data\Global Footer Data\Privacy Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Global Footer Data\Global Footer Data\Processing Country.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Global Footer Data\Global Footer Data\Sitemap Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Global Footer Data\Global Footer Data\Terms and Conditions Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Global Footer Data\Global Footer Data\Terms of Use Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Membership Automatic Renewal Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Membership Automatic Renewal Data\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Membership Automatic Renewal Data\Membership Automatic Renewal.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Membership Automatic Renewal Data\Membership Automatic Renewal\Header Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Membership Automatic Renewal Data\Membership Automatic Renewal\Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Membership Terms And Conditions Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Membership Terms And Conditions Data\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Membership Terms And Conditions Data\Membership Terms And Conditions.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Membership Terms And Conditions Data\Membership Terms And Conditions\Agree Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Membership Terms And Conditions Data\Membership Terms And Conditions\Optional Agree Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Membership Terms And Conditions Data\Membership Terms And Conditions\Retiree Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Membership Terms And Conditions Data\Membership Terms And Conditions\Title.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Order Review Payment.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Order Review Payment\Order Review Payment Folder.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Order Review Payment\Order Review Payment Folder\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Order Review Payment\Order Review Payment.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Order Review Payment\Order Review Payment\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Order Review Payment\Order Review Payment\Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Order Review Payment\Order Review Payment\Data\BillingAddress.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Order Review Payment\Order Review Payment\Data\PaymentMethods.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Order Review Payment\Order Review Payment\Data\PurchaseOrder.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Order Review Payment\Order Review Payment\Data\VoucherAsPayment.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Payment Email Main Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Payment Email Main Data\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Payment Email Main Data\Component Texts.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Payment Email Main Data\Component Texts\Component Header.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\PaymentAddress.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\PaymentAddress\PaymentAddress.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\PaymentAddress\PaymentAddress\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\PaymentAddress\PaymentAddress\Payment.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\PaymentAddress\PaymentAddress\Payment\Address Label.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\PaymentAddress\PaymentAddressFolder.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\PaymentAddress\PaymentAddressFolder\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Primary Phone Number Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Primary Phone Number Data\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Primary Phone Number Data\Primary Phone Number.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Primary Phone Number Data\Primary Phone Number\Area Code.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Primary Phone Number Data\Primary Phone Number\Country Code.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Primary Phone Number Data\Primary Phone Number\Ext.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Primary Phone Number Data\Primary Phone Number\Number.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Primary Phone Number Data\Primary Phone Number\Phone Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Primary Phone Number Data\Primary Phone Number\Phone Title.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Primary Phone Number Data\Primary Phone Number\Phone Tooltip Link.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Primary Phone Number Data\Primary Phone Number\Phone Tooltip Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Primary Phone Number Data\Primary Phone Number\Phone Tooltip Title.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Promocode Component Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Promocode Component Data\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Promocode Component Data\Promocode Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Promocode Component Data\Promocode Data\Apply Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Promocode Component Data\Promocode Data\Promocode Placeholder.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Return To PmiOrg Button.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Return To PmiOrg Button\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Return To PmiOrg Button\Return To PmiOrg Button Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Return To PmiOrg Button\Return To PmiOrg Button Data\Button Link.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Return To PmiOrg Button\Return To PmiOrg Button Data\Button Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Review Total.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Review Total\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Review Total\Content.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Review Total\Content\Purchase.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Review Total\Content\Total.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Review Total\Content\TotalSaving.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Student Membership Confirmation Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Student Membership Confirmation Data\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Student Membership Confirmation Data\Important Note.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Student Membership Confirmation Data\Important Note\Note Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Student Membership Confirmation Data\Important Note\Note Title.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Student Membership Confirmation Data\Student Membership Confirmation.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Student Membership Confirmation Data\Student Membership Confirmation\Item Box Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Student Membership Confirmation Data\Student Membership Confirmation\Item Box Title Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Student Membership Confirmation Data\Student Membership Confirmation\Submit Button Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Student Membership Confirmation Data\Student Membership Confirmation\Title.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Thank You Message.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Thank You Message\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Thank You Message\Thank You Message.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Thank You Message\Thank You Message\Order Processing Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Thank You Message\Thank You Message\Thank Inquiry Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Data Sources\Thank You Message\Thank You Message\Thank Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Local Chapters Search Parameters Template.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Local Chapters Search Parameters Template\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Local Chapters Search Parameters Template\Search Criteria.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Local Chapters Search Parameters Template\Search Criteria\distance.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Local Chapters Search Parameters Template\Search Criteria\maxResult.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\AutoSaveUserData.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\AutoSaveUserData\Field Labels.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\AutoSaveUserData\Field Labels\AutoSaveText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Brazil CPF Code Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Brazil CPF Code Data\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Brazil CPF Code Data\Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Brazil CPF Code Data\Data\Add Cpf Button Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Brazil CPF Code Data\Data\Allow CNPJ.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Brazil CPF Code Data\Data\Cpf Field Label.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Brazil CPF Code Data\Data\Cpf Field Placeholder.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Brazil CPF Code Data\Data\Expand Link Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Brazil CPF Code Data\Data\Heading Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Brazil CPF Code Data\Validation Messages.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Brazil CPF Code Data\Validation Messages\Cpf Invalid Message.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Brazil CPF Code Data\Validation Messages\Cpf Required Message.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Cart Folder.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Cart Folder\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Change Single Membership Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Change Single Membership Data\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Change Single Membership Data\Default State.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Change Single Membership Data\Default State\Accordion Button Title.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Change Single Membership Data\Default State\Header Description.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Change Single Membership Data\Default State\Header Title.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Change Single Membership Data\Expanded State.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Change Single Membership Data\Expanded State\Expanded Description.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Change Single Membership Data\Expanded State\Expanded Title.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Checkout Folder.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Checkout Folder\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\CheckoutContainerData.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\CheckoutContainerData\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\CheckoutContainerData\data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\CheckoutContainerData\data\BackButtonText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\CheckoutContainerData\data\GoToCartLink.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\CheckoutContainerData\data\Heading.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\CheckoutContainerData\data\ShowBackButton.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\CheckoutDonationData.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\CheckoutDonationData\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\CheckoutDonationData\Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\CheckoutDonationData\Data\DonationAddToCartText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\CheckoutDonationData\Data\DonationHeader.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\CheckoutDonationData\Data\DonationMadeSubText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\CheckoutDonationData\Data\donationMadeText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\CheckoutDonationData\Data\DonationOtherAmountPlaceholderText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\CheckoutDonationData\Data\DonationSubHeader.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Confirmation Page Footer Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Confirmation Payment Method Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Confirmation Payment Method Data\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Confirmation Payment Method Data\data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Confirmation Payment Method Data\data\imageLookUp.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ConfirmationAccessItemsData.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ConfirmationAccessItemsData\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ConfirmationAccessItemsData\Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ConfirmationAccessItemsData\Data\CoursesButtonLink.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ConfirmationAccessItemsData\Data\CoursesText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ConfirmationAccessItemsData\Data\DashboardButtonLink.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ConfirmationAccessItemsData\Data\DashboardText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ConfirmationAccessItemsData\Data\EligibleProductTypesToViewCourses.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ConfirmationAccessItemsData\Data\Header.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ConfirmationContainerData.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ConfirmationContainerData\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ConfirmationContainerData\Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ConfirmationContainerData\Data\PendingOrderTitle.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ConfirmationContainerData\Data\QuoteTitle.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ConfirmationContainerData\Data\Title.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ConfirmationDetailsData.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ConfirmationDetailsData\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ConfirmationDetailsData\Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ConfirmationDetailsData\Data\EmailSentText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ConfirmationDetailsData\Data\GeneralBannerInfoForPendingOpenOrders.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ConfirmationDetailsData\Data\NotRecievedEmailText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ConfirmationDetailsData\Data\QuoteSentText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ConfirmationFolder.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ConfirmationFolder\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ContactUsNow.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ContactUsNow\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ContactUsNow\Details.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ContactUsNow\Details\ContactUsLink.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ContactUsNow\Details\Header.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ContactUsNow\Details\SubText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Continue Shopping Button.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Continue Shopping Button\data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Continue Shopping Button\data\continue shopping button text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Continue Shopping Button\data\continue shopping link.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Create Quote Button Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Create Quote Button Data\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Create Quote Button Data\Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Create Quote Button Data\Data\ButtonText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Create Quote Button Data\Data\Confirmation Page Url.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\GetOrder Failed Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\GetOrder Failed Data\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\GetOrder Failed Data\Order Not Found Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\GetOrder Failed Data\Order Not Found Data\Header.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\GetOrder Failed Data\Order Not Found Data\Message Body.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Go To Checkout Button Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Go To Checkout Button Data\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Go To Checkout Button Data\Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Go To Checkout Button Data\Data\ButtonText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Go To Checkout Button Data\Data\Checkout Page Url.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Go To Checkout Button Data\Data\LocalCurrency.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Membership Promo Modal Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Membership Promo Modal Data\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Membership Promo Modal Data\Buttons data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Membership Promo Modal Data\Buttons data\Add to Cart Button Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Membership Promo Modal Data\Buttons data\Cancel Button Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Membership Promo Modal Data\Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Membership Promo Modal Data\Data\List Items.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Membership Promo Modal Data\Data\Membership Items.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Membership Promo Modal Data\Data\Options Link.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Membership Promo Modal Data\Data\Student Membership Validation Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Membership Promo Modal Data\Data\Title.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Membership Promo Modal Data\Data\Total Savings Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Membership Promo Modal Data\Sheer ID Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Membership Promo Modal Data\Sheer ID Data\SheerIDParameters.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\MembershipBenefitsCardData.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\MembershipBenefitsCardData\Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\MembershipBenefitsCardData\Data\HeadingText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\MembershipBenefitsCardData\Data\Link.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\MembershipBenefitsCardData\Data\Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\MembershipBenefitsCardDataFolder.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\MembershipBenefitsCardDataFolder\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\MembershipBenefitsCarouselData.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\MembershipBenefitsCarouselData\Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\MembershipBenefitsCarouselData\Data\Cards.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\MembershipCongratulationsData.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\MembershipCongratulationsData\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\MembershipCongratulationsData\Details.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\MembershipCongratulationsData\Details\GiftMembershipDescription.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\MembershipCongratulationsData\Details\GiftMembershipTitle.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\MembershipCongratulationsData\Details\MembershipDescription.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\MembershipCongratulationsData\Details\PurchaseTermsWithAutoRenewalStatement.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\MembershipCongratulationsData\Details\PurchaseTermsWithoutAutoRenewStatement.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\MembershipCongratulationsData\Details\StudentMembershipDescription.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\MembershipCongratulationsData\Details\Title.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\MembershipUpsellData.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\MembershipUpsellData\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\MembershipUpsellData\Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\MembershipUpsellData\Data\ActionText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\MembershipUpsellData\Data\Body.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\MembershipUpsellData\Data\BodyNoDiscount.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\MembershipUpsellData\Data\Title.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\MembershipUpsellData\Data\TitleNoDiscount.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\MembershipUpsellNewData.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\MembershipUpsellNewData\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\MembershipUpsellNewData\Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\MembershipUpsellNewData\Data\ActionText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\MembershipUpsellNewData\Data\Body.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\MembershipUpsellNewData\Data\BodyNoDiscount.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Order Confirmation Summary Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Order Confirmation Summary Data\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Order Confirmation Summary Data\Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Order Confirmation Summary Data\Data\Header.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Order Confirmation Summary Data\Data\Order discount.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Order Confirmation Summary Data\Data\Order total.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Order Confirmation Summary Data\Data\Quote Header.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Order Confirmation Summary Data\Data\Subtotal.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Order Confirmation Summary Data\Data\Taxes.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Order Confirmation Summary Data\Data\Voucher discount.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Order Summary Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Order Summary Data\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Order Summary Data\data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Order Summary Data\data\Checkout Button Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Order Summary Data\data\Estimated Tax.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Order Summary Data\data\Estimated Total.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Order Summary Data\data\Go To Checkout Url.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Order Summary Data\data\GoToCartLink.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Order Summary Data\data\Order Discount.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Order Summary Data\data\Subtotal.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Order Summary Data\data\Title.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Order Summary Data\data\Total Cart Discount.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Order Summary Data\data\Voucher Order Discount.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Order Summary Mobile Accordion Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Order Summary Mobile Accordion Data\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Order Summary Mobile Accordion Data\Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Order Summary Mobile Accordion Data\Data\Hide Button Title.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Order Summary Mobile Accordion Data\Data\Show Button Title.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PageLevelLoadingModalData.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PageLevelLoadingModalData\Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PageLevelLoadingModalData\Data\Message.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PageLevelLoadingModalData\Data\Title.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Contact Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Contact Data\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Contact Data\content.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Contact Data\content\Dialing Code Label.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Contact Data\content\email-field-label.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Contact Data\content\EmailFieldPlaceholderText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Contact Data\content\name-change-disclaimer.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Contact Data\content\Phone Character Limit Message.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Contact Data\content\Phone Number Label.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Contact Data\content\Phone Number Placeholder Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Contact Data\content\Phone Section Label Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Contact Data\content\submit-button-text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Contact Data\ToolTips.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Contact Data\ToolTips\EmailAddressToolTipMessage.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Contact Data\ToolTips\PhoneNumberToolTipMessage.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Contact Data\Validation Messages.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Contact Data\Validation Messages\DialingCountryCodeRequiredMessage.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Contact Data\Validation Messages\EmailInvalidMessage.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Contact Data\Validation Messages\EmailRequiredMessage.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Contact Data\Validation Messages\PhoneRequiredMessage.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Contact Data\Validation Messages\PhoneValidMessage.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Method Adyen Dropin Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Method Adyen Dropin Data\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Method Adyen Dropin Data\Messages.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Method Adyen Dropin Data\Messages\MembershipAndSubscriptionAutoRenewMessage.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Method Adyen Dropin Data\Messages\MembershipAutoRenewMessage.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Method Adyen Dropin Data\Messages\SubscriptionAutoRenewMessage.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Method Adyen Dropin Data\Page Loader.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Method Adyen Dropin Data\Page Loader\Page Loader Body.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Method Adyen Dropin Data\Page Loader\Page Loader Title.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Method Adyen Dropin Data\PaymentSettings.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Method Adyen Dropin Data\PaymentSettings\AllowedPaymentMethodsMapping.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Method Adyen Dropin Data\PaymentSettings\Settings.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Method Adyen Dropin Data\PaymentSettings\TranslationOverrides.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Method Adyen Pay by Secure Link Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Method Adyen Pay by Secure Link Data\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Method Alipay Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Method Alipay Data\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Method Alipay Data\Field Labels.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Method Alipay Data\Field Labels\RedirectInformationMessage.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Method Razorpay Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Method Razorpay Data\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Method Razorpay Data\Credit Card Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Method Razorpay Data\Credit Card Data\CreditCardLabel.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Method Razorpay Data\Credit Card Data\CreditCardSubtext.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Method Razorpay Data\Messages.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Method Razorpay Data\Messages\MembershipAndSubscriptionAutoRenewMessage.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Method Razorpay Data\Netbanking Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Method Razorpay Data\Netbanking Data\NetbankingLabel.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Method Razorpay Data\Netbanking Data\NetbankingSubtext.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Method Razorpay Data\UPI Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Method Razorpay Data\UPI Data\UpiLabel.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Method Razorpay Data\UPI Data\UpiLimitAmount.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Method Razorpay Data\UPI Data\UpiSubtext.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Method Razorpay Pay by Secure Link Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Payment Method Razorpay Pay by Secure Link Data\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentAccordionData.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentAccordionData\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentAccordionData\BillingSection.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentAccordionData\BillingSection\BillingExpandLinkText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentAccordionData\BillingSection\BillingHeadingText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentAccordionData\ContactSection.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentAccordionData\ContactSection\ContactEmailTooltipText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentAccordionData\ContactSection\ContactExpandLinkText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentAccordionData\ContactSection\ContactHeadingText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentAccordionData\ContactSection\ContactPhoneToolTipText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentAccordionData\PaymentMethodSection.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentAccordionData\PaymentMethodSection\PaymentMethodExpandLinkText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentAccordionData\PaymentMethodSection\PaymentMethodHeadingText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentAccordionData\PaymentMethodSection\PaymentSecureInformationText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentBillingData.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentBillingData\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentBillingData\Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentBillingData\Data\AddAddressButtonText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentBillingData\Data\EditButtonText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentBillingData\Data\HeadingText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentBillingData\Data\UseAddressButtonText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentBillingData\Field Labels.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentBillingData\Field Labels\AddressLine1Label.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentBillingData\Field Labels\AddressLine1Placeholder.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentBillingData\Field Labels\AddressLine2Label.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentBillingData\Field Labels\AddressLine2Placeholder.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentBillingData\Field Labels\CancelButtonText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentBillingData\Field Labels\CityLabel.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentBillingData\Field Labels\CityPlaceholder.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentBillingData\Field Labels\CountryLabel.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentBillingData\Field Labels\CountryPlaceholder.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentBillingData\Field Labels\LoadingText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentBillingData\Field Labels\PostalCodeLabel.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentBillingData\Field Labels\PostalCodePlaceholder.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentBillingData\Field Labels\SaveAsPrimaryLabel.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentBillingData\Field Labels\StateProvinceLabel.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentBillingData\Field Labels\StateProvincePlaceholder.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentBillingData\Field Labels\SubmitButtonText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentBillingData\Validation Messages.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentBillingData\Validation Messages\AddressLine1Empty.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentBillingData\Validation Messages\CityEmpty.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentBillingData\Validation Messages\CountryEmpty.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentBillingData\Validation Messages\PostalCodeEmpty.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentBillingData\Validation Messages\RequiredFieldsMessage.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentBillingData\Validation Messages\StateProvinceEmpty.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentMethodData.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentMethodData\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentMethodData\Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentMethodData\Data\Description.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentMethodData\Field Labels.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentMethodData\Field Labels\Cancel Button Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentMethodData\Field Labels\Submit Button Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentMethodPaypalData.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentMethodPaypalData\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentMethodPaypalData\Field Labels.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentMethodPaypalData\Field Labels\RedirectInformationMessage.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentMethodPaypalData\Field Labels\SavePaymentDescription.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentMethodPaypalData\Field Labels\SavePaymentSubTitle.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentMethodWorldpayData.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentMethodWorldpayData\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentMethodWorldpayData\Field Labels.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentMethodWorldpayData\Field Labels\CardHolderNameLabel.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentMethodWorldpayData\Field Labels\CardHolderNamePlaceholder.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentMethodWorldpayData\Field Labels\CardNumberLabel.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentMethodWorldpayData\Field Labels\CardNumberPlaceholder.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentMethodWorldpayData\Field Labels\ExpirationMonthLabel.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentMethodWorldpayData\Field Labels\ExpirationMonthPlaceholder.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentMethodWorldpayData\Field Labels\ExpirationYearLabel.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentMethodWorldpayData\Field Labels\ExpirationYearPlaceholder.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentMethodWorldpayData\Field Labels\SaveAsDefaultLabel.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentMethodWorldpayData\Field Labels\SaveAsDefaultSubText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentMethodWorldpayData\Field Labels\SecurityCodeLabel.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentMethodWorldpayData\Field Labels\SecurityCodePlaceholder.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentMethodWorldpayData\Field Labels\SecurityCodeTooltipText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentMethodWorldpayData\Validation Messages.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentMethodWorldpayData\Validation Messages\CardHolderNameEmpty.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentMethodWorldpayData\Validation Messages\CardNumberInvalid.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentMethodWorldpayData\Validation Messages\ExpirationMonthEmpty.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentMethodWorldpayData\Validation Messages\ExpirationMonthYearExpired.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentMethodWorldpayData\Validation Messages\ExpirationYearEmpty.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentMethodWorldpayData\Validation Messages\SecurityCodeEmpty.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentMethodWorldpayHostedPayment.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PaymentMethodWorldpayHostedPayment\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PayPal Pay Later Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PayPal Pay Later Data\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PayPal Pay Later Data\Settings.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PayPal Pay Later Data\Settings\AdyenSetting.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PayPalPlaceOrderButton.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PayPalPlaceOrderButton\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PayPalPlaceOrderButton\OrderProcessingState.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PayPalPlaceOrderButton\OrderProcessingState\OrderProcessingStateBody.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PayPalPlaceOrderButton\OrderProcessingState\OrderProcessingStateTitle.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PayPalPlaceOrderButton\Redirect Processing Errors.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PayPalPlaceOrderButton\Redirect Processing Errors\AuthorizationCancelledErrorMessage.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PayPalPlaceOrderButton\Redirect Processing Errors\AuthorizationFailedErrorMessage.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PayPalPlaceOrderButton\Redirect Processing Errors\AuthorizationFallbackErrorMessage.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PayPalPlaceOrderButton\RedirectProcessingState.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PayPalPlaceOrderButton\RedirectProcessingState\RedirectWaitingStateBody.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PayPalPlaceOrderButton\RedirectProcessingState\RedirectWaitingStateTitle.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PresumptiveChaptersData.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PresumptiveChaptersData\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PresumptiveChaptersData\Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PresumptiveChaptersData\Data\AccordionButtonText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PresumptiveChaptersData\Data\AccordionInnerHeader.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PresumptiveChaptersData\Data\AccordionSubtitle.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PresumptiveChaptersData\Data\CloseButtonText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PresumptiveChaptersData\Data\ExploreMoreChaptersButtonText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PresumptiveChaptersData\Data\ExploreMoreChaptersUrl.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PresumptiveChaptersData\Data\Header Title.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\PresumptiveChaptersData\Data\Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Promo Modal Membership Item Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Promo Modal Membership Item Data\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Promo Modal Membership Item Data\Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Promo Modal Membership Item Data\Data\Membership Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Promo Modal Membership Item Data\Data\ProductSku.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Promocode Component.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Promocode Component\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Promocode Component\Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Promocode Component\Data\Button Label.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Promocode Component\Data\Placeholder.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Promocode Component\Data\Title.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Promocode Component\Data\Tooltip Message.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Quote Folder.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Quote Folder\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Razorpay Submit Button Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Razorpay Submit Button Data\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Razorpay Submit Button Data\Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Razorpay Submit Button Data\Data\ButtonText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Razorpay Submit Button Data\Data\Settings.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Razorpay Submit Button Data\Overlay Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Razorpay Submit Button Data\Overlay Data\OverlayBody.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Razorpay Submit Button Data\Overlay Data\OverlayTitle.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\RequestQuoteLinkData.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\RequestQuoteLinkData\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\RequestQuoteLinkData\Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\RequestQuoteLinkData\Data\GoToCheckoutLink.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\RequestQuoteLinkData\Data\LocalCurrency.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\RequestQuoteLinkData\Data\TooltipMessage.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\SavedPaymentCardsData.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\SavedPaymentCardsData\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\SavedPaymentCardsData\Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\SavedPaymentCardsData\Data\HeadingText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\SavedPaymentCardsData\Field Labels.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\SavedPaymentCardsData\Field Labels\AddNewButtonText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\SavedPaymentCardsData\Field Labels\EditButtonText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\SavedPaymentCardsData\Field Labels\SubmitButtonText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ShoppingCartData.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ShoppingCartData\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ShoppingCartData\EmptyCart.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ShoppingCartData\EmptyCart\EmptyCartBody.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ShoppingCartData\EmptyCart\EmptyCartHeading.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ShoppingCartData\EmptyCart\EmptyCartNavigateToLink.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Submit Order Button Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Submit Order Button Data\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Submit Order Button Data\Adyen Pay by Secure Link Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Submit Order Button Data\Adyen Pay by Secure Link Data\Adyen Pay by Secure Link Button Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Submit Order Button Data\Alipay Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Submit Order Button Data\Alipay Data\Alipay Button Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Submit Order Button Data\Alipay Data\Alipay Order Processing State Body.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Submit Order Button Data\Alipay Data\Alipay Order Processing State Title.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Submit Order Button Data\Alipay Data\Alipay Redirect Waiting State Body.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Submit Order Button Data\Alipay Data\Alipay Redirect Waiting State Title.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Submit Order Button Data\Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Submit Order Button Data\Data\ButtonText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Submit Order Button Data\Data\Confirmation Page Url.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Submit Order Button Data\Order Processing Messages.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Submit Order Button Data\Order Processing Messages\OrderProcessingBody.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Submit Order Button Data\Order Processing Messages\OrderProcessingTitle.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Submit Order Button Data\Razorpay Pay by Secure Link Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Submit Order Button Data\Razorpay Pay by Secure Link Data\Razorpay Pay by Secure Link Button Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Submit Order Button Data\Worldpay Hosted Payment Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Submit Order Button Data\Worldpay Hosted Payment Data\WorldpayHostedPaymentPlaceOrderText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ViewInvoiceButtonData.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ViewInvoiceButtonData\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ViewInvoiceButtonData\Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ViewInvoiceButtonData\Data\CheckOrderStatusLink.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ViewInvoiceButtonData\Data\ViewInvoiceButtonLink.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ViewInvoicePendingButtonData.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ViewInvoicePendingButtonData\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ViewInvoicePendingButtonData\Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ViewInvoicePendingButtonData\Data\CheckOrderStatusLink.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ViewInvoicePendingButtonData\Data\ViewInvoiceButtonLink.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\ViewInvoicePendingButtonData\Data\ViewOrderStatustext.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Vouchercode Component.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Vouchercode Component\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Vouchercode Component\Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Vouchercode Component\Data\Button Label.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Vouchercode Component\Data\Placeholder.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Vouchercode Component\Data\Title.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Data Sources\Vouchercode Component\Data\Tooltip Message.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Rendering Parameters.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Rendering Parameters\OrderSummaryRenderingParameters.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Rendering Parameters\OrderSummaryRenderingParameters\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Rendering Parameters\OrderSummaryRenderingParameters\Custom Properties.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Rendering Parameters\OrderSummaryRenderingParameters\Custom Properties\DisplayCartLines.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Rendering Parameters\OrderSummaryRenderingParameters\Custom Properties\HideGoToCheckoutButton.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Rendering Parameters\PaymentAccordionRenderingParameters.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Rendering Parameters\PaymentAccordionRenderingParameters\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Rendering Parameters\PaymentAccordionRenderingParameters\Parameters.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Rendering Parameters\PaymentAccordionRenderingParameters\Parameters\HidePaymentSection.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\New Checkout\Rendering Parameters\PaymentAccordionRenderingParameters\Parameters\IsQuotePage.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Cart Totals Rendering Parameters.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Cart Totals Rendering Parameters\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Cart Totals Rendering Parameters\Parameters.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Cart Totals Rendering Parameters\Parameters\Cart Totals Data Source.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Cart Totals Rendering Parameters\Parameters\Show Taxes.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Cart Totals Row Rendering Parameters.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Cart Totals Row Rendering Parameters\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Cart Totals Row Rendering Parameters\Parameters.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Cart Totals Row Rendering Parameters\Parameters\Field.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Cart Totals Row Rendering Parameters\Parameters\Is Bold.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Cart Totals Row Rendering Parameters\Parameters\Is Red.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Cart Totals Row Rendering Parameters\Parameters\UseLocalCurrencyTotal.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\CartMembershipRenderingParameters.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\CartMembershipRenderingParameters\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\CartMembershipRenderingParameters\Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\CartMembershipRenderingParameters\ExperienceTest.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\CartMembershipRenderingParameters\ExperienceTest\isABTestEnabled.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Checkout Navigation Rendering Parameters.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Checkout Navigation Rendering Parameters\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Checkout Navigation Rendering Parameters\Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Checkout Navigation Rendering Parameters\Data\Hide Navigation.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Donation Rendering Parameters.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Donation Rendering Parameters\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Donation Rendering Parameters\Settings.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Donation Rendering Parameters\Settings\Donation Data Source.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Donation Rendering Parameters\Settings\Fixed Amounts.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Donation Rendering Parameters\Settings\Readonly.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Google Analytics Event Rendering Parameters.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Google Analytics Event Rendering Parameters\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Google Analytics Event Rendering Parameters\Parameters.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Google Analytics Event Rendering Parameters\Parameters\Event Body.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Google Analytics Event Rendering Parameters\Parameters\Event type.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Live Person Chat Rendering Parameters.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Live Person Chat Rendering Parameters\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Live Person Chat Rendering Parameters\Parameters.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Live Person Chat Rendering Parameters\Parameters\Chat Script Body.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Live Person Chat Rendering Parameters\Parameters\livePersonId.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Order Summary Mobile Rendering Parameters.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Order Summary Mobile Rendering Parameters\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Order Summary Mobile Rendering Parameters\Parameters.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Order Summary Mobile Rendering Parameters\Parameters\Header Text.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Order Summary Mobile Rendering Parameters\Parameters\Show Button.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Product Items Rendering Parameters.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Product Items Rendering Parameters\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Product Items Rendering Parameters\Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Product Items Rendering Parameters\Data\CartHeaderText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Product Items Rendering Parameters\Data\HideMemberPrice.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Product Items Rendering Parameters\Data\HidePriceInfoWhenMember.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Product Items Rendering Parameters\Data\HideProductItemsQuantity.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Product Items Rendering Parameters\Data\HideProductTypeIcon.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Product Items Rendering Parameters\Data\HideRegularPrice.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Product Items Rendering Parameters\Data\Product Items Data Source.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Product Items Rendering Parameters\Data\Read Only.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Product Items Rendering Parameters\Data\Show Cart Header.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Product Items Rendering Parameters\ExperienceTest.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Product Items Rendering Parameters\ExperienceTest\isABTestEnabled.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Product Items Rendering Parameters\SingleMembership.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Rendering Parameters\Product Items Rendering Parameters\SingleMembership\Search Timeout After Key Press in Milliseconds.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Settings.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ExcludeItemFrom></ExcludeItemFrom><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Settings\Checkout Settings.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ExcludeItemFrom></ExcludeItemFrom><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Settings\Feature Switch.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ExcludeItemFrom></ExcludeItemFrom><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Settings\Feature Switch\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ExcludeItemFrom></ExcludeItemFrom><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Settings\Feature Switch\Switch.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ExcludeItemFrom></ExcludeItemFrom><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Checkout\Settings\Feature Switch\Switch\ShouldUpdateGetCartCache.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ExcludeItemFrom></ExcludeItemFrom><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
  </ItemGroup>
  <ItemGroup>
    <CodeGenTemplate Include="..\..\..\..\t4templates\glass\GeneralExtensions.tt"><Link>Code Generation Templates\GeneralExtensions.tt</Link>
    </CodeGenTemplate><CodeGenTemplate Include="..\..\..\..\t4templates\glass\GlassV5Header.tt"><Link>Code Generation Templates\GlassV5Header.tt</Link>
    </CodeGenTemplate><CodeGenTemplate Include="..\..\..\..\t4templates\glass\GlassV5Item.tt"><Link>Code Generation Templates\GlassV5Item.tt</Link>
    </CodeGenTemplate><CodeGenTemplate Include="..\..\..\..\t4templates\glass\Helpers.tt"><Link>Code Generation Templates\Helpers.tt</Link>
    </CodeGenTemplate><CodeGenTemplate Include="..\..\..\..\t4templates\glass\Inflector.tt"><Link>Code Generation Templates\Inflector.tt</Link>
    </CodeGenTemplate><CodeGenTemplate Include="..\..\..\..\t4templates\glass\StringExtensions.tt"><Link>Code Generation Templates\StringExtensions.tt</Link>
    </CodeGenTemplate></ItemGroup>
  <ItemGroup>
    <Content Include="packages.config" />
  </ItemGroup>
  <Import Project="..\..\..\..\packages\HedgehogDevelopment.TDS.6.0.0.34\build\HedgehogDevelopment.TDS.targets" Condition="Exists('..\..\..\..\packages\HedgehogDevelopment.TDS.6.0.0.34\build\HedgehogDevelopment.TDS.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\..\..\..\packages\HedgehogDevelopment.TDS.6.0.0.34\build\HedgehogDevelopment.TDS.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\..\..\packages\HedgehogDevelopment.TDS.6.0.0.34\build\HedgehogDevelopment.TDS.targets'))" />
  </Target>
</Project>